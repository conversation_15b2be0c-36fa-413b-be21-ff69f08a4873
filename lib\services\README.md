# Services Directory Structure

This directory contains all services organized by functionality for better maintainability and easier navigation.

## 📁 Directory Structure

```
lib/services/
├── 📁 api/                    # API-related services
│   ├── 📁 features/           # Feature-specific API services
│   │   ├── dashboard_service.dart
│   │   ├── earnings_service.dart
│   │   ├── cash_balance_service.dart
│   │   ├── refer_earn_service.dart
│   │   ├── break_management_service.dart
│   │   └── shift_status_service.dart
│   ├── api_helper.dart        # HTTP client wrapper
│   ├── api_service.dart       # Main API service
│   ├── common_data_service.dart
│   └── index.dart             # API services exports
│
├── 📁 auth/                   # Authentication services
│   ├── authentication_service.dart
│   ├── profile_service.dart
│   └── index.dart
│
├── 📁 storage/                # Data storage services
│   ├── secure_storage_service.dart
│   └── index.dart
│
├── 📁 ui/                     # UI-related services
│   ├── themes.dart
│   ├── app_text_theme.dart
│   ├── app_strings.dart
│   ├── localization_service.dart
│   └── index.dart
│
├── 📁 location/               # Location services
│   ├── maps_service.dart
│   └── index.dart
│
├── 📁 utils/                  # Utility services
│   ├── environment_service.dart
│   ├── biometric_service.dart
│   ├── bank_verification_service.dart
│   ├── ifsc_verification_service.dart
│   ├── whatsapp_otp_service.dart
│   ├── rider_details_service.dart
│   └── index.dart
│
├── 📁 api_service/            # Legacy API service (backward compatibility)
│   ├── api_service.dart
│   └── index.dart
│
└── index.dart                 # Main services export
```

## 🚀 Usage

### Import All Services (Recommended)
```dart
import 'package:kisankonnect_rider/services/all_services.dart';
```

### Alternative Import (Backward Compatibility)
```dart
import 'package:kisankonnect_rider/services/index.dart';
```

### Individual Service Imports (Not Recommended)
```dart
// Individual service imports (use only if needed)
import 'package:kisankonnect_rider/services/auth/authentication_service.dart';
import 'package:kisankonnect_rider/services/storage/secure_storage_service.dart';
import 'package:kisankonnect_rider/services/ui/themes.dart';
```

## 📋 Service Categories

### 🔌 API Services (`/api/`)
- **api_helper.dart**: HTTP client wrapper with error handling
- **api_service.dart**: Main API service that provides access to all endpoints
- **common_data_service.dart**: Common data fetching service
- **features/**: Feature-specific API services for better organization

### 🔐 Authentication Services (`/auth/`)
- **authentication_service.dart**: Login, OTP, logout functionality
- **profile_service.dart**: User profile management

### 💾 Storage Services (`/storage/`)
- **secure_storage_service.dart**: Centralized storage service using SharedPreferences

### 🎨 UI Services (`/ui/`)
- **themes.dart**: App themes and colors
- **app_text_theme.dart**: Text styles and typography
- **app_strings.dart**: Static strings and constants
- **localization_service.dart**: Internationalization support

### 📍 Location Services (`/location/`)
- **maps_service.dart**: Google Maps integration and location services

### 🛠️ Utility Services (`/utils/`)
- **environment_service.dart**: Environment configuration
- **biometric_service.dart**: Biometric authentication
- **bank_verification_service.dart**: Bank account verification
- **ifsc_verification_service.dart**: IFSC code validation
- **whatsapp_otp_service.dart**: WhatsApp OTP integration
- **rider_details_service.dart**: Rider profile data management

## 🔄 Migration Guide

### Old Import (Deprecated)
```dart
import 'package:kisankonnect_rider/services/api_service/index.dart';
import 'package:kisankonnect_rider/services/secure_storage_service.dart';
import 'package:kisankonnect_rider/services/themes.dart';
// ... multiple imports
```

### New Import (Recommended)
```dart
import 'package:kisankonnect_rider/services/all_services.dart';
```

### Benefits of Single Import
- ✅ **One import for everything**: No need to remember multiple import paths
- ✅ **Cleaner code**: Fewer import statements
- ✅ **Better maintainability**: Changes to service locations don't break imports
- ✅ **Faster development**: No need to search for correct import paths

## ✅ Benefits

1. **🗂️ Better Organization**: Services grouped by functionality
2. **🔍 Easy Navigation**: Clear directory structure
3. **📦 Modular Imports**: Import only what you need
4. **🔄 Backward Compatibility**: Legacy imports still work
5. **📚 Better Documentation**: Clear service categorization
6. **🚀 Improved Maintainability**: Easier to find and update services
