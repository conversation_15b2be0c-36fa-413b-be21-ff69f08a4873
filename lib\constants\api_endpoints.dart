import '../config/env_config.dart';

/// Centralized API endpoints constants
/// All API endpoints should be defined here for easy management
class ApiEndpoints {
  // Private constructor to prevent instantiation
  ApiEndpoints._();

  // Base URLs
  static String get baseUrl => EnvConfig.fullApiBaseUrl;
  static String get riderBaseUrl => EnvConfig.riderBaseUrl;
  static String get documentsBaseUrl => EnvConfig.documentsBaseUrl;

  // ============================================================================
  // AUTHENTICATION ENDPOINTS
  // ============================================================================

  /// Rider login endpoint
  static String get riderLogin => '/Rider/FE_RidersLoginNewV1';

  /// WhatsApp OTP endpoint
  static String get sendWhatsAppOtp => '/Rider/FE_SendWhatsAppOTP';

  // ============================================================================
  // PROFILE & REGISTRATION ENDPOINTS
  // ============================================================================

  /// Get rider details
  static String get getRiderDetails => '/Rider/FE_GetRiderDetails';

  /// Get user info after login
  static String get userInfoAfterLogin => '/Rider/FE_SRInfoAfterLogin';

  /// Register rider profile
  static String get registerProfile => '/Rider/IN_RiderRegistration';

  /// Register rider documents
  static String get registerDocuments => '/Rider/RiderRegisterationInsertDocument';

  /// Register rider work details
  static String get registerWorkDetails => '/Rider/RiderRegisterationInsertWork';

  /// Common data for registration
  static String get commonData => '/Rider/FE_LoginCommonAPI';

  // ============================================================================
  // DASHBOARD ENDPOINTS
  // ============================================================================

  /// Dashboard story banner
  static String get dashboardStoryBanner => '/Rider/FE_DashboardStoryBanner';

  /// Current rider progress
  static String get currentRiderProgress => '/Rider/FE_CurrentRiderProgressV1';

  // ============================================================================
  // EARNINGS ENDPOINTS
  // ============================================================================

  /// Weekly earnings data
  static String get weeklyEarnings => '/Rider/FE_RiderEarining_WeeklyNew';

  /// Cash balance/COD limit
  static String get cashBalance => '/Rider/FE_KFHRiderCODLimit';

  // ============================================================================
  // REPORTS ENDPOINTS
  // ============================================================================

  /// Service level report
  static String get serviceLevelReport => '/Rider/FE_KFHRiderServiceLevelReport';

  // ============================================================================
  // RIDER STATUS ENDPOINTS
  // ============================================================================

  /// Current rider status
  static String get currentRiderStatus => '/Rider/FE_CurrentRiderStatus';

  /// Rider available status
  static String get riderAvailableStatus => '/Rider/IN_KHFRiderAvailableStatus';

  /// Rider break time
  static String get riderBreakTime => '/Rider/IN_RiderBreakTime';

  // ============================================================================
  // REFER & EARN ENDPOINTS
  // ============================================================================

  /// Refer and earn banner
  static String get referEarnBanner => '/Rider/FE_RefferealAndEarnBanner';

  // ============================================================================
  // DOCUMENT URLs
  // ============================================================================

  /// Terms and conditions URL
  static String get termsAndConditions => EnvConfig.termsConditionsUrl;

  /// Privacy policy URL
  static String get privacyPolicy => EnvConfig.privacyPolicyUrl;

  // ============================================================================
  // EXTERNAL SERVICE URLs
  // ============================================================================

  /// IFSC verification (Razorpay)
  static String get ifscVerification => EnvConfig.ifscApiUrl;

  /// Twilio WhatsApp API
  static String get twilioApi => EnvConfig.twilioApiUrl;

  /// MessageBird API
  static String get messageBirdApi => EnvConfig.messageBirdApiUrl;

  /// Gupshup API
  static String get gupshupApi => EnvConfig.gupshupApiUrl;

  // ============================================================================
  // HELPER METHODS
  // ============================================================================

  /// Get full URL for a rider endpoint
  static String getRiderUrl(String endpoint) {
    return '$riderBaseUrl$endpoint';
  }

  /// Get full URL for a base endpoint
  static String getBaseUrl(String endpoint) {
    return '$baseUrl$endpoint';
  }

  /// Get IFSC verification URL
  static String getIfscUrl(String ifscCode) {
    return '$ifscVerification/$ifscCode';
  }

  // ============================================================================
  // ENDPOINT VALIDATION
  // ============================================================================

  /// Validate if endpoint starts with correct path
  static bool isValidRiderEndpoint(String endpoint) {
    return endpoint.startsWith('/Rider/');
  }

  /// Get all rider endpoints for debugging
  static List<String> getAllRiderEndpoints() {
    return [
      riderLogin,
      sendWhatsAppOtp,
      getRiderDetails,
      userInfoAfterLogin,
      registerProfile,
      registerDocuments,
      registerWorkDetails,
      commonData,
      dashboardStoryBanner,
      currentRiderProgress,
      weeklyEarnings,
      cashBalance,
      serviceLevelReport,
      currentRiderStatus,
      riderAvailableStatus,
      riderBreakTime,
      referEarnBanner,
    ];
  }

  /// Get all external service URLs for debugging
  static Map<String, String> getAllExternalUrls() {
    return {
      'ifsc': ifscVerification,
      'twilio': twilioApi,
      'messageBird': messageBirdApi,
      'gupshup': gupshupApi,
      'termsAndConditions': termsAndConditions,
      'privacyPolicy': privacyPolicy,
    };
  }
}
