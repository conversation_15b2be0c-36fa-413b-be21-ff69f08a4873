#!/bin/bash

# <PERSON>ript to fix import errors after reorganizing services directory
echo "Fixing import errors after services directory reorganization..."

# Fix imports for api_helper.dart
find lib -type f -name "*.dart" -not -path "*/services/*" -exec sed -i 's|import.*services/api_helper.dart|import '\''package:kisankonnect_rider/services/api/api_helper.dart'\''|g' {} \;

# Fix imports for secure_storage_service.dart
find lib -type f -name "*.dart" -not -path "*/services/*" -exec sed -i 's|import.*services/secure_storage_service.dart|import '\''package:kisankonnect_rider/services/storage/secure_storage_service.dart'\''|g' {} \;

# Fix imports for authentication_service.dart
find lib -type f -name "*.dart" -not -path "*/services/*" -exec sed -i 's|import.*services/api_service/authentication_service.dart|import '\''package:kisankonnect_rider/services/auth/authentication_service.dart'\''|g' {} \;

# Fix imports for profile_service.dart
find lib -type f -name "*.dart" -not -path "*/services/*" -exec sed -i 's|import.*services/api_service/profile_service.dart|import '\''package:kisankonnect_rider/services/auth/profile_service.dart'\''|g' {} \;

# Fix imports for dashboard_service.dart
find lib -type f -name "*.dart" -not -path "*/services/*" -exec sed -i 's|import.*services/api_service/dashboard_service.dart|import '\''package:kisankonnect_rider/services/api/features/dashboard_service.dart'\''|g' {} \;

# Fix imports for earnings_service.dart
find lib -type f -name "*.dart" -not -path "*/services/*" -exec sed -i 's|import.*services/api_service/earnings_service.dart|import '\''package:kisankonnect_rider/services/api/features/earnings_service.dart'\''|g' {} \;

# Fix imports for cash_balance_service.dart
find lib -type f -name "*.dart" -not -path "*/services/*" -exec sed -i 's|import.*services/api_service/cash_balance_service.dart|import '\''package:kisankonnect_rider/services/api/features/cash_balance_service.dart'\''|g' {} \;

# Fix imports for refer_earn_service.dart
find lib -type f -name "*.dart" -not -path "*/services/*" -exec sed -i 's|import.*services/api_service/refer_earn_service.dart|import '\''package:kisankonnect_rider/services/api/features/refer_earn_service.dart'\''|g' {} \;

# Fix imports for break_management_service.dart
find lib -type f -name "*.dart" -not -path "*/services/*" -exec sed -i 's|import.*services/api_service/break_management_service.dart|import '\''package:kisankonnect_rider/services/api/features/break_management_service.dart'\''|g' {} \;

# Fix imports for shift_status_service.dart
find lib -type f -name "*.dart" -not -path "*/services/*" -exec sed -i 's|import.*services/api_service/shift_status_service.dart|import '\''package:kisankonnect_rider/services/api/features/shift_status_service.dart'\''|g' {} \;

# Fix imports for common_data_service.dart
find lib -type f -name "*.dart" -not -path "*/services/*" -exec sed -i 's|import.*services/api_service/common_data_service.dart|import '\''package:kisankonnect_rider/services/api/common_data_service.dart'\''|g' {} \;

# Fix imports for environment_service.dart
find lib -type f -name "*.dart" -not -path "*/services/*" -exec sed -i 's|import.*services/environment_service.dart|import '\''package:kisankonnect_rider/services/utils/environment_service.dart'\''|g' {} \;

# Fix imports for biometric_service.dart
find lib -type f -name "*.dart" -not -path "*/services/*" -exec sed -i 's|import.*services/biometric_service.dart|import '\''package:kisankonnect_rider/services/utils/biometric_service.dart'\''|g' {} \;

# Fix imports for bank_verification_service.dart
find lib -type f -name "*.dart" -not -path "*/services/*" -exec sed -i 's|import.*services/bank_verification_service.dart|import '\''package:kisankonnect_rider/services/utils/bank_verification_service.dart'\''|g' {} \;

# Fix imports for ifsc_verification_service.dart
find lib -type f -name "*.dart" -not -path "*/services/*" -exec sed -i 's|import.*services/ifsc_verification_service.dart|import '\''package:kisankonnect_rider/services/utils/ifsc_verification_service.dart'\''|g' {} \;

# Fix imports for whatsapp_otp_service.dart
find lib -type f -name "*.dart" -not -path "*/services/*" -exec sed -i 's|import.*services/whatsapp_otp_service.dart|import '\''package:kisankonnect_rider/services/utils/whatsapp_otp_service.dart'\''|g' {} \;

# Fix imports for rider_details_service.dart
find lib -type f -name "*.dart" -not -path "*/services/*" -exec sed -i 's|import.*services/rider_details_service.dart|import '\''package:kisankonnect_rider/services/utils/rider_details_service.dart'\''|g' {} \;

# Fix imports for maps_service.dart
find lib -type f -name "*.dart" -not -path "*/services/*" -exec sed -i 's|import.*services/maps_service.dart|import '\''package:kisankonnect_rider/services/location/maps_service.dart'\''|g' {} \;

# Fix imports for themes.dart
find lib -type f -name "*.dart" -not -path "*/services/*" -exec sed -i 's|import.*services/themes.dart|import '\''package:kisankonnect_rider/services/ui/themes.dart'\''|g' {} \;

# Fix imports for app_text_theme.dart
find lib -type f -name "*.dart" -not -path "*/services/*" -exec sed -i 's|import.*services/app_text_theme.dart|import '\''package:kisankonnect_rider/services/ui/app_text_theme.dart'\''|g' {} \;

# Fix imports for app_strings.dart
find lib -type f -name "*.dart" -not -path "*/services/*" -exec sed -i 's|import.*services/app_strings.dart|import '\''package:kisankonnect_rider/services/ui/app_strings.dart'\''|g' {} \;

# Fix imports for localization_service.dart
find lib -type f -name "*.dart" -not -path "*/services/*" -exec sed -i 's|import.*services/localization_service.dart|import '\''package:kisankonnect_rider/services/ui/localization_service.dart'\''|g' {} \;

# Fix imports for api_service/index.dart
find lib -type f -name "*.dart" -not -path "*/services/*" -exec sed -i 's|import.*services/api_service/index.dart|import '\''package:kisankonnect_rider/services/index.dart'\''|g' {} \;

echo "Import errors fixed successfully!"
