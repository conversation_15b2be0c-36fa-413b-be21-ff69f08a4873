import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

class GoogleMapsTestScreen extends StatefulWidget {
  const GoogleMapsTestScreen({super.key});

  @override
  State<GoogleMapsTestScreen> createState() => _GoogleMapsTestScreenState();
}

class _GoogleMapsTestScreenState extends State<GoogleMapsTestScreen> {
  GoogleMapController? _mapController;
  final LatLng _initialPosition = const LatLng(19.0760, 72.8777); // Mumbai
  final Set<Marker> _markers = {};
  bool _isMapCreated = false;
  String _statusMessage = 'Initializing map...';

  @override
  void initState() {
    super.initState();
    _validateGoogleMapsSetup();
  }

  void _validateGoogleMapsSetup() {
    final mapsService = MapsService.instance;
    debugPrint('🗺️ === Google Maps API Key Validation ===');
    debugPrint('🗺️ API Key from MapsService: ${mapsService.apiKey.substring(0, 8)}...');
    debugPrint('🗺️ Is API Key Valid: ${mapsService.isApiKeyValid}');
    debugPrint('🗺️ =====================================');

    setState(() {
      _statusMessage = 'API Key Valid: ${mapsService.isApiKeyValid}';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Google Maps Test'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              setState(() {
                _isMapCreated = false;
                _statusMessage = 'Reloading map...';
              });
              Future.delayed(const Duration(milliseconds: 500), () {
                setState(() {
                  _isMapCreated = true;
                });
              });
            },
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              _statusMessage,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: _isMapCreated
                ? GoogleMap(
                    onMapCreated: (GoogleMapController controller) {
                      _mapController = controller;
                      setState(() {
                        _statusMessage = 'Map created successfully!';
                        _addMarker(_initialPosition);
                      });
                      debugPrint('🗺️ Google Map created successfully');
                    },
                    initialCameraPosition: CameraPosition(
                      target: _initialPosition,
                      zoom: 14.0,
                    ),
                    markers: _markers,
                    myLocationEnabled: true,
                    myLocationButtonEnabled: true,
                    onTap: _addMarker,
                    onCameraMove: (position) {
                      debugPrint('🗺️ Camera moved to: ${position.target}');
                    },
                  )
                : Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const CircularProgressIndicator(),
                        const SizedBox(height: 16),
                        Text('API Key: ${MapsService.instance.apiKey.substring(0, 8)}...'),
                        const SizedBox(height: 8),
                        ElevatedButton(
                          onPressed: () {
                            setState(() {
                              _isMapCreated = true;
                            });
                          },
                          child: const Text('Load Map'),
                        ),
                      ],
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  void _addMarker(LatLng position) {
    setState(() {
      _markers.clear();
      _markers.add(
        Marker(
          markerId: const MarkerId('selected_location'),
          position: position,
          infoWindow: InfoWindow(
            title: 'Selected Location',
            snippet: '${position.latitude}, ${position.longitude}',
          ),
        ),
      );
      _statusMessage = 'Marker added at: ${position.latitude}, ${position.longitude}';
    });
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }
}
