import '../auth/authentication_service.dart';
import '../auth/profile_service.dart';
import '../api/features/shift_status_service.dart';
import '../api/features/break_management_service.dart';
import '../api/features/dashboard_service.dart';
import '../api/features/earnings_service.dart';
import '../api/features/cash_balance_service.dart';

/// Main API Service - Single point of access to all API services
class ApiService {
  static ApiService? _instance;
  static ApiService get instance => _instance ??= ApiService._();

  ApiService._();

  // Authentication
  AuthenticationService get auth => AuthenticationService.instance;

  // Profile Management
  ProfileService get profile => ProfileService.instance;

  // Shift Management
  ShiftStatusService get shift => ShiftStatusService.instance;
  BreakManagementService get breaks => BreakManagementService.instance;

  // Dashboard & Data
  DashboardService get dashboard => DashboardService.instance;
  EarningsService get earnings => EarningsService.instance;
  CashBalanceService get cashBalance => CashBalanceService.instance;
}
