import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

import 'package:kisankonnect_rider/utils/responsive_utils.dart';
import 'package:kisankonnect_rider/l10n/app_localizations.dart';
import 'package:kisankonnect_rider/controllers/earnings_controller.dart';
import 'package:kisankonnect_rider/models/earnings_models.dart';

class EarningsSummarySection extends StatefulWidget {
  const EarningsSummarySection({super.key});

  @override
  State<EarningsSummarySection> createState() => _EarningsSummarySectionState();
}

class _EarningsSummarySectionState extends State<EarningsSummarySection> {
  late final EarningsController _earningsController;

  @override
  void initState() {
    super.initState();
    _earningsController = Get.find<EarningsController>();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title outside container
            Padding(
              padding: EdgeInsets.symmetric(horizontal: ResponsiveUtils.spacingM(context)),
              child: Text(
                AppLocalizations.of(context)!.myEarnings,
                style: AppTextTheme.cardTitle,
              ),
            ),

            const SizedBox(height: 8), // Reduced spacing

            // White container
            Container(
              margin: EdgeInsets.symmetric(horizontal: ResponsiveUtils.spacingM(context)),
              padding: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.shadowLight,
                    blurRadius: ResponsiveUtils.width(context, 1),
                    offset: Offset(0, ResponsiveUtils.height(context, 0.2)),
                  ),
                ],
              ),
              child: _earningsController.isLoading
                  ? _buildLoadingState(context)
                  : _earningsController.error.isNotEmpty
                      ? _buildErrorState(context)
                      : _buildEarningsContent(context),
            ),
          ],
        ));
  }

  Widget _buildLoadingState(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: ResponsiveUtils.height(context, 5)),
        const CircularProgressIndicator(),
        SizedBox(height: ResponsiveUtils.height(context, 2)),
        Text(
          'Loading earnings data...',
          style: AppTextTheme.cardSubtitle,
        ),
        SizedBox(height: ResponsiveUtils.height(context, 5)),
      ],
    );
  }

  Widget _buildErrorState(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: ResponsiveUtils.height(context, 3)),
        Icon(
          Icons.error_outline,
          color: Colors.red,
          size: ResponsiveUtils.iconSize(context, IconSizeType.large),
        ),
        SizedBox(height: ResponsiveUtils.height(context, 2)),
        Text(
          _earningsController.error,
          style: AppTextTheme.cardSubtitle.copyWith(color: Colors.red),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: ResponsiveUtils.height(context, 2)),
        ElevatedButton(
          onPressed: () => _earningsController.refreshEarnings(),
          child: const Text('Retry'),
        ),
        SizedBox(height: ResponsiveUtils.height(context, 3)),
      ],
    );
  }

  Widget _buildEarningsContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Tab Buttons
        Row(
          children: [
            _buildTabButton(AppLocalizations.of(context)!.today, EarningsPeriod.today),
            SizedBox(width: ResponsiveUtils.spacingS(context)),
            _buildTabButton(AppLocalizations.of(context)!.thisWeek, EarningsPeriod.thisWeek),
            SizedBox(width: ResponsiveUtils.spacingS(context)),
            _buildTabButton(AppLocalizations.of(context)!.thisMonth, EarningsPeriod.thisMonth),
          ],
        ),

        SizedBox(height: ResponsiveUtils.height(context, 2.5)),

        // Total earnings section
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              _earningsController.earningsSummaryText,
              style: AppTextTheme.cardSubtitle.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              _earningsController.formatCurrency(_earningsController.totalEarnings),
              style: AppTextTheme.cardTitle,
            ),
          ],
        ),

        SizedBox(height: ResponsiveUtils.height(context, 0.5)),

        // Previous period earnings
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              _earningsController.previousEarningsText,
              style: AppTextTheme.cardCaption,
            ),
            Text(
              _earningsController.formatCurrency(_earningsController.previousPeriodEarnings),
              style: AppTextTheme.cardCaption,
            ),
          ],
        ),

        SizedBox(height: ResponsiveUtils.height(context, 2.5)),

        // Earnings breakdown
        _buildEarningItem(
          icon: Icons.delivery_dining,
          iconColor: Colors.red,
          title: AppLocalizations.of(context)!.orderDelivered,
          value: _earningsController.formatOrdersCount(_earningsController.totalOrders),
        ),

        SizedBox(height: ResponsiveUtils.height(context, 1.5)),

        _buildEarningItem(
          icon: Icons.currency_rupee,
          iconColor: Colors.orange,
          title: AppLocalizations.of(context)!.orderEarning,
          value: _earningsController.formatCurrency(_earningsController.orderEarnings),
        ),

        SizedBox(height: ResponsiveUtils.height(context, 1.5)),

        _buildEarningItem(
          icon: Icons.umbrella,
          iconColor: Colors.blue,
          title: AppLocalizations.of(context)!.rainSurgeEarning,
          value: _earningsController.formatCurrency(_earningsController.rainSurgeEarnings),
        ),

        SizedBox(height: ResponsiveUtils.height(context, 1.5)),

        _buildEarningItem(
          icon: Icons.star,
          iconColor: Colors.cyan,
          title: AppLocalizations.of(context)!.totalIncentive,
          subtitle: AppLocalizations.of(context)!.incentiveSubtitle,
          value: _earningsController.formatCurrency(_earningsController.totalIncentives),
          hasDropdown: true,
        ),

        SizedBox(height: ResponsiveUtils.height(context, 2)),

        // Incentive banner
        Container(
          padding: EdgeInsets.all(ResponsiveUtils.spacingS(context)),
          decoration: BoxDecoration(
            color: Colors.orange.shade50,
            borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.small)),
            border: Border.all(color: Colors.orange.shade200),
          ),
          child: Row(
            children: [
              Icon(
                Icons.emoji_events,
                color: Colors.orange,
                size: ResponsiveUtils.iconSize(context, IconSizeType.medium),
              ),
              SizedBox(width: ResponsiveUtils.spacingS(context)),
              Expanded(
                child: Text(
                  AppLocalizations.of(context)!.doMoreOrdersAndEarn,
                  style: AppTextTheme.cardSubtitle.copyWith(
                    fontWeight: FontWeight.w500,
                    color: Colors.orange.shade700,
                  ),
                ),
              ),
              Text(
                _earningsController.potentialEarningsText,
                style: AppTextTheme.cardTitle.copyWith(
                  fontWeight: FontWeight.w700,
                  color: Colors.orange.shade700,
                ),
              ),
            ],
          ),
        ),

        SizedBox(height: ResponsiveUtils.height(context, 2)),

        // View all earnings button - aligned to right
        Align(
          alignment: Alignment.centerRight,
          child: OutlinedButton.icon(
            onPressed: () {},
            style: OutlinedButton.styleFrom(
              side: BorderSide(color: AppColors.green, width: 1),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.large)),
              ),
              padding: EdgeInsets.symmetric(
                horizontal: ResponsiveUtils.spacingM(context),
                vertical: ResponsiveUtils.spacingS(context),
              ),
              minimumSize: Size.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            icon: Icon(
              Icons.arrow_forward,
              size: ResponsiveUtils.iconSize(context, IconSizeType.small),
              color: AppColors.green,
            ),
            label: Text(
              AppLocalizations.of(context)!.viewAllEarningsAndIncentives,
              style: AppTextTheme.cardSubtitle.copyWith(
                color: AppColors.green,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTabButton(String title, EarningsPeriod period) {
    bool isSelected = _earningsController.selectedPeriod == period;
    return GestureDetector(
      onTap: () {
        _earningsController.changePeriod(period);
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: ResponsiveUtils.spacingM(context),
          vertical: ResponsiveUtils.spacingS(context),
        ),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.green.withValues(alpha: 0.9) : Colors.transparent,
          borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.large)),
          border: Border.all(
            color: isSelected ? AppColors.green.withValues(alpha: 0.9) : AppColors.borderLight.withValues(alpha: 0.7),
            width: 1,
          ),
        ),
        child: Text(
          title,
          style: AppTextTheme.cardSubtitle.copyWith(
            color: isSelected ? Colors.white : AppColors.textSecondary.withValues(alpha: 0.8),
          ),
        ),
      ),
    );
  }

  Widget _buildEarningItem({
    required IconData icon,
    required Color iconColor,
    required String title,
    String? subtitle,
    required String value,
    bool hasDropdown = false,
  }) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(ResponsiveUtils.spacingXS(context)),
          decoration: BoxDecoration(
            color: iconColor.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: iconColor,
            size: ResponsiveUtils.iconSize(context, IconSizeType.medium),
          ),
        ),
        SizedBox(width: ResponsiveUtils.spacingS(context)),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTextTheme.cardSubtitle,
              ),
              if (subtitle != null)
                Text(
                  subtitle,
                  style: AppTextTheme.cardCaption,
                ),
            ],
          ),
        ),
        Row(
          children: [
            Text(
              value,
              style: AppTextTheme.cardSubtitle.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            if (hasDropdown) ...[
              SizedBox(width: ResponsiveUtils.spacingXS(context)),
              Icon(
                Icons.keyboard_arrow_up,
                color: AppColors.textSecondary,
                size: ResponsiveUtils.iconSize(context, IconSizeType.small),
              ),
            ],
          ],
        ),
      ],
    );
  }
}
