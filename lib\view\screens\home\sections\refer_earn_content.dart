import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

import 'package:kisankonnect_rider/l10n/app_localizations.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/view/widgets/common/common_app_bar.dart';
import 'package:kisankonnect_rider/controllers/refer_earn_controller.dart';

import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

class ReferEarnScreen extends StatelessWidget {
  const ReferEarnScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ReferEarnController>(
      init: ReferEarnController(),
      builder: (controller) {
        return Scaffold(
          backgroundColor: const Color(0xFFF7F8FA),
          appBar: CommonAppBar(titleKey: "Refer & Earn"),
          body: RefreshIndicator(
            onRefresh: () => controller.refreshReferEarnData(),
            child: controller.error.isNotEmpty
                ? _buildErrorState(context, controller)
                : _buildReferEarnContent(context, controller),
          ),
        );
      },
    );
  }

  Widget _buildErrorState(BuildContext context, ReferEarnController controller) {
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      child: SizedBox(
        height: MediaQuery.of(context).size.height - 200,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.grey.shade400,
              ),
              const SizedBox(height: 16),
              Text(
                controller.error,
                style: AppTextTheme.cardSubtitle,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => controller.refreshReferEarnData(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.green,
                ),
                child: const Text('Retry', style: TextStyle(color: Colors.white)),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildReferEarnContent(BuildContext context, ReferEarnController controller) {
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Main Referral Banner
          Container(
            width: double.infinity,
            decoration: controller.bannerImageUrl.isNotEmpty
                ? BoxDecoration(
                    image: DecorationImage(
                      image: NetworkImage(controller.bannerImageUrl),
                      fit: BoxFit.cover,
                    ),
                  )
                : const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Color(0xFF2E7D32),
                        Color(0xFF4CAF50),
                      ],
                    ),
                  ),
            child: Container(
              decoration: controller.bannerImageUrl.isNotEmpty
                  ? BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.black.withValues(alpha: 0.6),
                          Colors.black.withValues(alpha: 0.4),
                        ],
                      ),
                    )
                  : null,
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              AppLocalizations.of(context)!.referFriendAndEarn,
                              style: AppTextTheme.cardTitle.copyWith(
                                color: Colors.white,
                                fontSize: 20,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              '₹${controller.referralAmount}',
                              style: AppTextTheme.priceLarge.copyWith(
                                color: const Color(0xFFFFD700),
                                fontSize: 32,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.2),
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.add,
                                color: Colors.white,
                                size: 24,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              AppLocalizations.of(context)!.yourFriendGets,
                              style: AppTextTheme.cardSubtitle.copyWith(
                                color: Colors.white,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(height: 4),
                            RichText(
                              text: TextSpan(
                                children: [
                                  TextSpan(
                                    text: '₹${controller.friendAmount}',
                                    style: AppTextTheme.priceLarge.copyWith(
                                      color: const Color(0xFFFFD700),
                                      fontSize: 24,
                                    ),
                                  ),
                                  TextSpan(
                                    text: AppLocalizations.of(context)!.onJoining,
                                    style: AppTextTheme.cardSubtitle.copyWith(
                                      color: Colors.white,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Gift Box Icon
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Stack(
                          children: [
                            Center(
                              child: Container(
                                width: 50,
                                height: 50,
                                decoration: BoxDecoration(
                                  gradient: const LinearGradient(
                                    colors: [Color(0xFFFFD700), Color(0xFFFFA000)],
                                  ),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: const Icon(
                                  Icons.card_giftcard,
                                  color: Colors.white,
                                  size: 30,
                                ),
                              ),
                            ),
                            // Sparkle effects
                            Positioned(
                              top: 10,
                              right: 15,
                              child: Icon(
                                Icons.star,
                                color: Colors.white.withValues(alpha: 0.8),
                                size: 12,
                              ),
                            ),
                            Positioned(
                              bottom: 15,
                              left: 10,
                              child: Icon(
                                Icons.star,
                                color: Colors.white.withValues(alpha: 0.6),
                                size: 8,
                              ),
                            ),
                            Positioned(
                              top: 20,
                              left: 5,
                              child: Icon(
                                Icons.star,
                                color: Colors.white.withValues(alpha: 0.7),
                                size: 10,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Total Referral Earnings Card
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  spreadRadius: 1,
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppLocalizations.of(context)!.totalReferralEarnings,
                  style: AppTextTheme.cardTitle,
                ),
                const SizedBox(height: 8),
                Text(
                  '₹${controller.totalEarnings}',
                  style: AppTextTheme.priceLarge.copyWith(
                    color: AppColors.green,
                    fontSize: 24,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${controller.totalReferrals} ${AppLocalizations.of(context)!.friendsReferred}',
                  style: AppTextTheme.cardSubtitle,
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Share Referral Code Section
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppLocalizations.of(context)!.shareYourReferralCode,
                  style: AppTextTheme.cardTitle,
                ),

                const SizedBox(height: 16),

                // Referral Code Container
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          controller.referralCode,
                          style: AppTextTheme.cardTitle.copyWith(
                            fontSize: 16,
                          ),
                        ),
                      ),
                      GestureDetector(
                        onTap: () => _copyToClipboard(context, controller),
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: const Icon(
                            Icons.copy,
                            size: 20,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // How it works Section
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppLocalizations.of(context)!.howItWorks,
                  style: AppTextTheme.cardTitle,
                ),
                const SizedBox(height: 8),
                Text(
                  AppLocalizations.of(context)!.referInSimpleSteps,
                  style: AppTextTheme.cardSubtitle,
                ),
                const SizedBox(height: 16),

                // Step 1
                _buildHowItWorksStep(
                  stepNumber: '1',
                  title: AppLocalizations.of(context)!.copyCodeOrShareViaWhatsapp,
                  backgroundColor: const Color(0xFFE8F5E8),
                ),

                const SizedBox(height: 12),

                // Step 2
                _buildHowItWorksStep(
                  stepNumber: '2',
                  title: AppLocalizations.of(context)!.completeTheTarget,
                  backgroundColor: const Color(0xFFE8F5E8),
                ),

                const SizedBox(height: 12),

                // Step 3
                _buildHowItWorksStep(
                  stepNumber: '3',
                  title: AppLocalizations.of(context)!.enjoyTheBonus,
                  backgroundColor: const Color(0xFFE8F5E8),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Your Referrals Section
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppLocalizations.of(context)!.yourReferrals,
                  style: AppTextTheme.cardTitle,
                ),

                const SizedBox(height: 16),

                // Referral List
                if (controller.referralList.isEmpty)
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.grey.shade200),
                    ),
                    child: Center(
                      child: Text(
                        'No referrals yet. Start referring friends!',
                        style: AppTextTheme.cardSubtitle,
                      ),
                    ),
                  )
                else
                  ...controller.referralList.map((referral) => Padding(
                        padding: const EdgeInsets.only(bottom: 12),
                        child: _buildReferralItem(
                          context: context,
                          name: referral.name,
                          date: referral.date,
                          status: referral.status,
                          amount: referral.amount,
                          statusColor: referral.statusColor,
                        ),
                      )),
              ],
            ),
          ),

          const SizedBox(height: 32),

          // Invite via WhatsApp Button
          Padding(
            padding: const EdgeInsets.all(16),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => _shareViaWhatsApp(context, controller),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.green,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(32),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: Text(
                  AppLocalizations.of(context)!.inviteViaWhatsApp,
                  style: AppTextTheme.buttonLarge,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReferralItem({
    required BuildContext context,
    required String name,
    required String date,
    required String status,
    required String amount,
    required Color statusColor,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          // Avatar
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.person,
              color: Colors.grey,
              size: 20,
            ),
          ),

          const SizedBox(width: 12),

          // Name and Date
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: AppTextTheme.cardTitle,
                ),
                const SizedBox(height: 4),
                Text(
                  date,
                  style: AppTextTheme.cardCaption,
                ),
              ],
            ),
          ),

          // Status and Amount
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              if (amount != '--')
                Text(
                  amount,
                  style: AppTextTheme.priceAmount,
                ),
              const SizedBox(height: 4),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: statusColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  status,
                  style: AppTextTheme.cardCaption.copyWith(
                    color: Colors.white,
                    fontSize: 10,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHowItWorksStep({
    required String stepNumber,
    required String title,
    required Color backgroundColor,
  }) {
    return Row(
      children: [
        // Step Number Circle
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: backgroundColor,
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              stepNumber,
              style: AppTextTheme.cardTitle.copyWith(
                color: AppColors.green,
              ),
            ),
          ),
        ),

        const SizedBox(width: 16),

        // Step Title
        Expanded(
          child: Text(
            title,
            style: AppTextTheme.cardSubtitle.copyWith(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  void _copyToClipboard(BuildContext context, ReferEarnController controller) {
    Clipboard.setData(ClipboardData(text: controller.referralCode));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(AppLocalizations.of(context)!.referralCodeCopied),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _shareViaWhatsApp(BuildContext context, ReferEarnController controller) async {
    try {
      // Construct complete message with msg1 + msg2 + referral code
      String completeMessage = '';

      if (controller.shareMessage.isNotEmpty) {
        completeMessage = controller.shareMessage;

        // Add msg2 (friend amount) if available
        if (controller.referEarnData?.referEarnData.msg2.isNotEmpty == true) {
          completeMessage += '\n\n💰 Your friend gets: ${controller.referEarnData!.referEarnData.msg2}';
        }

        // Ensure referral code is included
        if (!completeMessage.contains(controller.referralCode)) {
          completeMessage += '\n\n🎯 Referral Code: ${controller.referralCode}';
        }
      } else {
        // Fallback to localized message
        completeMessage = AppLocalizations.of(context)!.joinKisanKonnectMessage(controller.referralCode);
      }

      // Check if banner image is available for sharing
      final bannerImageUrl = controller.bannerImageUrl;

      if (bannerImageUrl.isNotEmpty) {
        // Share with image and text
        completeMessage += '\n\n🖼️ Check out this banner: $bannerImageUrl';
      }

      // Try to open WhatsApp directly
      final whatsappUrl = 'whatsapp://send?text=${Uri.encodeComponent(completeMessage)}';
      if (await canLaunchUrl(Uri.parse(whatsappUrl))) {
        await launchUrl(Uri.parse(whatsappUrl));
      } else {
        // Fallback to general sharing
        await Share.share(completeMessage);
      }
    } catch (e) {
      // Fallback with basic message
      final fallbackMessage = '🚀 Join KisanKonnect and earn more!\n\n🎯 Use referral code: ${controller.referralCode}';
      await Share.share(fallbackMessage);
    }
  }
}
