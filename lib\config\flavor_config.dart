import 'env_config.dart';

/// Available app flavors/environments
enum Flavor {
  development,
  production,
}

/// Flavor configuration extensions
extension FlavorExtension on Flavor {
  /// Get flavor name as string
  String get name {
    switch (this) {
      case Flavor.development:
        return 'development';
      case Flavor.production:
        return 'production';
    }
  }

  /// Get display name for UI
  String get displayName {
    switch (this) {
      case Flavor.development:
        return 'Development';
      case Flavor.production:
        return 'Production';
    }
  }

  /// Check if flavor is production
  bool get isProduction => this == Flavor.production;

  /// Check if flavor is development
  bool get isDevelopment => this == Flavor.development;

  /// Check if flavor allows debugging
  bool get allowsDebugging => this != Flavor.production;

  /// Check if flavor requires security features
  bool get requiresSecurity => this == Flavor.production;
}

/// Main flavor configuration class
class FlavorConfig {
  static Flavor? appFlavor;

  /// Get current flavor name
  static String get name => appFlavor?.name ?? 'unknown';

  /// Get current flavor display name
  static String get displayName => appFlavor?.displayName ?? 'Unknown';

  /// Get app title based on flavor
  static String get title {
    switch (appFlavor) {
      case Flavor.development:
        return 'KisanKonnect Rider Dev';
      case Flavor.production:
        return 'KisanKonnect Rider';
      case null:
        return 'KisanKonnect Rider';
    }
  }

  /// Convenience getters for flavor checks
  static bool get isDevelopment => appFlavor?.isDevelopment ?? false;
  static bool get isProduction => appFlavor?.isProduction ?? false;
  static bool get allowsDebugging => appFlavor?.allowsDebugging ?? true;
  static bool get requiresSecurity => appFlavor?.requiresSecurity ?? false;
}

/// Configuration values for each flavor
/// Now uses environment variables from .env files
class FlavorValues {
  FlavorValues();

  // All configuration now comes from EnvConfig
  // This class is kept for backward compatibility
}

/// Environment configuration accessor
/// Now uses EnvConfig for environment variables from .env files
class Environment {
  static bool _initialized = false;

  /// Initialize environment with flavor and load .env files
  static Future<void> init({required Flavor flavor}) async {
    if (_initialized) return;

    FlavorConfig.appFlavor = flavor;

    // Initialize EnvConfig with the selected flavor
    await EnvConfig.init(flavor: flavor);

    _initialized = true;
  }

  // Network Configuration
  static String get baseUrl => EnvConfig.apiBaseUrl;
  static String get socketUrl => EnvConfig.socketUrl;
  static String get apiVersion => EnvConfig.apiVersion;
  static Duration get requestTimeout => EnvConfig.requestTimeoutDuration;
  static int get maxRetries => EnvConfig.maxRetries;

  // Feature Flags
  static bool get enableLogging => EnvConfig.enableLogging;
  static bool get enableCrashlytics => EnvConfig.enableCrashlytics;
  static bool get enableAnalytics => EnvConfig.enableAnalytics;
  static bool get enablePerformanceMonitoring => EnvConfig.enablePerformanceMonitoring;
  static bool get enableDevicePreview => EnvConfig.enableDevicePreview;
  static bool get enableFlavorBanner => EnvConfig.enableFlavorBanner;

  // App Configuration
  static String get bundleId => EnvConfig.bundleId;
  static String get appName => EnvConfig.appName;
  static String get databaseName => EnvConfig.databaseName;
  static Duration get cacheTimeout => EnvConfig.cacheTimeout;
  static String get logLevel => EnvConfig.logLevel;

  // Computed URLs
  static String get apiBaseUrl => EnvConfig.fullApiBaseUrl;

  // API Endpoints - KisanKonnect specific
  static String get riderBaseUrl => EnvConfig.riderBaseUrl;
  static String get loginUrl => '$riderBaseUrl/FE_RidersLoginNew';
  static String get profileUrl => '$riderBaseUrl/profile';
  static String get ordersUrl => '$apiBaseUrl/orders';
  static String get earningsUrl => '$riderBaseUrl/FE_RiderEarining_WeeklyNew';
  static String get paymentsUrl => '$apiBaseUrl/payments';
  static String get walletUrl => '$apiBaseUrl/wallet';

  // Document URLs
  static String get documentsBaseUrl => EnvConfig.documentsBaseUrl;
  static String get termsAndConditionsUrl => EnvConfig.termsConditionsUrl;
  static String get privacyPolicyUrl => EnvConfig.privacyPolicyUrl;

  // Debug Information
  static Map<String, dynamic> get debugInfo => EnvConfig.environmentSummary;
}
