import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

/// Dialog for selecting time slots when Part Time is chosen
class TimeSlotSelectionDialog extends StatefulWidget {
  final Function(String)? onTimeSlotSelected;

  const TimeSlotSelectionDialog({
    super.key,
    this.onTimeSlotSelected,
  });

  @override
  State<TimeSlotSelectionDialog> createState() => _TimeSlotSelectionDialogState();
}

class _TimeSlotSelectionDialogState extends State<TimeSlotSelectionDialog> {
  String? _selectedTimeSlot;

  final List<Map<String, dynamic>> _timeSlots = [
    {
      'title': 'Morning',
      'time': '7 am - 12 pm',
      'earnings': 'Upto ₹5,000 weekly earnings',
      'icon': '🌅',
    },
    {
      'title': 'Afternoon',
      'time': '12 pm - 4 pm',
      'earnings': 'Upto ₹5,000 weekly earnings',
      'icon': '☀️',
    },
    {
      'title': 'Evening',
      'time': '4 pm - 8 pm',
      'earnings': 'Upto ₹5,000 weekly earnings',
      'icon': '🌆',
    },
    {
      'title': 'Evening',
      'time': '5 pm - 11 pm',
      'earnings': 'Upto ₹5,000 weekly earnings',
      'icon': '🌙',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.orange.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    '⏰',
                    style: TextStyle(fontSize: 20),
                  ),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'Select time slot',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close, color: Colors.grey),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Time slot options
            ...(_timeSlots.map((slot) => _buildTimeSlotOption(slot))),

            const SizedBox(height: 20),

            // Continue button
            SizedBox(
              width: double.infinity,
              height: 48,
              child: ElevatedButton(
                onPressed: _selectedTimeSlot != null
                    ? () {
                        Navigator.of(context).pop();
                        widget.onTimeSlotSelected?.call(_selectedTimeSlot!);
                      }
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _selectedTimeSlot != null ? AppColors.green : Colors.grey,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(24),
                  ),
                ),
                child: const Text(
                  'Continue',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeSlotOption(Map<String, dynamic> slot) {
    final String slotKey = '${slot['title']} ${slot['time']}';
    final bool isSelected = _selectedTimeSlot == slotKey;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedTimeSlot = slotKey;
          });
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.green.withValues(alpha: 0.1) : Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected ? AppColors.green : Colors.grey.shade300,
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              // Radio button
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected ? AppColors.green : Colors.grey.shade400,
                    width: 2,
                  ),
                ),
                child: isSelected
                    ? Center(
                        child: Container(
                          width: 10,
                          height: 10,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: AppColors.green,
                          ),
                        ),
                      )
                    : null,
              ),

              const SizedBox(width: 16),

              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${slot['title']} ${slot['time']}',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        const Text(
                          '💰',
                          style: TextStyle(fontSize: 14),
                        ),
                        const SizedBox(width: 6),
                        Text(
                          slot['earnings'],
                          style: const TextStyle(
                            fontSize: 14,
                            color: AppColors.green,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Show the time slot selection dialog
  static Future<String?> show({
    required BuildContext context,
    Function(String)? onTimeSlotSelected,
  }) async {
    return await showDialog<String>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return TimeSlotSelectionDialog(onTimeSlotSelected: onTimeSlotSelected);
      },
    );
  }
}
