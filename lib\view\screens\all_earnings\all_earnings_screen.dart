import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/utils/responsive_utils.dart';
import 'package:get/get.dart';

class AllEarningsScreen extends StatefulWidget {
  const AllEarningsScreen({super.key});

  @override
  State<AllEarningsScreen> createState() => _AllEarningsScreenState();
}

class _AllEarningsScreenState extends State<AllEarningsScreen> {
  int _selectedWeekIndex = 1; // Week 2 is selected by default

  final List<Map<String, dynamic>> _weeks = [
    {'week': 'Week 1', 'dateRange': '1 May - 7 May', 'isCurrentWeek': false},
    {'week': 'Week 2', 'dateRange': '8 May - 14 May', 'isCurrentWeek': true},
    {'week': 'Week 3', 'dateRange': '15 May - 21 May', 'isCurrentWeek': false},
    {'week': 'Week 4', 'dateRange': '22 May - 28 May', 'isCurrentWeek': false},
    {'week': 'Week 5', 'dateRange': '28 May - 03 April', 'isCurrentWeek': false},
  ];

  String _selectedFilter = 'All';
  final List<String> _filterOptions = ['All', 'Slotted', 'Express'];

  final List<Map<String, dynamic>> _earningBreakdown = [
    {
      'title': 'Order earnings',
      'amount': '₹1000',
      'icon': Icons.shopping_bag,
      'color': Colors.red,
      'isExpanded': false,
    },
    {
      'title': 'Total Incentive',
      'amount': '₹1000',
      'icon': Icons.card_giftcard,
      'color': Colors.teal,
      'isExpanded': false,
    },
    {
      'title': 'Other Incentive',
      'amount': '₹100',
      'icon': Icons.star,
      'color': Colors.green,
      'isExpanded': false,
    },
    {
      'title': 'Total deductions',
      'amount': '₹200',
      'icon': Icons.remove_circle,
      'color': Colors.red,
      'isExpanded': false,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: AppColors.textPrimary),
          onPressed: () => Get.back(),
        ),
        title: Text(
          'All Earnings',
          style: AppTextTheme.cardTitle,
        ),
        centerTitle: false,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Week Selection Header
            _buildWeekSelector(context),

            SizedBox(height: ResponsiveUtils.spacingM(context)),

            // Earnings Summary Card
            _buildEarningsSummaryCard(context),

            SizedBox(height: ResponsiveUtils.spacingM(context)),

            // Daywise Earning Details Section
            _buildDaywiseEarningSection(context),
          ],
        ),
      ),
    );
  }

  Widget _buildWeekSelector(BuildContext context) {
    final selectedWeek = _weeks[_selectedWeekIndex];

    return GestureDetector(
      onTap: () => _showWeekFilterDialog(context),
      child: Container(
        padding: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
          boxShadow: [
            BoxShadow(
              color: AppColors.shadowLight,
              blurRadius: ResponsiveUtils.width(context, 1),
              offset: Offset(0, ResponsiveUtils.height(context, 0.2)),
            ),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        selectedWeek['week'],
                        style: AppTextTheme.cardSubtitle.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      SizedBox(width: ResponsiveUtils.spacingXS(context)),
                      if (selectedWeek['isCurrentWeek'])
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: ResponsiveUtils.spacingS(context),
                            vertical: ResponsiveUtils.spacingXS(context) / 2,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.green.withAlpha(25),
                            borderRadius:
                                BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.small)),
                          ),
                          child: Text(
                            'This Week',
                            style: AppTextTheme.cardCaption.copyWith(
                              color: AppColors.green,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                    ],
                  ),
                  SizedBox(height: ResponsiveUtils.spacingXS(context) / 2),
                  Text(
                    selectedWeek['dateRange'],
                    style: AppTextTheme.cardCaption.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: ResponsiveUtils.spacingS(context),
                vertical: ResponsiveUtils.spacingXS(context),
              ),
              decoration: BoxDecoration(
                color: AppColors.green,
                borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.visibility,
                    color: Colors.white,
                    size: ResponsiveUtils.iconSize(context, IconSizeType.small),
                  ),
                  SizedBox(width: ResponsiveUtils.spacingXS(context) / 2),
                  Text(
                    'View past',
                    style: AppTextTheme.cardCaption.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEarningsSummaryCard(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: ResponsiveUtils.width(context, 1),
            offset: Offset(0, ResponsiveUtils.height(context, 0.2)),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '₹1,605',
            style: AppTextTheme.cardTitle.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: ResponsiveUtils.spacingXS(context) / 2),
          Text(
            'Your earnings this week',
            style: AppTextTheme.cardSubtitle.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: ResponsiveUtils.spacingM(context)),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  context,
                  '🥕',
                  'Daily Average',
                  '₹190.5',
                ),
              ),
              SizedBox(width: ResponsiveUtils.spacingM(context)),
              Expanded(
                child: _buildStatItem(
                  context,
                  '🍯',
                  'Orders Completed',
                  '36',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDaywiseEarningSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header
        Text(
          'Daywise earning details',
          style: AppTextTheme.cardSubtitle.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: ResponsiveUtils.spacingM(context)),

        // Filter Tabs
        _buildFilterTabs(context),

        SizedBox(height: ResponsiveUtils.spacingM(context)),

        // Earning Breakdown List
        ..._earningBreakdown.map((item) => _buildEarningBreakdownCard(context, item)),
      ],
    );
  }

  Widget _buildFilterTabs(BuildContext context) {
    return Row(
      children: _filterOptions.map((filter) {
        final isSelected = filter == _selectedFilter;
        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedFilter = filter;
            });
          },
          child: Container(
            margin: EdgeInsets.only(right: ResponsiveUtils.spacingS(context)),
            padding: EdgeInsets.symmetric(
              horizontal: ResponsiveUtils.spacingM(context),
              vertical: ResponsiveUtils.spacingS(context),
            ),
            decoration: BoxDecoration(
              color: isSelected ? Colors.black : Colors.transparent,
              borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.large)),
              border: Border.all(
                color: isSelected ? Colors.black : AppColors.textSecondary.withValues(alpha: 0.3),
              ),
            ),
            child: Text(
              filter,
              style: AppTextTheme.cardCaption.copyWith(
                color: isSelected ? Colors.white : AppColors.textSecondary,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildEarningBreakdownCard(BuildContext context, Map<String, dynamic> item) {
    return Container(
      margin: EdgeInsets.only(bottom: ResponsiveUtils.spacingS(context)),
      padding: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: ResponsiveUtils.width(context, 1),
            offset: Offset(0, ResponsiveUtils.height(context, 0.2)),
          ),
        ],
      ),
      child: Row(
        children: [
          // Icon
          Container(
            padding: EdgeInsets.all(ResponsiveUtils.spacingS(context)),
            decoration: BoxDecoration(
              color: item['color'].withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.small)),
            ),
            child: Icon(
              item['icon'],
              color: item['color'],
              size: ResponsiveUtils.iconSize(context, IconSizeType.medium),
            ),
          ),

          SizedBox(width: ResponsiveUtils.spacingM(context)),

          // Title
          Expanded(
            child: Text(
              item['title'],
              style: AppTextTheme.cardSubtitle.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          // Amount
          Text(
            item['amount'],
            style: AppTextTheme.cardSubtitle.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),

          SizedBox(width: ResponsiveUtils.spacingS(context)),

          // Dropdown Arrow
          Icon(
            item['title'] == 'Total deductions' ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
            color: AppColors.textSecondary,
            size: ResponsiveUtils.iconSize(context, IconSizeType.medium),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, String emoji, String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              emoji,
              style: AppTextTheme.cardSubtitle,
            ),
            SizedBox(width: ResponsiveUtils.spacingXS(context)),
            Text(
              label,
              style: AppTextTheme.cardCaption.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
        SizedBox(height: ResponsiveUtils.spacingXS(context) / 2),
        Text(
          value,
          style: AppTextTheme.cardSubtitle.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  void _showWeekFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
          ),
          child: Container(
            padding: EdgeInsets.all(ResponsiveUtils.spacingL(context)),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Select your preferred filter',
                      style: AppTextTheme.cardSubtitle.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    GestureDetector(
                      onTap: () => Navigator.pop(context),
                      child: Icon(
                        Icons.close,
                        color: AppColors.textSecondary,
                        size: ResponsiveUtils.iconSize(context, IconSizeType.medium),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: ResponsiveUtils.spacingL(context)),
                ..._weeks.asMap().entries.map((entry) {
                  int index = entry.key;
                  Map<String, dynamic> week = entry.value;
                  bool isSelected = index == _selectedWeekIndex;

                  return _buildWeekOption(context, week, isSelected, index);
                }),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildWeekOption(BuildContext context, Map<String, dynamic> weekData, bool isSelected, int index) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedWeekIndex = index;
        });
        Navigator.pop(context);
      },
      child: Container(
        margin: EdgeInsets.only(bottom: ResponsiveUtils.spacingM(context)),
        padding: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.green.withAlpha(25) : Colors.transparent,
          borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
          border: Border.all(
            color: isSelected ? AppColors.green : AppColors.borderLight,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        weekData['week'],
                        style: AppTextTheme.cardSubtitle.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      if (weekData['isCurrentWeek']) ...[
                        SizedBox(width: ResponsiveUtils.spacingS(context)),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: ResponsiveUtils.spacingS(context),
                            vertical: ResponsiveUtils.spacingXS(context) / 2,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.green.withAlpha(25),
                            borderRadius:
                                BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.small)),
                          ),
                          child: Text(
                            'This Week',
                            style: AppTextTheme.cardCaption.copyWith(
                              color: AppColors.green,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                  SizedBox(height: ResponsiveUtils.spacingXS(context) / 2),
                  Text(
                    weekData['dateRange'],
                    style: AppTextTheme.cardCaption.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color: AppColors.green,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 14,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
