# KisanKonnect Rider App - Development Environment
# This file contains development-specific environment variables

# =============================================================================
# APP CONFIGURATION
# =============================================================================
APP_NAME=KisanKonnect Rider Dev
BUNDLE_ID=com.kisankonnect.rider.dev

# =============================================================================
# API CONFIGURATION
# =============================================================================
API_BASE_URL=http://knet.kisankonnect.com/SRIT3O/api
SOCKET_URL=wss://dev-socket.kisankonnect.com
REQUEST_TIMEOUT=30
MAX_RETRIES=3

# =============================================================================
# FEATURE FLAGS - DEVELOPMENT
# =============================================================================
ENABLE_LOGGING=true
ENABLE_CRASHLYTICS=false
ENABLE_ANALYTICS=false
ENABLE_PERFORMANCE_MONITORING=false
ENABLE_DEVICE_PREVIEW=true
ENABLE_FLAVOR_BANNER=true

# =============================================================================
# CACHE & STORAGE - DEVELOPMENT
# =============================================================================
CACHE_TIMEOUT_MINUTES=5
DATABASE_NAME=kisankonnect_rider_dev.db
LOG_LEVEL=debug

# =============================================================================
# SECURITY - DEVELOPMENT (Relaxed)
# =============================================================================
ENABLE_SSL_PINNING=false
ENABLE_CERTIFICATE_VALIDATION=false
ENABLE_NETWORK_SECURITY=false

# =============================================================================
# DEVELOPMENT TOOLS - ENABLED
# =============================================================================
ENABLE_DEBUG_MENU=true
ENABLE_NETWORK_INSPECTOR=true
ENABLE_PERFORMANCE_OVERLAY=true

# =============================================================================
# DEVELOPMENT SPECIFIC
# =============================================================================
# Use test/mock data
USE_MOCK_DATA=false
MOCK_API_DELAY=1000

# Development server settings
DEV_SERVER_PORT=3000
DEV_HOT_RELOAD=true

# Debug settings
VERBOSE_LOGGING=true
LOG_API_REQUESTS=true
LOG_API_RESPONSES=true

# Testing
ENABLE_WIDGET_TESTING=true
ENABLE_INTEGRATION_TESTING=true

# =============================================================================
# THIRD PARTY INTEGRATIONS - DEVELOPMENT
# =============================================================================
GOOGLE_MAPS_API_KEY=AIzaSyDRL7BCTTbpAXgKb5pZgM8S05uFlpLZx4s
RAZORPAY_KEY_ID=your_razorpay_key_id
