import 'package:flutter/material.dart';
import 'elegant_shimmer.dart';
import '../../../utils/responsive_utils.dart';

/// Elegant dashboard shimmer layouts like Zomato/Swiggy
class ElegantDashboardShimmer extends StatelessWidget {
  const ElegantDashboardShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return ElegantShimmer(
      enabled: true,
      child: SingleChildScrollView(
        child: Column(
          children: [
            // Shift Info Section Shimmer
            const ElegantShiftInfoShimmer(),

            SizedBox(height: ResponsiveUtils.height(context, 2)),

            // Earning Badges Shimmer
            const ElegantEarningBadgesShimmer(),

            <PERSON><PERSON><PERSON><PERSON>(height: ResponsiveUtils.height(context, 2)),

            // Banner Carousel Shimmer
            const ElegantBannerCarouselShimmer(),

            SizedBox(height: ResponsiveUtils.height(context, 2)),

            // Earnings Summary Shimmer
            const ElegantEarningsSummaryShimmer(),

            SizedBox(height: ResponsiveUtils.height(context, 2)),

            // Orders Section Shimmer
            const ElegantOrdersSectionShimmer(),

            SizedBox(height: ResponsiveUtils.height(context, 2)),

            // Reports Section Shimmer
            const ElegantReportsSectionShimmer(),

            SizedBox(height: ResponsiveUtils.height(context, 3)),
          ],
        ),
      ),
    );
  }
}

/// Shift Info Section Shimmer
class ElegantShiftInfoShimmer extends StatelessWidget {
  const ElegantShiftInfoShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: ResponsiveUtils.spacingM(context)),
      padding: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Shift status row
          Row(
            children: [
              ElegantShimmerBox(width: 100, height: 24, borderRadius: BorderRadius.circular(12)),
              const SizedBox(width: 12),
              const ElegantShimmerLine(width: 120, height: 16),
              const Spacer(),
              const ElegantShimmerCircle(size: 24),
            ],
          ),

          const SizedBox(height: 16),

          // Dotted line
          Container(
            height: 1,
            width: double.infinity,
            color: const Color(0xFFF0F0F0),
          ),

          const SizedBox(height: 16),

          // Timer row
          Row(
            children: [
              const ElegantShimmerCircle(size: 24),
              const SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const ElegantShimmerLine(width: 80, height: 16),
                  const SizedBox(height: 4),
                  const ElegantShimmerLine(width: 60, height: 12),
                ],
              ),
              const Spacer(),
              ElegantShimmerBox(width: 100, height: 32, borderRadius: BorderRadius.circular(16)),
            ],
          ),
        ],
      ),
    );
  }
}

/// Earning Badges Shimmer
class ElegantEarningBadgesShimmer extends StatelessWidget {
  const ElegantEarningBadgesShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 120,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: ResponsiveUtils.spacingM(context)),
        itemCount: 3,
        itemBuilder: (context, index) {
          return Container(
            width: 200,
            margin: const EdgeInsets.only(right: 12),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.04),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const ElegantShimmerCircle(size: 32),
                    const SizedBox(width: 12),
                    const Expanded(child: ElegantShimmerLine(height: 14)),
                  ],
                ),
                const SizedBox(height: 12),
                const ElegantShimmerLine(width: 80, height: 20),
                const SizedBox(height: 8),
                const ElegantShimmerLine(width: 120, height: 12),
              ],
            ),
          );
        },
      ),
    );
  }
}

/// Banner Carousel Shimmer
class ElegantBannerCarouselShimmer extends StatelessWidget {
  const ElegantBannerCarouselShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 160,
      margin: EdgeInsets.symmetric(horizontal: ResponsiveUtils.spacingM(context)),
      decoration: BoxDecoration(
        color: const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Center(
        child: ElegantShimmerCircle(size: 48),
      ),
    );
  }
}

/// Earnings Summary Shimmer
class ElegantEarningsSummaryShimmer extends StatelessWidget {
  const ElegantEarningsSummaryShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: ResponsiveUtils.spacingM(context)),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const ElegantShimmerLine(width: 120, height: 16),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: Column(
                  children: [
                    const ElegantShimmerLine(width: 60, height: 24),
                    const SizedBox(height: 8),
                    const ElegantShimmerLine(width: 80, height: 12),
                  ],
                ),
              ),
              Container(width: 1, height: 40, color: const Color(0xFFF0F0F0)),
              Expanded(
                child: Column(
                  children: [
                    const ElegantShimmerLine(width: 60, height: 24),
                    const SizedBox(height: 8),
                    const ElegantShimmerLine(width: 80, height: 12),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// Orders Section Shimmer
class ElegantOrdersSectionShimmer extends StatelessWidget {
  const ElegantOrdersSectionShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: ResponsiveUtils.spacingM(context)),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFF0F0F0)),
      ),
      child: Row(
        children: [
          const ElegantShimmerLine(width: 120, height: 16),
          const Spacer(),
          const ElegantShimmerCircle(size: 32),
        ],
      ),
    );
  }
}

/// Reports Section Shimmer
class ElegantReportsSectionShimmer extends StatelessWidget {
  const ElegantReportsSectionShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: ResponsiveUtils.spacingM(context)),
          child: const ElegantShimmerLine(width: 80, height: 16),
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: EdgeInsets.symmetric(horizontal: ResponsiveUtils.spacingM(context)),
            itemCount: 3,
            itemBuilder: (context, index) {
              return ElegantCardShimmer(
                height: 100,
                margin: const EdgeInsets.only(right: 12),
                children: [
                  const ElegantShimmerLine(width: 100, height: 14),
                  const SizedBox(height: 8),
                  const ElegantShimmerLine(width: 60, height: 20),
                  const Spacer(),
                  const ElegantShimmerLine(width: 80, height: 12),
                ],
              );
            },
          ),
        ),
      ],
    );
  }
}
