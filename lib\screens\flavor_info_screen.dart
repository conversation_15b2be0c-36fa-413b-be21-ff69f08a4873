import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../config/flavor_config.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

class FlavorInfoScreen extends StatelessWidget {
  const FlavorInfoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final envService = EnvironmentService.instance;
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Environment Info'),
        backgroundColor: _getAppBarColor(),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.copy),
            onPressed: () => _copyToClipboard(context),
            tooltip: 'Copy Info',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Environment Badge
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: _getEnvironmentColor(),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                envService.environmentName.toUpperCase(),
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),

            const SizedBox(height: 24),

            // App Information
            _buildSection(
              'App Information',
              [
                _buildInfoRow('App Name', envService.appName),
                _buildInfoRow('Bundle ID', envService.bundleId),
                _buildInfoRow('Environment', envService.environmentName),
                _buildInfoRow('Is Development', envService.isDevelopment.toString()),
                _buildInfoRow('Is Production', envService.isProduction.toString()),
              ],
            ),

            const SizedBox(height: 24),

            // API Configuration
            _buildSection(
              'API Configuration',
              [
                _buildInfoRow('Base URL', envService.baseUrl),
                _buildInfoRow('Socket URL', envService.socketUrl),
                _buildInfoRow('API Version', envService.apiVersion),
                _buildInfoRow('API Base URL', envService.apiBaseUrl),
              ],
            ),

            const SizedBox(height: 24),

            // Feature Flags
            _buildSection(
              'Feature Flags',
              [
                _buildInfoRow('Logging Enabled', envService.isLoggingEnabled.toString()),
                _buildInfoRow('Crashlytics Enabled', envService.isCrashlyticsEnabled.toString()),
                _buildInfoRow('Debug Menu', envService.isFeatureEnabled('debug_menu').toString()),
                _buildInfoRow(
                    'Performance Monitoring', envService.isFeatureEnabled('performance_monitoring').toString()),
              ],
            ),

            const SizedBox(height: 24),

            // API Endpoints
            _buildSection(
              'API Endpoints',
              [
                _buildInfoRow('Login', envService.loginEndpoint),
                _buildInfoRow('Profile', envService.profileEndpoint),
                _buildInfoRow('Orders', envService.ordersEndpoint),
                _buildInfoRow('Earnings', envService.earningsEndpoint),
                _buildInfoRow('Payments', envService.paymentsEndpoint),
                _buildInfoRow('Wallet', envService.walletEndpoint),
              ],
            ),

            const SizedBox(height: 24),

            // Configuration Details
            _buildSection(
              'Configuration Details',
              [
                _buildInfoRow('Connection Timeout', '${envService.connectionTimeout.inSeconds}s'),
                _buildInfoRow('Receive Timeout', '${envService.receiveTimeout.inSeconds}s'),
                _buildInfoRow('Max Retries', envService.maxRetries.toString()),
                _buildInfoRow('Cache Expiry', '${envService.cacheExpiry.inMinutes}min'),
                _buildInfoRow('Database Name', envService.databaseName),
                _buildInfoRow('Prefs Key Prefix', envService.prefsKeyPrefix),
              ],
            ),

            const SizedBox(height: 32),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _logEnvironmentInfo(),
                    icon: const Icon(Icons.bug_report),
                    label: const Text('Log to Console'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _getEnvironmentColor(),
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _copyToClipboard(context),
                    icon: const Icon(Icons.copy),
                    label: const Text('Copy Info'),
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: _getEnvironmentColor()),
                      foregroundColor: _getEnvironmentColor(),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.black54,
              ),
            ),
          ),
          const Text(': '),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w400,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getEnvironmentColor() {
    return FlavorConfig.isDevelopment ? Colors.orange : Colors.green;
  }

  Color _getAppBarColor() {
    return FlavorConfig.isDevelopment ? Colors.orange : Colors.green;
  }

  void _logEnvironmentInfo() {
    EnvironmentService.instance.logEnvironmentInfo();
  }

  void _copyToClipboard(BuildContext context) {
    final envService = EnvironmentService.instance;
    final info = envService.environmentConfig;
    final text = info.entries.map((e) => '${e.key}: ${e.value}').join('\n');

    Clipboard.setData(ClipboardData(text: text));

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Environment info copied to clipboard'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}
