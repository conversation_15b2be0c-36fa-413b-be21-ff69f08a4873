{"@@locale": "en", "appName": "KisanKonnect Rider", "@appName": {"description": "The name of the application"}, "profile": "Profile", "@profile": {"description": "Profile screen title"}, "riderName": "Rider Name", "@riderName": {"description": "Default rider name"}, "phoneNumber": "+91- 98388 89898", "@phoneNumber": {"description": "Default phone number"}, "idCard": "ID Card", "@idCard": {"description": "ID Card menu item"}, "cashBalance": "Cash Balance", "@cashBalance": {"description": "Cash Balance menu item"}, "myShift": "My Shift", "@myShift": {"description": "My Shift menu item"}, "myKFHLocation": "My KFH Location", "@myKFHLocation": {"description": "My KFH Location menu item"}, "tripHistory": "Trip History", "@tripHistory": {"description": "Trip History menu item"}, "kisanStore": "Kissan Store", "@kisanStore": {"description": "Kissan Store menu item"}, "newTrends": "New trends", "@newTrends": {"description": "New trends badge text"}, "referAndEarn": "Refer & Earn", "@referAndEarn": {"description": "Refer & Earn menu item"}, "referralBonusText": "10,000+ riders are earning referral bonus", "@referralBonusText": {"description": "Referral bonus subtitle text"}, "helpAndSupport": "Help & Support", "@helpAndSupport": {"description": "Help & Support menu item"}, "appVersion": "App version v0.1.10", "@appVersion": {"description": "App version text"}, "featuredProducts": "Featured products", "@featuredProducts": {"description": "Featured products section title"}, "bigDiscount": "BIG Discount", "@bigDiscount": {"description": "Big discount banner text"}, "orderNow": "Order Now", "@orderNow": {"description": "Order now button text"}, "cartReview": "Cart review", "@cartReview": {"description": "Cart review screen title"}, "cartDetails": "Cart details", "@cartDetails": {"description": "Cart details section title"}, "wallet": "Wallet", "@wallet": {"description": "Wallet section title"}, "kisanKonnectWallet": "KisanKonnect Wallet", "@kisanKonnectWallet": {"description": "KisanKonnect Wallet option"}, "kisanKash": "<PERSON><PERSON><PERSON><PERSON>", "@kisanKash": {"description": "KisanKash option"}, "balance": "Balance", "@balance": {"description": "Balance label"}, "add": "Add", "@add": {"description": "Add button text"}, "redeem": "Redeem", "@redeem": {"description": "Redeem button text"}, "selectPaymentMode": "Select your payment mode", "@selectPaymentMode": {"description": "Select payment mode section title"}, "cardsUPINetbanking": "Cards/UPI/Netbanking", "@cardsUPINetbanking": {"description": "Cards/UPI/Netbanking payment option"}, "payWithUPI": "Pay with UPI", "@payWithUPI": {"description": "Pay with UPI section title"}, "useAnyUPIApp": "Use any UPI app on your phone to pay", "@useAnyUPIApp": {"description": "UPI instruction text"}, "payNow": "Pay Now", "@payNow": {"description": "Pay Now button text"}, "orderPlacedSuccessfully": "Your order placed\nsuccessfully", "@orderPlacedSuccessfully": {"description": "Order success message"}, "continueShopping": "Continue Shopping", "@continueShopping": {"description": "Continue shopping button text"}, "viewMyOrders": "View My Orders", "@viewMyOrders": {"description": "View my orders button text"}, "selectAddress": "Select Address", "@selectAddress": {"description": "Select address screen title"}, "selectYourLocation": "Select your location", "@selectYourLocation": {"description": "Select location screen title"}, "getUpdatedOnWhatsapp": "Get updated on Whatsapp", "@getUpdatedOnWhatsapp": {"description": "WhatsApp updates checkbox text"}, "continueButton": "Continue", "@continueButton": {"description": "Continue button text"}, "selectPreferableFilter": "Select your preferable filter", "@selectPreferableFilter": {"description": "Filter dialog title"}, "tripAscending": "Trip in Ascending order", "@tripAscending": {"description": "Trip ascending filter option"}, "tripDescending": "Trip in Descending order", "@tripDescending": {"description": "Trip descending filter option"}, "onlySuccessTrip": "Only Success Trip", "@onlySuccessTrip": {"description": "Success trip filter option"}, "onlyFailedTrip": "Only Failed Trip", "@onlyFailedTrip": {"description": "Failed trip filter option"}, "okay": "Okay", "@okay": {"description": "Okay button text"}, "myEarnings": "My Earnings", "@myEarnings": {"description": "Earnings summary section title"}, "today": "Today", "@today": {"description": "Today tab in earnings"}, "thisWeek": "This week", "@thisWeek": {"description": "This week tab in earnings"}, "thisMonth": "This month", "@thisMonth": {"description": "This month tab in earnings"}, "totalEarningsOfTheDay": "Total's earnings of the day", "@totalEarningsOfTheDay": {"description": "Total earnings of current day"}, "totalEarningsOfThePreviousDay": "Total's earnings of the previous day", "@totalEarningsOfThePreviousDay": {"description": "Total earnings of previous day"}, "orderDelivered": "Order Delivered", "@orderDelivered": {"description": "Order delivered label"}, "orderEarning": "Order Earning", "@orderEarning": {"description": "Order earning label"}, "rainSurgeEarning": "Rain Surge Earning", "@rainSurgeEarning": {"description": "Rain surge earning label"}, "totalIncentive": "Total Incentive", "@totalIncentive": {"description": "Total incentive label"}, "incentiveSubtitle": "Applied if you work on Sat and Sun", "@incentiveSubtitle": {"description": "Incentive subtitle explanation"}, "doMoreOrdersAndEarn": "Do 15 more orders and earn", "@doMoreOrdersAndEarn": {"description": "Incentive banner message"}, "viewAllEarningsAndIncentives": "View all earnings & incentives", "@viewAllEarningsAndIncentives": {"description": "View all earnings button text"}, "orders": "orders", "@orders": {"description": "Orders unit text"}, "referFriendAndEarn": "Refer a friend an earn", "@referFriendAndEarn": {"description": "Refer a friend and earn title"}, "yourFriendGets": "Your friend gets", "@yourFriendGets": {"description": "Your friend gets label"}, "onJoining": " on joining!", "@onJoining": {"description": "On joining text"}, "totalReferralEarnings": "Total referral earnings: ₹5500", "@totalReferralEarnings": {"description": "Total referral earnings text"}, "friendsReferred": "2 friends referred", "@friendsReferred": {"description": "Friends referred count"}, "shareYourReferralCode": "Share your referral code", "@shareYourReferralCode": {"description": "Share referral code title"}, "howItWorks": "How it works", "@howItWorks": {"description": "How it works section title"}, "referInSimpleSteps": "Refer in 3 simple steps", "@referInSimpleSteps": {"description": "Refer in simple steps subtitle"}, "copyCodeOrShareViaWhatsapp": "Copy Code or Share via Whatsapp", "@copyCodeOrShareViaWhatsapp": {"description": "Step 1 description"}, "completeTheTarget": "Complete the Target", "@completeTheTarget": {"description": "Step 2 description"}, "enjoyTheBonus": "Enjoy the Bonus", "@enjoyTheBonus": {"description": "Step 3 description"}, "yourReferrals": "Your referrals", "@yourReferrals": {"description": "Your referrals section title"}, "pending": "Pending", "@pending": {"description": "Pending status"}, "success": "Success", "@success": {"description": "Success status"}, "failed": "Failed", "@failed": {"description": "Failed status"}, "inviteViaWhatsApp": "Invite via WhatsApp", "@inviteViaWhatsApp": {"description": "Invite via WhatsApp button text"}, "referralCodeCopied": "Referral code copied to clipboard!", "@referralCodeCopied": {"description": "Referral code copied message"}, "joinKisanKonnectMessage": "🎉 Join <PERSON> and earn ₹10,000 on joining!\n\nUse my referral code: {code}\n\nDownload the app and start earning today!", "@joinKisanKonnectMessage": {"description": "WhatsApp share message", "placeholders": {"code": {"type": "String", "description": "Referral code"}}}}