import 'package:flutter/foundation.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

class EnvironmentService {
  static EnvironmentService? _instance;
  static EnvironmentService get instance => _instance ??= EnvironmentService._();

  EnvironmentService._();

  /// Initialize the environment service
  static void initialize() {
    _instance = EnvironmentService._();
  }

  /// Get current environment name
  String get environmentName => FlavorConfig.name;

  /// Check if running in development
  bool get isDevelopment => FlavorConfig.isDevelopment;

  /// Check if running in production
  bool get isProduction => FlavorConfig.isProduction;

  /// Get app title based on environment
  String get appTitle => FlavorConfig.title;

  /// Get base API URL
  String get baseUrl => Environment.baseUrl;

  /// Get socket URL
  String get socketUrl => Environment.socketUrl;

  /// Get API version
  String get apiVersion => Environment.apiVersion;

  /// Check if logging is enabled
  bool get isLoggingEnabled => Environment.enableLogging;

  /// Check if crashlytics is enabled
  bool get isCrashlyticsEnabled => Environment.enableCrashlytics;

  /// Get bundle ID
  String get bundleId => Environment.bundleId;

  /// Get app name
  String get appName => Environment.appName;

  /// Get full API base URL
  String get apiBaseUrl => Environment.apiBaseUrl;

  /// Get login endpoint
  String get loginEndpoint => Environment.loginUrl;

  /// Get profile endpoint
  String get profileEndpoint => Environment.profileUrl;

  /// Get orders endpoint
  String get ordersEndpoint => Environment.ordersUrl;

  /// Get earnings endpoint
  String get earningsEndpoint => Environment.earningsUrl;

  /// Get payments endpoint
  String get paymentsEndpoint => Environment.paymentsUrl;

  /// Get wallet endpoint
  String get walletEndpoint => Environment.walletUrl;

  /// Log environment information (only in debug mode)
  void logEnvironmentInfo() {
    if (kDebugMode && isLoggingEnabled) {
      debugPrint('🌍 Environment Information:');
      debugPrint('📱 App: $appName');
      debugPrint('🏷️ Flavor: $environmentName');
      debugPrint('🌐 Base URL: $baseUrl');
      debugPrint('🔌 Socket URL: $socketUrl');
      debugPrint('📝 Logging: ${isLoggingEnabled ? "Enabled" : "Disabled"}');
      debugPrint('🔥 Crashlytics: ${isCrashlyticsEnabled ? "Enabled" : "Disabled"}');
      debugPrint('📦 Bundle ID: $bundleId');
    }
  }

  /// Get environment configuration as Map
  Map<String, dynamic> get environmentConfig => Environment.debugInfo;

  /// Get HTTP headers with environment info
  Map<String, String> get defaultHeaders => {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-App-Version': apiVersion,
        'X-App-Flavor': environmentName,
        'X-Platform': 'flutter',
      };

  /// Get timeout configurations based on environment
  Duration get connectionTimeout => isDevelopment ? const Duration(seconds: 30) : const Duration(seconds: 15);

  Duration get receiveTimeout => isDevelopment ? const Duration(seconds: 30) : const Duration(seconds: 15);

  /// Get retry configurations
  int get maxRetries => isDevelopment ? 3 : 2;

  /// Check if feature is enabled based on environment
  bool isFeatureEnabled(String feature) {
    switch (feature) {
      case 'debug_menu':
        return isDevelopment;
      case 'crash_reporting':
        return isCrashlyticsEnabled;
      case 'detailed_logging':
        return isLoggingEnabled;
      case 'performance_monitoring':
        return isProduction;
      default:
        return false;
    }
  }

  /// Get environment-specific colors
  Map<String, dynamic> get environmentColors => {
        'primary': isDevelopment ? 0xFFFF9800 : 0xFF4CAF50,
        'accent': isDevelopment ? 0xFFFF5722 : 0xFF2196F3,
        'statusBar': isDevelopment ? 0xFFFF9800 : 0xFF4CAF50,
      };

  /// Get cache configurations
  Duration get cacheExpiry => isDevelopment ? const Duration(minutes: 5) : const Duration(hours: 1);

  /// Get database configurations
  String get databaseName => isDevelopment ? 'kisankonnect_rider_dev.db' : 'kisankonnect_rider.db';

  /// Get shared preferences key prefix
  String get prefsKeyPrefix => isDevelopment ? 'dev_' : 'prod_';
}
