import 'package:flutter/material.dart';

class AppColors {
  static const Color green = Color(0xFF2E7D32);
  static const Color greenLight = Color(0xFFE8F5E9);
  static const Color greenDark = Color(0xFF1B7A34);

  // Background colors
  static const Color background = Color(0xFFF7F8FA);
  static const Color cardBackground = Colors.white;

  // Text colors
  static const Color textPrimary = Color(0xFF000000);
  static const Color textSecondary = Color(0xFF666666);
  static const Color textLight = Color(0xFF999999);

  // Border colors
  static const Color borderLight = Color(0xFFE0E0E0);
  static const Color borderMedium = Color(0xFFCCCCCC);

  // Status colors
  static const Color error = Color(0xFFE53E3E);
  static const Color warning = Color(0xFFFF8C00);
  static const Color success = Color(0xFF25AA4A);

  // Shadow colors
  static const Color shadowLight = Color(0x1A000000);
  static const Color shadowMedium = Color(0x33000000);
}

final ThemeData appTheme = ThemeData(
  primaryColor: AppColors.green,
  colorScheme: ColorScheme.fromSwatch().copyWith(
    primary: AppColors.green,
    secondary: AppColors.green,
  ),
  elevatedButtonTheme: ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      backgroundColor: AppColors.green,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(32),
      ),
      foregroundColor: Colors.white,
    ),
  ),
  outlinedButtonTheme: OutlinedButtonThemeData(
    style: OutlinedButton.styleFrom(
      side: const BorderSide(color: AppColors.green),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
    ),
  ),
  bottomNavigationBarTheme: const BottomNavigationBarThemeData(
    selectedItemColor: AppColors.green,
    unselectedItemColor: Colors.grey,
  ),
  // Add more theme customizations as needed
);
