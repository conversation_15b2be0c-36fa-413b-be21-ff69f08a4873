import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/utils/responsive_utils.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../wallet/wallet_screen.dart';

class DashboardHeader extends StatelessWidget {
  final bool isOnline;
  final ValueChanged<bool> onToggle;
  const DashboardHeader({required this.isOnline, required this.onToggle, super.key});

  /// Make a phone call to the support number
  static Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);
    try {
      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri);
      } else {
        // Show error message if phone dialing is not available
        Get.snackbar(
          AppStrings.get('error'),
          AppStrings.get('phoneCallNotSupported'),
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      // Handle any errors
      Get.snackbar(
        AppStrings.get('error'),
        AppStrings.get('phoneCallFailed'),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // Using ResponsiveUtils for consistent sizing
    final headerHeight = ResponsiveUtils.height(context, 10);
    final horizontalPadding = ResponsiveUtils.spacingM(context);
    final backgroundColor = isOnline ? AppColors.green : Colors.grey.shade600;

    return Material(
      elevation: 0,
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(30),
            bottomRight: Radius.circular(30),
          ),
        ),
        child: SafeArea(
          maintainBottomViewPadding: true,
          child: Container(
            height: headerHeight.clamp(100.0, 140.0),
            padding: EdgeInsets.symmetric(
              horizontal: horizontalPadding,
              vertical: ResponsiveUtils.height(context, 1),
            ),
            child: Column(
              children: [
                SizedBox(height: ResponsiveUtils.height(context, 1)),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: ResponsiveUtils.spacingS(context),
                        vertical: ResponsiveUtils.height(context, 0.8),
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius:
                            BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.extraLarge)),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Status icon
                          Container(
                            width: ResponsiveUtils.width(context, 2.5),
                            height: ResponsiveUtils.width(context, 2.5),
                            decoration: BoxDecoration(
                              color: isOnline ? Colors.white : Colors.grey.shade400,
                              shape: BoxShape.circle,
                            ),
                          ),
                          SizedBox(width: ResponsiveUtils.spacingS(context)),
                          // Status text
                          Text(
                            isOnline ? AppStrings.get('online') : AppStrings.get('offline'),
                            style: AppTextTheme.buttonLarge.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: ResponsiveUtils.spacingS(context)),
                    // Custom toggle switch with symbols
                    GestureDetector(
                      onTap: () => onToggle(!isOnline),
                      child: Container(
                        width: 60,
                        height: 30,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(15),
                          color: isOnline ? Colors.white.withValues(alpha: 0.3) : Colors.black26,
                        ),
                        child: Stack(
                          children: [
                            // Thumb container
                            AnimatedPositioned(
                              duration: Duration(milliseconds: 200),
                              curve: Curves.easeInOut,
                              left: isOnline ? 30 : 2,
                              top: 2,
                              child: Container(
                                width: 26,
                                height: 26,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  shape: BoxShape.circle,
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black12,
                                      blurRadius: 4,
                                      offset: Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Center(
                                  child: Text(
                                    isOnline ? '✓' : '✕',
                                    style: TextStyle(
                                      color: isOnline ? AppColors.green : Colors.grey,
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const Spacer(),
                    // Action buttons
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Wallet button
                        IconButton(
                          icon: Icon(
                            Icons.account_balance_wallet_outlined,
                            color: Colors.white,
                            size: ResponsiveUtils.iconSize(context, IconSizeType.large),
                          ),
                          onPressed: () {
                            Get.to(() => const WalletScreen());
                          },
                        ),
                        // Notification button
                        IconButton(
                          icon: Icon(
                            Icons.notifications,
                            color: Colors.white,
                            size: ResponsiveUtils.iconSize(context, IconSizeType.large),
                          ),
                          onPressed: () {},
                        ),
                        // Support button
                        IconButton(
                          icon: Icon(
                            Icons.headset_mic_outlined,
                            color: Colors.white,
                            size: ResponsiveUtils.iconSize(context, IconSizeType.large),
                          ),
                          onPressed: () => _makePhoneCall('7499465693'),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
