import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

import 'package:kisankonnect_rider/utils/responsive_utils.dart';

class LanguageSelectorWidget extends StatelessWidget {
  const LanguageSelectorWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<LocalizationService>(
      builder: (localizationService) {
        return Container(
          margin: EdgeInsets.symmetric(
            horizontal: ResponsiveUtils.spacingM(context),
            vertical: ResponsiveUtils.spacingS(context),
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
            boxShadow: [
              BoxShadow(
                color: AppColors.shadowLight,
                blurRadius: ResponsiveUtils.width(context, 1),
                offset: Offset(0, ResponsiveUtils.height(context, 0.2)),
              ),
            ],
          ),
          child: <PERSON>umn(
            children: [
              // Header
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
                decoration: BoxDecoration(
                  color: AppColors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
                    topRight: Radius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
                  ),
                ),
                child: Text(
                  'Select Language / भाषा चुनें / भाषा निवडा',
                  style: TextStyle(
                    fontSize: ResponsiveUtils.fontSize(context, 16),
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              // Language Options
              _buildLanguageOption(
                context,
                'English',
                'en',
                localizationService.isEnglish,
                localizationService,
              ),
              _buildDivider(context),
              _buildLanguageOption(
                context,
                'हिंदी',
                'hi',
                localizationService.isHindi,
                localizationService,
              ),
              _buildDivider(context),
              _buildLanguageOption(
                context,
                'मराठी',
                'mr',
                localizationService.isMarathi,
                localizationService,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLanguageOption(
    BuildContext context,
    String languageName,
    String languageCode,
    bool isSelected,
    LocalizationService localizationService,
  ) {
    return InkWell(
      onTap: () {
        localizationService.setLanguage(languageCode);
        Get.back(); // Close the dialog/bottom sheet
      },
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
        child: Row(
          children: [
            // Language name
            Expanded(
              child: Text(
                languageName,
                style: TextStyle(
                  fontSize: ResponsiveUtils.fontSize(context, 16),
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  color: isSelected ? AppColors.green : AppColors.textPrimary,
                ),
              ),
            ),

            // Selection indicator
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: AppColors.green,
                size: ResponsiveUtils.iconSize(context, IconSizeType.medium),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDivider(BuildContext context) {
    return Divider(
      height: 1,
      thickness: 1,
      color: AppColors.borderLight.withValues(alpha: 0.5),
      indent: ResponsiveUtils.spacingM(context),
      endIndent: ResponsiveUtils.spacingM(context),
    );
  }

  // Static method to show language selector as bottom sheet
  static void showLanguageSelector(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.large)),
            topRight: Radius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.large)),
          ),
        ),
        child: const LanguageSelectorWidget(),
      ),
    );
  }

  // Static method to show language selector as dialog
  static void showLanguageDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
        ),
        child: const LanguageSelectorWidget(),
      ),
    );
  }
}
