import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/auth_models.dart';
import '../services/all_services.dart';
import '../routes/app_pages.dart';
import '../constants/storage_keys.dart';
import '../utils/performance_monitor.dart';
import '../utils/error_handler.dart';
import 'profile_controller.dart';

class AuthController extends GetxController {
  final ApiService _apiService = ApiService.instance;
  final SecureStorageService _storage = SecureStorageService.instance;

  // Observable state
  final Rx<AuthState> _authState = AuthState().obs;
  AuthState get authState => _authState.value;

  // Getters
  bool get isLoggedIn => _authState.value.isLoggedIn;
  UserProfile? get currentUser => _authState.value.user;
  bool get isLoading => _authState.value.isLoading;
  String? get error => _authState.value.error;

  // Storage keys (using centralized keys)
  static const String _userKey = StorageKeys.userProfile;
  static const String _tokenKey = StorageKeys.authToken;
  static const String _mobileKey = StorageKeys.mobileNumber;

  @override
  void onInit() {
    super.onInit();
    _loadStoredAuth();
  }

  /// Load stored authentication data
  Future<void> _loadStoredAuth() async {
    try {
      final userJson = await _storage.readMap(_userKey);
      final token = await _storage.read(_tokenKey);

      if (userJson != null && token != null) {
        final user = UserProfile.fromJson(userJson);
        _updateAuthState(
          isLoggedIn: true,
          user: user,
          token: token,
        );
        debugPrint('👤 Loaded stored auth for: ${user.mobileNumber}');
      }
    } catch (e) {
      debugPrint('🚨 Error loading stored auth: $e');
      clearAuth();
    }
  }

  /// Send OTP to mobile number using KisanKonnect API (SMS by default)
  Future<bool> sendOtp(String mobileNumber, {String channel = 'sms'}) async {
    try {
      _updateAuthState(isLoading: true, error: null);

      final response = channel == 'whatsapp'
          ? await _apiService.auth.sendWhatsAppOtp(mobileNumber)
          : await _apiService.auth.sendOtp(mobileNumber);

      if (response.isSuccess && response.data != null) {
        final loginResponse = response.data!;

        // Store mobile number, OTP, registration status, and approval status for verification
        await _storage.write(_mobileKey, mobileNumber);
        await _storage.write(StorageKeys.storedOtp, loginResponse.riderDetail.otp.toString());
        await _storage.write(StorageKeys.regStatus, loginResponse.riderDetail.regStatus.toString());
        await _storage.write(StorageKeys.approvalStatus, loginResponse.riderDetail.approvalStatus.toString());

        _updateAuthState(isLoading: false);

        debugPrint('📱 OTP Response: ${loginResponse.msg}');
        debugPrint('📱 Registration Status: ${loginResponse.riderDetail.regStatus}');
        debugPrint('📱 OTP: ${loginResponse.riderDetail.otp}');

        // Check if regStatus is 3 (profile complete) - if so, we can navigate to dashboard after OTP verification
        if (loginResponse.riderDetail.regStatus == 3) {
          debugPrint('📱 Profile is complete (regStatus: 3) - will navigate to dashboard after OTP verification');
        }

        Get.snackbar(
          'Success',
          'OTP sent to $mobileNumber',
          backgroundColor: Colors.green,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
        );

        return true;
      } else {
        _updateAuthState(isLoading: false, error: response.error);

        ErrorHandler.handleApiError(
          error: response.error ?? 'Failed to send OTP',
          showAsSnackbar: true,
          onRetry: () async => await sendOtp(mobileNumber),
        );

        return false;
      }
    } catch (e) {
      _updateAuthState(isLoading: false, error: e.toString());

      Get.snackbar(
        'Error',
        'An unexpected error occurred: ${e.toString()}',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );

      return false;
    }
  }

  /// Verify OTP and handle authentication using new login flow
  Future<bool> verifyOtp(String otp, {String? gcmId, String? imei}) async {
    try {
      _updateAuthState(isLoading: true, error: null);

      final mobileNumber = await _storage.read(_mobileKey);

      if (mobileNumber == null) {
        throw Exception('Mobile number not found. Please try again.');
      }

      debugPrint('🔐 Verifying OTP for mobile: $mobileNumber');

      // Get the regStatus and approvalStatus from storage (from the initial login API call)
      final storedRegStatus = await _storage.read(StorageKeys.regStatus);
      final storedApprovalStatus = await _storage.read(StorageKeys.approvalStatus);
      final storedOtp = await _storage.read(StorageKeys.storedOtp);

      final regStatus = int.tryParse(storedRegStatus ?? '0') ?? 0;
      final approvalStatus = int.tryParse(storedApprovalStatus ?? '0') ?? 0;
      final expectedOtp = storedOtp ?? '';

      debugPrint('🔐 Verifying OTP: $otp against expected: $expectedOtp');
      debugPrint('🔐 Retrieved regStatus from storage: $regStatus');
      debugPrint('🔐 Retrieved approvalStatus from storage: $approvalStatus');

      // Verify OTP using the login API (FE_RidersLoginNewV1)
      final loginSuccess = await _verifyOtpWithLoginAPI(mobileNumber, otp, gcmId, imei);

      if (loginSuccess) {
        debugPrint('🔐 OTP verification successful via FE_RidersLoginNewV1 API');
        debugPrint('🔐 Using regStatus from login response: $regStatus');
        debugPrint('🔐 Approval Status: $approvalStatus');

        // Handle navigation based on both regStatus and approvalStatus
        if (regStatus == 3 && approvalStatus == 1) {
          debugPrint('🔐 regStatus 3 and approvalStatus 1 (verified) - navigating to dashboard');

          // Create user profile for dashboard navigation
          final userProfile = UserProfile(
            mobileNumber: mobileNumber,
            regStatus: regStatus,
            approvalStatus: approvalStatus,
            isActive: true,
            isVerified: true,
          );

          // Store authentication data
          await _storeAuthData(userProfile, 'auth_token_${DateTime.now().millisecondsSinceEpoch}');

          _updateAuthState(
            isLoggedIn: true,
            user: userProfile,
            token: 'auth_token_${DateTime.now().millisecondsSinceEpoch}',
            isLoading: false,
          );

          // Initialize ProfileController to load server progress
          _initializeProfileController();

          // Navigate to dashboard immediately
          Get.offAllNamed(AppRoutes.dashboard);

          // Fetch and store rider details (this will get the detailed info we need)
          _fetchRiderDetails(mobileNumber);

          Get.snackbar(
            'Success',
            'Login successful! Welcome to dashboard.',
            backgroundColor: Colors.green,
            colorText: Colors.white,
            snackPosition: SnackPosition.TOP,
          );

          return true;
        } else if (regStatus == 3 && approvalStatus != 1) {
          debugPrint(
              '🔐 RegStatus is 3 but approvalStatus is $approvalStatus - registration pending approval or rejected');

          // Create user profile but don't navigate to dashboard
          final userProfile = UserProfile(
            mobileNumber: mobileNumber,
            regStatus: regStatus,
            approvalStatus: approvalStatus,
            isActive: true,
            isVerified: true,
          );

          // Store authentication data
          await _storeAuthData(userProfile, 'auth_token_${DateTime.now().millisecondsSinceEpoch}');

          _updateAuthState(
            isLoggedIn: true,
            user: userProfile,
            token: 'auth_token_${DateTime.now().millisecondsSinceEpoch}',
            isLoading: false,
          );

          // Navigate to delivery partner welcome screen
          Get.offAllNamed(AppRoutes.deliveryPartnerWelcome);

          Get.snackbar(
            'Registration Complete',
            'Your registration is complete but pending approval. Please wait for admin approval.',
            backgroundColor: Colors.orange,
            colorText: Colors.white,
            snackPosition: SnackPosition.BOTTOM,
            duration: const Duration(seconds: 4),
          );

          return true;
        } else {
          // For regStatus 1 or 2, navigate directly to appropriate step
          debugPrint('🔐 RegStatus is $regStatus - navigating to appropriate registration step');

          // Create user profile with actual regStatus
          final userProfile = UserProfile(
            mobileNumber: mobileNumber,
            regStatus: regStatus,
            approvalStatus: approvalStatus,
            isActive: true,
            isVerified: true,
          );

          // Store authentication data
          await _storeAuthData(userProfile, 'auth_token_${DateTime.now().millisecondsSinceEpoch}');

          _updateAuthState(
            isLoggedIn: true,
            user: userProfile,
            token: 'auth_token_${DateTime.now().millisecondsSinceEpoch}',
            isLoading: false,
          );

          debugPrint('🔐 OTP verification successful');
          debugPrint('🔐 Registration Status: $regStatus');
          debugPrint('🔐 Navigating to appropriate step based on regStatus');

          // Initialize ProfileController to load server progress
          _initializeProfileController();

          // Navigate based on actual registration status and approval status from login response
          _navigateAfterAuth(regStatus, approvalStatus: approvalStatus);

          String message = '';
          switch (regStatus) {
            case 1:
              message = 'Profile created! Continue with document upload.';
              break;
            case 2:
              message = 'Documents uploaded! Complete work details.';
              break;
            default:
              message = 'Continue registration process.';
              break;
          }

          Get.snackbar(
            'Continue Registration',
            message,
            backgroundColor: Colors.blue,
            colorText: Colors.white,
            snackPosition: SnackPosition.BOTTOM,
          );

          return true;
        }
      } else {
        // API call failed
        _updateAuthState(isLoading: false, error: 'Invalid OTP or verification failed');

        Get.snackbar(
          'Error',
          'Invalid OTP. Please try again.',
          backgroundColor: Colors.red,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
        );

        return false;
      }
    } catch (e) {
      _updateAuthState(isLoading: false, error: e.toString());

      Get.snackbar(
        'Error',
        'Verification failed. Please try again.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
      );

      return false;
    }
  }

  /// Verify OTP using the login API (FE_RidersLoginNewV1)
  Future<bool> _verifyOtpWithLoginAPI(String mobileNumber, String otp, String? gcmId, String? imei) async {
    try {
      debugPrint('🔄 Verifying OTP using login API (FE_RidersLoginNewV1)');

      // Call the login API with OTP for verification
      final response = await _apiService.auth.verifyOtp(
        mobileNumber: mobileNumber,
        otp: otp,
        gcmId: gcmId,
        imei: imei,
      );

      if (response.isSuccess && response.data != null) {
        debugPrint('✅ OTP verification successful');
        final loginResponse = response.data!;

        // Extract user details from login response
        final riderDetail = loginResponse.riderDetail;

        // Use default values since RiderDetail from login API doesn't have all fields
        // These will be updated later when we fetch rider details
        final userId = '0'; // Will be fetched later
        final riderName = '${riderDetail.regStatus}-${riderDetail.approvalStatus}'; // Temporary
        final userName = 'user'; // Default
        final dcId = '0'; // Will be fetched later
        final kfhStatus = '0'; // Default value

        debugPrint('👤 User ID from API: $userId');
        debugPrint('👤 Rider Name: $riderName');
        debugPrint('👤 User Name: $userName');
        debugPrint('👤 DC ID: $dcId');

        // Store user ID and other info in secure storage
        await _storage.write(StorageKeys.riderId, userId);
        await _storage.write(StorageKeys.riderName, riderName);
        await _storage.write(StorageKeys.userName, userName);
        await _storage.write(StorageKeys.dcId, dcId);
        await _storage.write(StorageKeys.kfhStatus, kfhStatus);

        // Store user ID in memory for immediate access (will be updated later)
        // _userId = userId; // Commented out since we don't have the actual userId yet

        return true; // Verification successful
      } else {
        debugPrint('❌ OTP verification failed: ${response.error}');
        // Store fallback values
        await _storage.write(StorageKeys.riderId, '0');
        await _storage.write(StorageKeys.riderName, 'Rider');
        await _storage.write(StorageKeys.userName, 'user');
        await _storage.write(StorageKeys.dcId, '0');
        await _storage.write(StorageKeys.kfhStatus, '0');
        return false; // Verification failed
      }
    } catch (e) {
      debugPrint('🚨 Error verifying OTP: $e');
      // Store fallback values
      await _storage.write(StorageKeys.riderId, '0');
      await _storage.write(StorageKeys.riderName, 'Rider');
      await _storage.write(StorageKeys.userName, 'user');
      await _storage.write(StorageKeys.dcId, '0');
      await _storage.write(StorageKeys.kfhStatus, '0');
      return false; // Exception occurred
    }
  }

  /// Fetch and store rider details in background
  Future<void> _fetchRiderDetails(String mobileNumber) async {
    try {
      debugPrint('🔄 Fetching rider details for: $mobileNumber');

      // First, fetch detailed rider info using FE_SRInfoAfterLogin API
      await _fetchDetailedRiderInfo(mobileNumber);

      // Then fetch rider details using FE_GetRiderDetails API
      final riderDetailsService = RiderDetailsService.instance;
      final riderDetails = await riderDetailsService.fetchAndStoreRiderDetails(mobileNumber);

      if (riderDetails != null && riderDetails.isSuccess) {
        debugPrint('✅ Rider details fetched and stored successfully');

        // Log the rider name and mobile for verification
        if (riderDetails.riderDetail != null) {
          final detail = riderDetails.riderDetail!;
          debugPrint('👤 Rider Name: ${detail.fullName}');
          debugPrint('📱 Rider Mobile: ${detail.mobileNo}');
        }
      } else {
        debugPrint('❌ Failed to fetch rider details');
      }
    } catch (e) {
      debugPrint('🚨 Error fetching rider details: $e');
      // Don't show error to user since this is background operation
    }
  }

  /// Fetch detailed rider info using FE_SRInfoAfterLogin API
  Future<void> _fetchDetailedRiderInfo(String mobileNumber) async {
    try {
      debugPrint('🔄 Fetching detailed rider info using FE_SRInfoAfterLogin API');

      // Get stored OTP for the API call
      final storedOtp = await _storage.read(StorageKeys.storedOtp) ?? '';

      if (storedOtp.isEmpty) {
        debugPrint('❌ No stored OTP found for FE_SRInfoAfterLogin API');
        return;
      }

      // Call the FE_SRInfoAfterLogin API
      final response = await _apiService.auth.fetchAndStoreRiderInfoAfterLogin(
        mobileNo: mobileNumber,
        otp: storedOtp,
        gcmId: '', // Optional
        imei: '', // Optional - will use device ID from ApiHelper
      );

      if (response.isSuccess) {
        debugPrint('✅ Detailed rider info fetched and stored successfully');
      } else {
        debugPrint('❌ Failed to fetch detailed rider info: ${response.error}');
      }
    } catch (e) {
      debugPrint('🚨 Error fetching detailed rider info: $e');
      // Don't show error to user since this is background operation
    }
  }

  /// Initialize ProfileController
  void _initializeProfileController() {
    try {
      // Check if ProfileController is already registered
      if (Get.isRegistered<ProfileController>()) {
        debugPrint('✅ ProfileController already registered');
        return;
      }

      // Create ProfileController if not found
      Get.put(ProfileController());
      debugPrint('✅ ProfileController created and registered');
    } catch (e) {
      debugPrint('🚨 Error initializing ProfileController: $e');
      // Force create ProfileController as fallback
      try {
        Get.delete<ProfileController>(force: true);
        Get.put(ProfileController());
        debugPrint('✅ ProfileController force created');
      } catch (fallbackError) {
        debugPrint('🚨 Fallback ProfileController creation failed: $fallbackError');
      }
    }
  }

  /// Navigate after successful authentication based on registration status and approval status
  ///
  /// API Flow Mapping:
  /// regStatus 0: Profile not created → Start profile creation (Step 0)
  /// regStatus 1: Basic profile created via RiderRegisterationInsertProfile → Continue to documents (Step 6)
  /// regStatus 2: Documents uploaded via RiderRegisterationInsertDocument → Continue to work details (Step 7)
  /// regStatus 3 + approvalStatus 3: Work details added + approved → Go to dashboard
  /// regStatus 3 + approvalStatus != 3: Work details added but pending approval → Show registration status
  void _navigateAfterAuth(int regStatus, {int approvalStatus = 0}) {
    debugPrint('🚀 Navigating after auth - regStatus: $regStatus, approvalStatus: $approvalStatus');

    switch (regStatus) {
      case 0:
        // Profile not created - start profile creation from beginning
        debugPrint('🚀 regStatus 0: Profile not created - starting profile creation');
        Get.offAllNamed(AppRoutes.createProfile, arguments: {'initialStep': 0});
        break;
      case 1:
        // Basic profile created (RiderRegisterationInsertProfile completed)
        // Continue to document upload
        debugPrint('🚀 regStatus 1: Basic profile created - continuing to document upload');
        Get.offAllNamed(AppRoutes.createProfile, arguments: {'initialStep': 6});
        break;
      case 2:
        // Documents uploaded (RiderRegisterationInsertDocument completed)
        // Continue to work details
        debugPrint('🚀 regStatus 2: Documents uploaded - continuing to work details');
        Get.offAllNamed(AppRoutes.createProfile, arguments: {'initialStep': 7});
        break;
      case 3:
        // Check approval status for final navigation
        if (approvalStatus == 1) {
          // Registration complete and verified - go to dashboard
          debugPrint('🚀 regStatus 3 + approvalStatus 1: Verified - navigating to dashboard');
          Get.offAllNamed(AppRoutes.dashboard);
        } else {
          // Registration complete but pending approval (0) or rejected (2) - show welcome screen
          debugPrint('🚀 regStatus 3 + approvalStatus $approvalStatus: Showing delivery partner welcome screen');
          Get.offAllNamed(AppRoutes.deliveryPartnerWelcome);
        }
        break;
      default:
        // Unknown status - start from beginning
        debugPrint('🚀 Unknown regStatus: $regStatus - starting from beginning');
        Get.offAllNamed(AppRoutes.createProfile, arguments: {'initialStep': 0});
        break;
    }
  }

  /// Public method to navigate based on status (for use by splash screen)
  void navigateBasedOnStatus(int regStatus, int approvalStatus) {
    _navigateAfterAuth(regStatus, approvalStatus: approvalStatus);
  }

  /// Store authentication data
  Future<void> _storeAuthData(UserProfile user, String? token) async {
    await _storage.writeMap(_userKey, user.toJson());
    if (token != null) {
      await _storage.write(_tokenKey, token);
    }
  }

  // Removed updateProfile method - not used in login/registration flow

  /// Update user profile
  Future<void> updateUserProfile(UserProfile updatedProfile) async {
    try {
      // Store updated profile
      await _storage.writeMap(_userKey, updatedProfile.toJson());

      // Update state
      _updateAuthState(
        isLoggedIn: true,
        user: updatedProfile,
        token: _authState.value.token,
        isLoading: false,
      );

      debugPrint('👤 User profile updated: regStatus = ${updatedProfile.regStatus}');
    } catch (e) {
      debugPrint('🚨 Error updating user profile: $e');
    }
  }

  /// Logout user with optimized performance
  Future<void> logout() async {
    try {
      // Show loading state immediately
      _updateAuthState(isLoading: true);

      debugPrint('🚪 Starting logout process...');
      PerformanceMonitor.startTimer('logout');

      // Step 1: Clear profile creation data (fast operation)
      if (Get.isRegistered<ProfileController>()) {
        Get.find<ProfileController>().clearData(); // Use faster in-memory clear
      }

      // Step 2: Batch delete storage keys for better performance
      await _batchDeleteStorageKeys();

      // Step 3: Update auth state
      _updateAuthState(
        isLoggedIn: false,
        user: null,
        token: null,
        isLoading: false,
        error: null,
      );

      final elapsedMs = PerformanceMonitor.stopTimer('logout');
      debugPrint('🚪 Logout completed in ${elapsedMs}ms');
      debugPrint('📊 ${PerformanceMonitor.getLogoutPerformanceStatus()}');

      // Step 4: Navigate to login
      Get.offAllNamed(AppRoutes.login);

      // Show success message
      Get.snackbar(
        'Logged Out',
        'You have been successfully logged out',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      debugPrint('🚨 Error during logout: $e');

      // Even if there's an error, ensure user is logged out
      _updateAuthState(
        isLoggedIn: false,
        user: null,
        token: null,
        isLoading: false,
        error: null,
      );

      Get.offAllNamed(AppRoutes.login);

      Get.snackbar(
        'Logout',
        'Logged out with some cleanup issues',
        backgroundColor: Colors.orange,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// Optimized batch delete for storage keys
  Future<void> _batchDeleteStorageKeys() async {
    try {
      // Combine all keys to delete
      final allKeysToDelete = [
        ...StorageKeys.logoutClearKeys,
        'total_hour_shift',
        'number_enable',
        'weekoff_days',
        'sr_shift',
        'lat_long_data',
      ];

      debugPrint('🗑️ Deleting ${allKeysToDelete.length} storage keys...');

      // Use the optimized batch delete method
      await _storage.batchDelete(allKeysToDelete);

      debugPrint('🗑️ Storage cleanup completed');
    } catch (e) {
      debugPrint('🚨 Error in batch delete: $e');
      // Don't rethrow - logout should continue even if cleanup fails
    }
  }

  /// Clear authentication data
  void clearAuth() {
    _updateAuthState(
      isLoggedIn: false,
      user: null,
      token: null,
      isLoading: false,
      error: null,
    );
  }

  /// Get stored login info from local storage
  Future<Map<String, String?>> getStoredLoginInfo() async {
    return {
      'rider_id': await _storage.read(StorageKeys.riderId),
      'rider_name': await _storage.read(StorageKeys.riderName),
      'user_name': await _storage.read(StorageKeys.userName),
      'password': await _storage.read(StorageKeys.password),
      'user_type': await _storage.read(StorageKeys.userType),
      'dc_id': await _storage.read(StorageKeys.dcId),
      'latitude': await _storage.read(StorageKeys.latitude),
      'longitude': await _storage.read(StorageKeys.longitude),
      'application_version_sr': await _storage.read(StorageKeys.applicationVersionSr),
      'area_in_meter': await _storage.read(StorageKeys.areaInMeter),
      'download_date': await _storage.read(StorageKeys.downloadDate),
      'dunzo_key': await _storage.read(StorageKeys.dunzoKey),
      'dunzo_token': await _storage.read(StorageKeys.dunzoToken),
      'tata_token': await _storage.read(StorageKeys.tataToken),
      'api_token': await _storage.read(StorageKeys.apiToken),
      'locus_client': await _storage.read(StorageKeys.locusClient),
      'locus_user': await _storage.read(StorageKeys.locusUser),
      'locus_password': await _storage.read(StorageKeys.locusPassword),
      'login_status': await _storage.read(StorageKeys.loginStatus),
      'login_status_dc': await _storage.read(StorageKeys.loginStatusDc),
      'image_status': await _storage.read(StorageKeys.imageStatus),
      'store_status': await _storage.read(StorageKeys.storeStatus),
      'order_delivery': await _storage.read(StorageKeys.orderDelivery),
      'order_assign': await _storage.read(StorageKeys.orderAssign),
      'kfh_status': await _storage.read(StorageKeys.kfhStatus),
      'dc_status': await _storage.read(StorageKeys.dcStatus),
      'doc_status': await _storage.read(StorageKeys.docStatus),
      'total_hour_shift': await _storage.read('total_hour_shift'),
      'number_enable': await _storage.read('number_enable'),
      'weekoff_days': await _storage.read('weekoff_days'),
      'sr_shift': await _storage.read('sr_shift'),
      'lat_long_data': await _storage.read('lat_long_data'),
    };
  }

  /// Get specific login info value
  Future<String?> getLoginInfo(String key) async {
    return await _storage.read(key);
  }

  /// Update authentication state
  void _updateAuthState({
    bool? isLoggedIn,
    UserProfile? user,
    String? token,
    bool? isLoading,
    String? error,
  }) {
    _authState.value = _authState.value.copyWith(
      isLoggedIn: isLoggedIn,
      user: user,
      token: token,
      isLoading: isLoading,
      error: error,
    );
  }

  /// Get stored mobile number
  Future<String?> getStoredMobileNumber() async {
    return await _storage.read(_mobileKey);
  }

  /// Check if user needs to complete profile
  bool get needsProfileCompletion {
    return isLoggedIn && (currentUser?.isProfileComplete != true);
  }

  /// Get authentication headers for API calls
  Map<String, String> get authHeaders {
    final token = _authState.value.token;
    if (token != null) {
      return {
        'Authorization': 'Bearer $token',
      };
    }
    return {};
  }
}
