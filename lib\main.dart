import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:kisankonnect_rider/l10n/app_localizations.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/routes/app_pages.dart';
import 'package:kisankonnect_rider/bindings/app_bindings.dart';
import 'package:kisankonnect_rider/config/flavor_config.dart';

/// Default main entry point
/// Uses development flavor by default for easier development
/// For production builds, use main_prod.dart
/// For development with device preview, use main_dev.dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize default environment (development)
  await Environment.init(flavor: Flavor.development);

  // Initialize all dependencies using centralized bindings
  InitialBindings().dependencies();

  // Initialize Maps Service
  await _initializeMapsService();

  runApp(const MyApp());
}

/// Initialize Maps Service
Future<void> _initializeMapsService() async {
  try {
    await MapsService.instance.initialize();
  } catch (e) {
    debugPrint('❌ Error initializing Maps Service: $e');
  }
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      theme: appTheme,
      initialRoute: AppRoutes.splash,
      getPages: AppPages.pages,
      initialBinding: AppBindings(),
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: LocalizationService.supportedLocales,
      fallbackLocale: LocalizationService.fallbackLocale,
      locale: Get.find<LocalizationService>().locale,
    );
  }
}
