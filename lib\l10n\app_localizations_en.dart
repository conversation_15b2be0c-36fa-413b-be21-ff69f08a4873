// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'KisanKonnect Rider';

  @override
  String get profile => 'Profile';

  @override
  String get riderName => 'Rider Name';

  @override
  String get phoneNumber => '+91- 98388 89898';

  @override
  String get idCard => 'ID Card';

  @override
  String get cashBalance => 'Cash Balance';

  @override
  String get myShift => 'My Shift';

  @override
  String get myKFHLocation => 'My KFH Location';

  @override
  String get tripHistory => 'Trip History';

  @override
  String get kisanStore => 'Kissan Store';

  @override
  String get newTrends => 'New trends';

  @override
  String get referAndEarn => 'Refer & Earn';

  @override
  String get referralBonusText => '10,000+ riders are earning referral bonus';

  @override
  String get helpAndSupport => 'Help & Support';

  @override
  String get appVersion => 'App version v0.1.10';

  @override
  String get featuredProducts => 'Featured products';

  @override
  String get bigDiscount => 'BIG Discount';

  @override
  String get orderNow => 'Order Now';

  @override
  String get cartReview => 'Cart review';

  @override
  String get cartDetails => 'Cart details';

  @override
  String get wallet => 'Wallet';

  @override
  String get kisanKonnectWallet => 'KisanKonnect Wallet';

  @override
  String get kisanKash => 'KisanKash';

  @override
  String get balance => 'Balance';

  @override
  String get add => 'Add';

  @override
  String get redeem => 'Redeem';

  @override
  String get selectPaymentMode => 'Select your payment mode';

  @override
  String get cardsUPINetbanking => 'Cards/UPI/Netbanking';

  @override
  String get payWithUPI => 'Pay with UPI';

  @override
  String get useAnyUPIApp => 'Use any UPI app on your phone to pay';

  @override
  String get payNow => 'Pay Now';

  @override
  String get orderPlacedSuccessfully => 'Your order placed\nsuccessfully';

  @override
  String get continueShopping => 'Continue Shopping';

  @override
  String get viewMyOrders => 'View My Orders';

  @override
  String get selectAddress => 'Select Address';

  @override
  String get selectYourLocation => 'Select your location';

  @override
  String get getUpdatedOnWhatsapp => 'Get updated on Whatsapp';

  @override
  String get continueButton => 'Continue';

  @override
  String get selectPreferableFilter => 'Select your preferable filter';

  @override
  String get tripAscending => 'Trip in Ascending order';

  @override
  String get tripDescending => 'Trip in Descending order';

  @override
  String get onlySuccessTrip => 'Only Success Trip';

  @override
  String get onlyFailedTrip => 'Only Failed Trip';

  @override
  String get okay => 'Okay';

  @override
  String get myEarnings => 'My Earnings';

  @override
  String get today => 'Today';

  @override
  String get thisWeek => 'This week';

  @override
  String get thisMonth => 'This month';

  @override
  String get totalEarningsOfTheDay => 'Total\'s earnings of the day';

  @override
  String get totalEarningsOfThePreviousDay =>
      'Total\'s earnings of the previous day';

  @override
  String get orderDelivered => 'Order Delivered';

  @override
  String get orderEarning => 'Order Earning';

  @override
  String get rainSurgeEarning => 'Rain Surge Earning';

  @override
  String get totalIncentive => 'Total Incentive';

  @override
  String get incentiveSubtitle => 'Applied if you work on Sat and Sun';

  @override
  String get doMoreOrdersAndEarn => 'Do 15 more orders and earn';

  @override
  String get viewAllEarningsAndIncentives => 'View all earnings & incentives';

  @override
  String get orders => 'orders';

  @override
  String get referFriendAndEarn => 'Refer a friend an earn';

  @override
  String get yourFriendGets => 'Your friend gets';

  @override
  String get onJoining => ' on joining!';

  @override
  String get totalReferralEarnings => 'Total referral earnings: ₹5500';

  @override
  String get friendsReferred => '2 friends referred';

  @override
  String get shareYourReferralCode => 'Share your referral code';

  @override
  String get howItWorks => 'How it works';

  @override
  String get referInSimpleSteps => 'Refer in 3 simple steps';

  @override
  String get copyCodeOrShareViaWhatsapp => 'Copy Code or Share via Whatsapp';

  @override
  String get completeTheTarget => 'Complete the Target';

  @override
  String get enjoyTheBonus => 'Enjoy the Bonus';

  @override
  String get yourReferrals => 'Your referrals';

  @override
  String get pending => 'Pending';

  @override
  String get success => 'Success';

  @override
  String get failed => 'Failed';

  @override
  String get inviteViaWhatsApp => 'Invite via WhatsApp';

  @override
  String get referralCodeCopied => 'Referral code copied to clipboard!';

  @override
  String joinKisanKonnectMessage(String code) {
    return '🎉 Join KisanKonnect and earn ₹10,000 on joining!\n\nUse my referral code: $code\n\nDownload the app and start earning today!';
  }
}
