# Custom Icon Setup Guide

## Overview

The shift info section now uses a custom image asset instead of the default Material Design `Icons.access_time` icon.

## Changes Made

### 1. Code Changes
- **File**: `lib/view/screens/home/<USER>/shift_info_section.dart`
- **Line**: 193-198
- **Change**: Replaced `Icon(Icons.access_time)` with `Image.asset('assets/icons/access_time.png')`

### 2. Asset Configuration
- **File**: `pubspec.yaml`
- **Addition**: Added `- assets/icons/` to the assets section
- **Directory**: Created `assets/icons/` directory

## Required Action

### Step 1: Add Your Clock Image
1. Save your clock icon image as `access_time.png`
2. Place it in the `assets/icons/` directory
3. Ensure the image has a transparent background

### Step 2: Image Specifications
- **Format**: PNG (recommended for transparency)
- **Size**: 24x24px or 48x48px (will be scaled automatically)
- **Background**: Transparent
- **Color**: Single color (black or white recommended)
- **Style**: Simple, clean design that works at small sizes

### Step 3: Test the Implementation
```bash
flutter pub get
flutter run
```

## Code Implementation

### Before (Material Icon)
```dart
Icon(
  Icons.access_time,
  color: widget.isOnline ? AppColors.green : Colors.grey.shade600,
  size: iconSize,
),
```

### After (Custom Image Asset)
```dart
Image.asset(
  'assets/icons/access_time.png',
  width: iconSize,
  height: iconSize,
  color: widget.isOnline ? AppColors.green : Colors.grey.shade600,
),
```

## Features

### Dynamic Color Tinting
The custom icon automatically changes color based on rider status:
- **Online**: Green (`AppColors.green`)
- **Offline**: Grey (`Colors.grey.shade600`)

### Responsive Sizing
The icon size is responsive and uses `ResponsiveUtils.iconSize(context, IconSizeType.medium)` for consistent sizing across different screen sizes.

## Benefits

1. **Custom Branding**: Use your own clock icon design
2. **Consistent Styling**: Matches your app's visual identity
3. **Dynamic Theming**: Automatically tints based on status
4. **Responsive**: Scales appropriately for different devices

## Troubleshooting

### Image Not Showing
1. Verify the image is saved as `assets/icons/access_time.png`
2. Run `flutter pub get` after adding the image
3. Check that the image has the correct format (PNG)
4. Ensure the image path in code matches the actual file location

### Image Quality Issues
1. Use a high-resolution image (48x48px or higher)
2. Ensure the image has a transparent background
3. Use PNG format for best quality with transparency

### Color Tinting Not Working
1. Ensure the image is a single color (black or white)
2. The image should have transparent background
3. Avoid images with multiple colors or gradients

## File Structure
```
assets/
└── icons/
    ├── access_time.png  (Your clock icon - ADD THIS FILE)
    └── README.md        (Documentation)
```

## Next Steps

1. **Add the Image**: Place your clock icon as `assets/icons/access_time.png`
2. **Test**: Run the app to see the custom icon in action
3. **Adjust**: If needed, modify the image size or design for optimal appearance

The implementation is ready - you just need to add your custom clock icon image to complete the setup!
