import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

import '../../widgets/loading/elegant_dashboard_shimmer.dart';
import '../../widgets/dialogs/exit_confirmation_dialog.dart';
import 'sections/dashboard_header.dart';
import 'sections/shift_info_section.dart';
import 'sections/earning_badges_section.dart';
import 'sections/banner_carousel_section.dart';
import 'sections/earnings_summary_section.dart';
import 'sections/incentive_banner_section.dart';
import 'sections/orders_section.dart';
import 'sections/todays_progress_section.dart';
import 'sections/pending_assets_section.dart';
import 'sections/referral_banner_section.dart';
import 'sections/referral_bonus_section.dart';
import 'sections/reports_section.dart';
import 'sections/dashboard_bottom_nav.dart';
import 'earning/earning_content.dart';
import '../pay_now/pay_now_content.dart';
import 'profile/profile_screen.dart';
import 'sections/refer_earn_content.dart';
import 'package:get/get.dart';
import '../../../controllers/rider_status_controller.dart';
import '../../../controllers/dashboard_story_controller.dart';
import '../../../controllers/reports_controller.dart';
import '../../../controllers/earnings_controller.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  late final int? initialTab;
  late final Map<String, dynamic>? userData;
  late final RiderStatusController _riderStatusController;
  bool _isLoading = true;
  int _currentTabIndex = 0;

  @override
  void initState() {
    super.initState();
    final args = Get.arguments;
    initialTab = args?['initialTab'];
    userData = args?['userData'];

    // Initialize rider status controller
    _riderStatusController = Get.find<RiderStatusController>();

    // Load dashboard data dynamically
    _loadDashboardData();
  }

  void _loadDashboardData() async {
    try {
      // Initialize all dashboard controllers
      final dashboardStoryController = Get.find<DashboardStoryController>();
      final reportsController = Get.find<ReportsController>();
      final earningsController = Get.find<EarningsController>();

      // Load data from all controllers
      await Future.wait([
        dashboardStoryController.fetchDashboardStoryData(),
        reportsController.fetchReportsData(),
        earningsController.refreshEarnings(),
      ]);

      // Hide loading when all data is loaded
      if (mounted) {
        setState(() => _isLoading = false);
      }
    } catch (e) {
      debugPrint('🚨 Error loading dashboard data: $e');
      // Hide loading even on error after a short delay
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) setState(() => _isLoading = false);
      });
    }
  }

  void _showGoOfflineDialog() async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return Center(
          child: Material(
            color: Colors.transparent,
            child: Container(
              width: 390,
              height: 475,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(24),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Align(
                    alignment: Alignment.topRight,
                    child: GestureDetector(
                      onTap: () => Get.back(result: false),
                      child: const Icon(Icons.close, size: 28, color: Colors.black54),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    width: 90,
                    height: 90,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        colors: [Color(0xFFFF6A3D), Color(0xFFFFC371)],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ),
                    ),
                    child: const Center(
                      child: Icon(Icons.power_settings_new, size: 54, color: Colors.white),
                    ),
                  ),
                  const SizedBox(height: 32),
                  Text(
                    AppStrings.get('goOfflineConfirm'),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ),
                  ),
                  const Spacer(),
                  SizedBox(
                    width: double.infinity,
                    height: 56,
                    child: ElevatedButton(
                      onPressed: () => Get.back(result: true),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.green,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(32),
                        ),
                        elevation: 0,
                      ),
                      child: Text(
                        AppStrings.get('yesGoOffline'),
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  SizedBox(
                    width: double.infinity,
                    height: 56,
                    child: OutlinedButton(
                      onPressed: () => Get.back(result: false),
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: AppColors.green, width: 2),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(32),
                        ),
                      ),
                      child: Text(
                        AppStrings.get('cancel'),
                        style: TextStyle(
                          color: AppColors.green,
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
    if (result == true) {
      await _riderStatusController.goOffline(customRemark: 'User requested to go offline');
    }
  }

  void _showGoOnlineDialog() async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return Center(
          child: Material(
            color: Colors.transparent,
            child: Container(
              width: 390,
              height: 475,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(24),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Align(
                    alignment: Alignment.topRight,
                    child: GestureDetector(
                      onTap: () => Get.back(result: false),
                      child: const Icon(Icons.close, size: 28, color: Colors.black54),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    width: 90,
                    height: 90,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        colors: [AppColors.green, Color(0xFF66BB6A)],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ),
                    ),
                    child: const Center(
                      child: Icon(Icons.power_settings_new, size: 54, color: Colors.white),
                    ),
                  ),
                  const SizedBox(height: 32),
                  Text(
                    AppStrings.get('goOnlineConfirm'),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    AppStrings.get('goOnlineSubtitle'),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: Colors.grey,
                    ),
                  ),
                  const Spacer(),
                  SizedBox(
                    width: double.infinity,
                    height: 56,
                    child: ElevatedButton(
                      onPressed: () => Get.back(result: true),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.green,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(32),
                        ),
                        elevation: 0,
                      ),
                      child: Text(
                        AppStrings.get('yesGoOnline'),
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  SizedBox(
                    width: double.infinity,
                    height: 56,
                    child: OutlinedButton(
                      onPressed: () => Get.back(result: false),
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: AppColors.green, width: 2),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(32),
                        ),
                      ),
                      child: Text(
                        AppStrings.get('cancel'),
                        style: TextStyle(
                          color: AppColors.green,
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
    if (result == true) {
      await _riderStatusController.goOnline(customRemark: 'User requested to go online');
    }
  }

  Widget _getBodyContent() {
    switch (_currentTabIndex) {
      case 0: // Home
        return Obx(() => Column(
              children: [
                DashboardHeader(
                  isOnline: _riderStatusController.isOnline,
                  onToggle: (val) {
                    if (!val) {
                      _showGoOfflineDialog();
                    } else {
                      _showGoOnlineDialog();
                    }
                  },
                ),
                // Fixed shift info section
                ShiftInfoSection(isOnline: _riderStatusController.isOnline),
                // Scrollable content below
                Expanded(
                  child: SafeArea(
                    top: false, // Don't add top padding since header handles it
                    child: SingleChildScrollView(
                      child: _riderStatusController.isOnline
                          ? const _OnlineDashboardBodyContent()
                          : const _OfflineDashboardBodyContent(),
                    ),
                  ),
                ),
              ],
            ));
      case 1: // Earning
        return SafeArea(
          child: EarningContent(
            onBackPressed: () {
              setState(() {
                _currentTabIndex = 0; // Go back to home tab
              });
            },
          ),
        );
      case 2: // Pay Now
        return SafeArea(
          child: PayNowContent(
            onBackPressed: () {
              setState(() {
                _currentTabIndex = 0; // Go back to home tab
              });
            },
          ),
        );
      case 3: // Refer & Earn
        return SafeArea(child: ReferEarnScreen());
      case 4: // Profile
        return const SafeArea(child: ProfileScreen());
      default:
        return Obx(() => Column(
              children: [
                DashboardHeader(
                  isOnline: _riderStatusController.isOnline,
                  onToggle: (val) {
                    if (!val) {
                      _showGoOfflineDialog();
                    } else {
                      _showGoOnlineDialog();
                    }
                  },
                ),
                // Fixed shift info section
                ShiftInfoSection(isOnline: _riderStatusController.isOnline),
                // Scrollable content below
                Expanded(
                  child: SafeArea(
                    top: false, // Don't add top padding since header handles it
                    child: SingleChildScrollView(
                      child: _riderStatusController.isOnline
                          ? const _OnlineDashboardBodyContent()
                          : const _OfflineDashboardBodyContent(),
                    ),
                  ),
                ),
              ],
            ));
    }
  }

  /// Handle back button press to show exit confirmation
  Future<bool> _onWillPop() async {
    // Only show exit confirmation on the home tab (index 0)
    if (_currentTabIndex == 0) {
      return await ExitConfirmationDialog.show();
    } else {
      // For other tabs, go back to home tab
      setState(() {
        _currentTabIndex = 0;
      });
      return false; // Don't exit the app
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false, // Prevent default back behavior
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop) {
          final shouldPop = await _onWillPop();
          if (shouldPop && context.mounted) {
            Navigator.of(context).pop();
          }
        }
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF7F8FA),
        bottomNavigationBar: DashboardBottomNav(
          currentIndex: _currentTabIndex,
          onTap: (index) {
            setState(() {
              _currentTabIndex = index;
            });
          },
        ),
        body: _isLoading ? const ElegantDashboardShimmer() : _getBodyContent(),
      ),
    );
  }
}

class _OnlineDashboardBodyContent extends StatelessWidget {
  const _OnlineDashboardBodyContent();
  @override
  Widget build(BuildContext context) {
    const sectionSpacing = 12.0; // Fixed spacing for more compact layout

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Add top spacing since shift info is now fixed above
        const SizedBox(height: sectionSpacing),

        const EarningBadgesSection(),
        const SizedBox(height: sectionSpacing),

        const BannerCarouselSection(),
        const SizedBox(height: sectionSpacing),

        const EarningsSummarySection(),
        const SizedBox(height: sectionSpacing),

        // const IncentiveBannerSection(),
        // const SizedBox(height: sectionSpacing),

        const OrdersSection(),
        const SizedBox(height: sectionSpacing),

        const TodaysProgressSection(),
        const SizedBox(height: sectionSpacing),

        const PendingAssetsSection(),
        const SizedBox(height: sectionSpacing),

        const ReferralBannerSection(),
        const SizedBox(height: sectionSpacing),

        const ReferralBonusSection(),
        const SizedBox(height: sectionSpacing),

        const ReportsCarousel(),
        const SizedBox(height: 24), // Extra spacing at bottom
      ],
    );
  }
}

class _OfflineDashboardBodyContent extends StatelessWidget {
  const _OfflineDashboardBodyContent();
  @override
  Widget build(BuildContext context) {
    const sectionSpacing = 12.0; // Fixed spacing for more compact layout

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Add top spacing since shift info is now fixed above
        const SizedBox(height: sectionSpacing),

        const EarningBadgesSection(),
        const SizedBox(height: sectionSpacing),

        const BannerCarouselSection(),
        const SizedBox(height: sectionSpacing),

        const EarningsSummarySection(),
        const SizedBox(height: sectionSpacing),

        const IncentiveBannerSection(),
        const SizedBox(height: sectionSpacing),

        const TodaysProgressSection(),
        const SizedBox(height: sectionSpacing),

        const ReferralBonusSection(),
        const SizedBox(height: sectionSpacing),

        const ReportsCarousel(),
        const SizedBox(height: 24), // Extra spacing at bottom
      ],
    );
  }
}
