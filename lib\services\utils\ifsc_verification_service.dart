import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

/// Service for IFSC code verification using Razorpay IFSC API
class IFSCVerificationService {
  static final Dio _dio = Dio();

  // Use centralized endpoint configuration
  static String get _baseUrl => ApiEndpoints.ifscVerification;

  /// Verify IFSC code and return bank details
  /// Returns null if IFSC is invalid or API call fails
  static Future<IFSCDetails?> verifyIFSC(String ifscCode) async {
    try {
      if (ifscCode.isEmpty || ifscCode.length != 11) {
        debugPrint('❌ Invalid IFSC code format: $ifscCode');
        return null;
      }

      final url = '$_baseUrl/$ifscCode';
      debugPrint('🔍 Verifying IFSC: $ifscCode');
      debugPrint('🌐 API URL: $url');

      final response = await _dio.get(
        url,
        options: Options(
          // connectTimeout: const Duration(seconds: 10),
          receiveTimeout: const Duration(seconds: 10),
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200 && response.data != null) {
        debugPrint('✅ IFSC verification successful');
        debugPrint('📄 Response data: ${response.data}');

        return IFSCDetails.fromJson(response.data);
      } else {
        debugPrint('❌ IFSC verification failed - Status: ${response.statusCode}');
        return null;
      }
    } on DioException catch (e) {
      debugPrint('🚨 Dio error during IFSC verification: ${e.message}');
      debugPrint('🚨 Error type: ${e.type}');
      debugPrint('🚨 Response: ${e.response?.data}');

      // Handle specific error cases
      if (e.response?.statusCode == 404) {
        debugPrint('❌ IFSC code not found');
      } else if (e.type == DioExceptionType.connectionTimeout) {
        debugPrint('❌ Connection timeout');
      } else if (e.type == DioExceptionType.receiveTimeout) {
        debugPrint('❌ Receive timeout');
      }

      return null;
    } catch (e) {
      debugPrint('🚨 Unexpected error during IFSC verification: $e');
      return null;
    }
  }
}

/// Model class for IFSC details response
class IFSCDetails {
  final String bank;
  final String ifsc;
  final String branch;
  final String address;
  final String contact;
  final String city;
  final bool rtgs;
  final bool neft;
  final bool imps;
  final bool upi;
  final String state;
  final String district;
  final String? micr;
  final String? centre;

  IFSCDetails({
    required this.bank,
    required this.ifsc,
    required this.branch,
    required this.address,
    required this.contact,
    required this.city,
    required this.rtgs,
    required this.neft,
    required this.imps,
    required this.upi,
    required this.state,
    required this.district,
    this.micr,
    this.centre,
  });

  factory IFSCDetails.fromJson(Map<String, dynamic> json) {
    return IFSCDetails(
      bank: json['BANK']?.toString() ?? '',
      ifsc: json['IFSC']?.toString() ?? '',
      branch: json['BRANCH']?.toString() ?? '',
      address: json['ADDRESS']?.toString() ?? '',
      contact: json['CONTACT']?.toString() ?? '',
      city: json['CITY']?.toString() ?? '',
      rtgs: json['RTGS'] == true,
      neft: json['NEFT'] == true,
      imps: json['IMPS'] == true,
      upi: json['UPI'] == true,
      state: json['STATE']?.toString() ?? '',
      district: json['DISTRICT']?.toString() ?? '',
      micr: json['MICR']?.toString(),
      centre: json['CENTRE']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'BANK': bank,
      'IFSC': ifsc,
      'BRANCH': branch,
      'ADDRESS': address,
      'CONTACT': contact,
      'CITY': city,
      'RTGS': rtgs,
      'NEFT': neft,
      'IMPS': imps,
      'UPI': upi,
      'STATE': state,
      'DISTRICT': district,
      'MICR': micr,
      'CENTRE': centre,
    };
  }

  @override
  String toString() {
    return 'IFSCDetails(bank: $bank, ifsc: $ifsc, branch: $branch, city: $city, state: $state)';
  }

  /// Get formatted services available
  String get availableServices {
    List<String> services = [];
    if (rtgs) services.add('RTGS');
    if (neft) services.add('NEFT');
    if (imps) services.add('IMPS');
    if (upi) services.add('UPI');
    return services.join(', ');
  }
}

/// Enum for IFSC verification states
enum IFSCVerificationState {
  unverified,
  verifying,
  verified,
  failed,
}
