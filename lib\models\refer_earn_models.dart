import 'package:flutter/material.dart';

/// Refer and Earn Response Model
class ReferEarnResponse {
  final String status;
  final String msg;
  final ReferEarnData referEarnData;

  ReferEarnResponse({
    required this.status,
    required this.msg,
    required this.referEarnData,
  });

  bool get isSuccess => status == "200";

  factory ReferEarnResponse.fromJson(Map<String, dynamic> json) {
    return ReferEarnResponse(
      status: json['status']?.toString() ?? '',
      msg: json['msg']?.toString() ?? '',
      referEarnData: ReferEarnData.fromJson(
        json['usP_FE_RefferealAndEarn'] as Map<String, dynamic>? ?? {},
      ),
    );
  }
}

/// Refer and Earn Data Model (matches usP_FE_RefferealAndEarn structure)
class ReferEarnData {
  final String banner;
  final int totalReferralEarning;
  final int friendsReferred;
  final String shareYourReferralCode;
  final String msg1;
  final String msg2;
  final List<ReferralItem> yourReferrals;

  ReferEarnData({
    required this.banner,
    required this.totalReferralEarning,
    required this.friendsReferred,
    required this.shareYourReferralCode,
    required this.msg1,
    required this.msg2,
    required this.yourReferrals,
  });

  factory ReferEarnData.fromJson(Map<String, dynamic> json) {
    return ReferEarnData(
      banner: json['banner']?.toString() ?? '',
      totalReferralEarning: int.tryParse(json['totalReferralEarning']?.toString() ?? '0') ?? 0,
      friendsReferred: int.tryParse(json['friendsreferred']?.toString() ?? '0') ?? 0,
      shareYourReferralCode: json['shareyourreferralcode']?.toString() ?? '',
      msg1: json['msg1']?.toString() ?? '',
      msg2: json['msg2']?.toString() ?? '₹10000',
      yourReferrals: (json['yourReferrals'] as List<dynamic>?)
              ?.map((item) => ReferralItem.fromJson(item as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  // Convenience getters
  String get referralCode => shareYourReferralCode;
  String get totalEarnings => totalReferralEarning.toString();
  String get totalReferrals => friendsReferred.toString();
  String get friendAmount => msg2.replaceAll('₹', '');
  String get bannerImage => banner;
}

/// Referral Item Model
class ReferralItem {
  final String name;
  final String date;
  final String status;
  final String amount;
  final String mobileNo;

  ReferralItem({
    required this.name,
    required this.date,
    required this.status,
    required this.amount,
    required this.mobileNo,
  });

  factory ReferralItem.fromJson(Map<String, dynamic> json) {
    return ReferralItem(
      name: json['name']?.toString() ?? '',
      date: json['date']?.toString() ?? '',
      status: json['status']?.toString() ?? '',
      amount: json['amount']?.toString() ?? '--',
      mobileNo: json['mobileNo']?.toString() ?? '',
    );
  }

  Color get statusColor {
    switch (status.toLowerCase()) {
      case 'success':
      case 'completed':
        return const Color(0xFF4CAF50);
      case 'pending':
        return const Color(0xFFFF9800);
      case 'failed':
      case 'rejected':
        return const Color(0xFFF44336);
      default:
        return const Color(0xFF9E9E9E);
    }
  }
}
