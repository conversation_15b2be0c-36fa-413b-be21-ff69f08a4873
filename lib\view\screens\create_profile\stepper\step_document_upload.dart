import 'dart:io';
import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import '../../../../controllers/profile_registration_controller.dart';

class StepDocumentUpload extends StatefulWidget {
  final Function(Map<String, dynamic>)? onContinue;
  final VoidCallback? onBack;
  const StepDocumentUpload({super.key, this.onContinue, this.onBack});

  @override
  State<StepDocumentUpload> createState() => _StepDocumentUploadState();
}

class _StepDocumentUploadState extends State<StepDocumentUpload> {
  late final Function(Map<String, dynamic>) onContinue;
  late final VoidCallback? onBack;

  final _panController = TextEditingController();
  final _licenseController = TextEditingController();

  final ImagePicker _picker = ImagePicker();
  bool _isLoading = false;

  // Document images
  File? _panImage;
  File? _licenseImage;
  File? _selfieImage;

  late ProfileRegistrationController _registrationController;

  /// Check if all required documents are uploaded
  bool get _areAllDocumentsUploaded {
    return _panImage != null &&
        _licenseImage != null &&
        _selfieImage != null &&
        _panController.text.trim().isNotEmpty &&
        _licenseController.text.trim().isNotEmpty;
  }

  @override
  void initState() {
    super.initState();
    final args = Get.arguments;
    onContinue = args?['onContinue'] ?? widget.onContinue ?? (data) {};
    onBack = args?['onBack'] ?? widget.onBack;

    // Initialize ProfileRegistrationController
    try {
      _registrationController = Get.find<ProfileRegistrationController>();
    } catch (e) {
      debugPrint('🚨 ProfileRegistrationController not found, creating new instance: $e');
      _registrationController = Get.put(ProfileRegistrationController());
    }
  }

  @override
  void dispose() {
    _panController.dispose();
    _licenseController.dispose();
    super.dispose();
  }

  Future<void> _pickImage(String docType) async {
    // Show dialog first
    await _showInfoDialog(docType);

    // Then show image source selection
    final ImageSource? source = await _showImageSourceDialog();
    if (source == null) return;

    try {
      final XFile? image = await _picker.pickImage(
        source: source,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          switch (docType) {
            case 'pan':
              _panImage = File(image.path);
              break;
            case 'license':
              _licenseImage = File(image.path);
              break;
            case 'selfie':
              _selfieImage = File(image.path);
              break;
          }
        });
      }
    } catch (e) {
      Get.snackbar('Error', 'Failed to pick image: $e');
    }
  }

  Future<ImageSource?> _showImageSourceDialog() async {
    return await showDialog<ImageSource>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Image Source'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('Camera'),
              onTap: () => Navigator.pop(context, ImageSource.camera),
            ),
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('Gallery'),
              onTap: () => Navigator.pop(context, ImageSource.gallery),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showInfoDialog(String docType) async {
    String title = '';
    IconData icon = Icons.credit_card;
    List<String> tips = [];

    if (docType == 'pan') {
      title = 'Upload your PAN Card';
      icon = Icons.credit_card;
      tips = [
        'Please ensure your name, PAN number, and date of birth are clearly displayed.',
        'Please ensure you\'re uploading your PAN. Don\'t upload someone else\'s PAN.',
        'We ensure it will be 100% safe.'
      ];
    } else if (docType == 'license') {
      title = 'Upload your Driving License';
      icon = Icons.credit_card;
      tips = [
        'Please ensure your name, Driving License and date of birth are clearly displayed.',
        'Please ensure you\'re uploading your Driving License. Don\'t upload someone else\'s License.',
        'We ensure it will be 100% safe.'
      ];
    } else if (docType == 'selfie') {
      title = 'Take your Selfie';
      icon = Icons.camera_alt;
      tips = [
        'Please ensure your face is clearly visible and well-lit.',
        'Look directly at the camera and keep a neutral expression.',
        'Make sure there are no obstructions covering your face.',
        'We ensure it will be 100% safe.'
      ];
    }
    await showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          child: Container(
            width: 390,
            height: 398,
            padding: const EdgeInsets.all(0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Header
                Container(
                  height: 56,
                  decoration: const BoxDecoration(
                    color: Color(0xFFF7F8FA),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                    ),
                  ),
                  child: Stack(
                    children: [
                      Align(
                        alignment: Alignment.center,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(icon, color: AppColors.green, size: 32),
                            const SizedBox(width: 8),
                            Text(title, style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
                          ],
                        ),
                      ),
                      Align(
                        alignment: Alignment.topRight,
                        child: IconButton(
                          icon: const Icon(Icons.close, color: Colors.black54),
                          onPressed: () => Get.back(),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                ...tips.map((t) => Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Icon(Icons.check_circle, color: AppColors.green, size: 22),
                          const SizedBox(width: 10),
                          Expanded(child: Text(t, style: const TextStyle(fontSize: 15))),
                        ],
                      ),
                    )),
                const Spacer(),
                Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: SizedBox(
                    width: double.infinity,
                    height: 48,
                    child: ElevatedButton(
                      onPressed: () => Get.back(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.green,
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(32)),
                      ),
                      child: const Text('Done', style: TextStyle(fontSize: 18, color: Colors.white)),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Validate that all required documents are uploaded
  bool _validateDocuments() {
    List<String> missingDocuments = [];

    if (_panImage == null) {
      missingDocuments.add('PAN Card');
    }
    if (_licenseImage == null) {
      missingDocuments.add('Driving License');
    }
    if (_selfieImage == null) {
      missingDocuments.add('Selfie Image');
    }

    // Also validate document numbers
    if (_panController.text.trim().isEmpty) {
      missingDocuments.add('PAN Card Number');
    }
    if (_licenseController.text.trim().isEmpty) {
      missingDocuments.add('Driving License Number');
    }

    if (missingDocuments.isNotEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Please upload and fill details for: ${missingDocuments.join(', ')}',
          ),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
        ),
      );
      return false;
    }

    return true;
  }

  // Removed unused _buildProgressIndicator method

  /// Confirm documents and call RiderRegisterationInsertDocument API
  Future<void> _confirmDocuments() async {
    // First validate that all documents are uploaded
    if (!_validateDocuments()) {
      return;
    }

    try {
      setState(() {
        _isLoading = true;
      });

      debugPrint('📄 Confirming documents and calling API...');

      // Update document paths in controller
      final documentData = {
        'panCard': _panImage?.path ?? '',
        'drivingLicense': _licenseImage?.path ?? '',
        'selfie': _selfieImage?.path ?? '',
        'panNumber': _panController.text,
        'dlNumber': _licenseController.text,
      };

      // Update document paths in controller
      _registrationController.updateDocumentPath('panCard', _panImage?.path ?? '');
      _registrationController.updateDocumentPath('drivingLicense', _licenseImage?.path ?? '');
      _registrationController.updateDocumentPath('selfie', _selfieImage?.path ?? '');

      // Update PAN and DL numbers in profile data
      _registrationController.updateMultipleProfileData({
        'panNumber': _panController.text,
        'dlNumber': _licenseController.text,
      });

      // Call the RiderRegisterationInsertDocument API
      final success = await _registrationController.submitDocuments();

      setState(() {
        _isLoading = false;
      });

      if (success) {
        debugPrint('✅ Documents confirmed and API call successful');

        // Call the parent's onContinue to move to next step
        widget.onContinue?.call(documentData);
      } else {
        debugPrint('❌ Document API call failed');

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to upload documents. Please try again.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('🚨 Error confirming documents: $e');

      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildDocumentCard({
    required String title,
    required String docType,
    required File? image,
    TextEditingController? controller,
    required String hint,
  }) {
    final bool isUploaded = image != null && (controller?.text.trim().isNotEmpty ?? true);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                title,
                style: AppTextTheme.cardTitle,
              ),
            ),
            if (isUploaded)
              Container(
                padding: const EdgeInsets.all(4),
                decoration: const BoxDecoration(
                  color: Colors.green,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 16,
                ),
              )
            else
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.red.shade100,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.close,
                  color: Colors.red.shade600,
                  size: 16,
                ),
              ),
          ],
        ),
        const SizedBox(height: 12),
        GestureDetector(
          onTap: () => _pickImage(docType),
          child: Container(
            width: double.infinity,
            height: 120,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.green, width: 2),
            ),
            child: image == null
                ? Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: AppColors.green.withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.file_upload_outlined,
                          size: 32,
                          color: AppColors.green,
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'Delivery',
                        style: TextStyle(
                          color: AppColors.green,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  )
                : ClipRRect(
                    borderRadius: BorderRadius.circular(10),
                    child: Image.file(
                      image,
                      fit: BoxFit.cover,
                      width: double.infinity,
                      height: double.infinity,
                    ),
                  ),
          ),
        ),
        const SizedBox(height: 12),
        if (controller != null) // Only show text field for documents that need number input
          TextFormField(
            controller: controller,
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: AppTextTheme.inputHint,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.grey),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.grey),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppColors.green),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            ),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FA),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Upload your documents',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'Please upload correct documents',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.black54,
                ),
              ),
              // const SizedBox(height: 16),

              // // Progress indicator
              // _buildProgressIndicator(),
              const SizedBox(height: 24),

              // First row: PAN Card and Driving License
              Row(
                children: [
                  Expanded(
                    child: _buildDocumentCard(
                      title: '1. PAN Card',
                      docType: 'pan',
                      image: _panImage,
                      controller: _panController,
                      hint: 'Enter PAN card no',
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildDocumentCard(
                      title: '2. Driving License',
                      docType: 'license',
                      image: _licenseImage,
                      controller: _licenseController,
                      hint: 'Enter driving license no',
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 32),

              // Second row: Selfie Image (centered)
              Row(
                children: [
                  Expanded(
                    child: _buildDocumentCard(
                      title: '3. Selfie Image',
                      docType: 'selfie',
                      image: _selfieImage,
                      controller: null, // No text input for selfie
                      hint: '',
                    ),
                  ),
                  const SizedBox(width: 16),
                  const Expanded(child: SizedBox()), // Empty space to center selfie
                ],
              ),
              const SizedBox(height: 100), // Space for bottom button
            ],
          ),
        ),
      ),
      bottomNavigationBar: Container(
        padding: EdgeInsets.only(
          left: 16,
          right: 16,
          bottom: MediaQuery.of(context).viewInsets.bottom + 16,
          top: 16,
        ),
        decoration: const BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 4,
              offset: Offset(0, -2),
            ),
          ],
        ),
        child: SizedBox(
          width: double.infinity,
          height: 56,
          child: ElevatedButton(
            onPressed: (_isLoading || !_areAllDocumentsUploaded) ? null : _confirmDocuments,
            style: ElevatedButton.styleFrom(
              backgroundColor: _areAllDocumentsUploaded ? AppColors.green : Colors.grey,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(32),
              ),
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Text(
                    _areAllDocumentsUploaded ? 'Continue' : 'Upload all documents to continue',
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
          ),
        ),
      ),
    );
  }
}
