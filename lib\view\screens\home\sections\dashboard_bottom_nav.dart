import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:get/get.dart';

class DashboardBottomNav extends StatelessWidget {
  final int currentIndex;
  final ValueChanged<int> onTap;

  const DashboardBottomNav({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<LocalizationService>(
      builder: (localizationService) {
        return BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          currentIndex: currentIndex,
          selectedItemColor: AppColors.green,
          unselectedItemColor: Colors.grey,
          onTap: onTap,
          items: [
            BottomNavigationBarItem(icon: const Icon(Icons.home), label: AppStrings.get('home')),
            BottomNavigationBarItem(icon: const Icon(Icons.account_balance_wallet), label: AppStrings.get('earnings')),
            BottomNavigationBarItem(icon: const Icon(Icons.payment), label: AppStrings.get('payNowTab')),
            BottomNavigationBarItem(icon: const Icon(Icons.group), label: AppStrings.get('referAndEarn')),
            BottomNavigationBarItem(icon: const Icon(Icons.person), label: AppStrings.get('profile')),
          ],
        );
      },
    );
  }
}
