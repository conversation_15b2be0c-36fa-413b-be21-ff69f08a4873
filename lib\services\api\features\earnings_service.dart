import 'package:kisankonnect_rider/services/all_services.dart';

/// Simple Earnings Service
class EarningsService {
  static EarningsService? _instance;
  static EarningsService get instance => _instance ??= EarningsService._();

  late ApiHelper _apiHelper;

  EarningsService._() {
    _apiHelper = ApiHelper.instance;
  }

  Future<ApiResponse<dynamic>> getRiderWeeklyEarnings({
    required String date,
    required String srid,
    required String kfhId,
    required String cdcType,
  }) async {
    return await _apiHelper.get<dynamic>(
      ApiEndpoints.weeklyEarnings,
      queryParameters: {
        'Date': date,
        'SRID': int.tryParse(srid) ?? 0,
        'KFHID': int.tryParse(kfhId) ?? 0,
        'CDCtype': int.tryParse(cdcType) ?? 0,
      },
    );
  }

  Future<ApiResponse<dynamic>> getServiceLevelReport({
    required String date,
    required String dcId,
    required String cdcType,
  }) async {
    return await _apiHelper.get<dynamic>(
      '/Rider/FE_KFHServiceLevelReportForDCIncharge',
      queryParameters: {
        'Date': date,
        'DCID': dcId,
        'CDCType': cdcType,
      },
    );
  }

  /// Get rider service level report (individual rider report)
  Future<ApiResponse<dynamic>> getRiderServiceLevelReport({
    required String date,
    required String dcId,
    required String cdcType,
    required String riderId,
  }) async {
    return await _apiHelper.get<dynamic>(
      '/Rider/FE_KFHRiderServiceLevelReport',
      queryParameters: {
        'Date': date,
        'DCID': dcId,
        'CDCType': cdcType,
        'RiderID': riderId,
      },
    );
  }
}
