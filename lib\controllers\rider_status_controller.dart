import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import '../models/rider_status_request.dart';
import '../utils/error_handler.dart';

class RiderStatusController extends GetxController {
  final ApiService _apiService = ApiService.instance;
  late final SecureStorageService _storage;

  // Observable status
  final RxBool _isOnline = false.obs;
  final RxBool _isLoading = false.obs;

  // Getters
  bool get isOnline => _isOnline.value;
  bool get isLoading => _isLoading.value;

  @override
  void onInit() {
    super.onInit();
    _storage = Get.find<SecureStorageService>();
    _loadStoredStatus();
  }

  /// Load stored online/offline status from local storage
  void _loadStoredStatus() async {
    try {
      final storedStatus = await _storage.read('rider_online_status');
      if (storedStatus != null) {
        _isOnline.value = storedStatus.toLowerCase() == 'true';
        debugPrint('📱 Loaded rider status from storage: ${_isOnline.value ? "Online" : "Offline"}');
      } else {
        // Default to offline if no stored status
        _isOnline.value = false;
        debugPrint('📱 No stored rider status found, defaulting to offline');
      }
    } catch (e) {
      debugPrint('❌ Error loading rider status: $e');
      _isOnline.value = false;
    }
  }

  /// Save status to local storage
  void _saveStatus(bool status) async {
    try {
      await _storage.write('rider_online_status', status.toString());
      debugPrint('📱 Saved rider status to storage: ${status ? "Online" : "Offline"}');
    } catch (e) {
      debugPrint('❌ Error saving rider status: $e');
    }
  }

  /// Get user data from stored login information
  Future<Map<String, dynamic>> _getUserData() async {
    try {
      final userid = await _storage.read('userid');
      final dcid = await _storage.read('dcid');
      final cdcType = await _storage.read('cdcType');

      final userData = {
        'userid': int.tryParse(userid ?? '') ?? 40796255, // Default fallback
        'dcid': int.tryParse(dcid ?? '') ?? 67056057, // Default fallback
        'cdcType': int.tryParse(cdcType ?? '') ?? 67056057, // Default fallback
      };

      debugPrint('📱 Retrieved user data: $userData');
      return userData;
    } catch (e) {
      debugPrint('❌ Error getting user data: $e');
      // Return default values if error
      return {
        'userid': 40796255,
        'dcid': 67056057,
        'cdcType': 67056057,
      };
    }
  }

  /// Toggle rider status (online/offline)
  Future<bool> toggleStatus({String? customRemark}) async {
    if (_isLoading.value) {
      debugPrint('📱 Status change already in progress, ignoring toggle');
      return false;
    }

    _isLoading.value = true;

    try {
      final userData = await _getUserData();
      final newStatus = !_isOnline.value;

      debugPrint('📱 Attempting to change status to: ${newStatus ? "Online" : "Offline"}');

      // Create the appropriate request
      final request = newStatus
          ? RiderStatusRequest.goOnline(
              userid: userData['userid'],
              dcid: userData['dcid'],
              cdcType: userData['cdcType'],
              customRemark: customRemark,
            )
          : RiderStatusRequest.goOffline(
              userid: userData['userid'],
              dcid: userData['dcid'],
              cdcType: userData['cdcType'],
              customRemark: customRemark,
            );

      debugPrint('📱 Sending request: $request');

      // Make API call
      final response = await _apiService.shift.updateRiderStatus(
        deliveryDate: request.deliveryDate,
        userid: request.userid.toString(),
        remark: request.remark,
        dcid: request.dcid.toString(),
        cdcType: request.cdcType.toString(),
        status: request.status.toString(),
      );

      if (response.isSuccess && response.data != null) {
        final statusResponse = RiderStatusResponse.fromJson(response.data);

        if (statusResponse.isSuccess) {
          // Update local status
          _isOnline.value = newStatus;
          _saveStatus(newStatus);

          debugPrint('✅ Status changed successfully to: ${newStatus ? "Online" : "Offline"}');

          // Show success message
          Get.snackbar(
            newStatus ? 'You are now Online' : 'You are now Offline',
            statusResponse.msg,
            backgroundColor: newStatus ? const Color(0xFF4CAF50) : const Color(0xFF757575),
            colorText: const Color(0xFFFFFFFF),
            snackPosition: SnackPosition.BOTTOM,
            duration: const Duration(seconds: 2),
          );

          return true;
        } else {
          debugPrint('❌ API returned error: ${statusResponse.msg}');
          ErrorHandler.showErrorSnackbar(
            title: 'Status Change Failed',
            message: statusResponse.msg,
          );
          return false;
        }
      } else {
        debugPrint('❌ API call failed: ${response.error}');
        ErrorHandler.handleApiError(
          error: response.error ?? 'Failed to update status',
          showAsSnackbar: true,
          onRetry: () async => await toggleStatus(),
        );
        return false;
      }
    } catch (e) {
      debugPrint('❌ Exception during status change: $e');
      ErrorHandler.showErrorSnackbar(
        title: 'Error',
        message: 'An unexpected error occurred while updating your status.',
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Go online with optional custom remark
  Future<bool> goOnline({String? customRemark}) async {
    if (_isOnline.value) {
      debugPrint('📱 Already online, ignoring request');
      return true;
    }
    return await toggleStatus(customRemark: customRemark);
  }

  /// Go offline with optional custom remark
  Future<bool> goOffline({String? customRemark}) async {
    if (!_isOnline.value) {
      debugPrint('📱 Already offline, ignoring request');
      return true;
    }
    return await toggleStatus(customRemark: customRemark);
  }

  /// Force set status without API call (for testing or initialization)
  void setStatusLocally(bool status) {
    _isOnline.value = status;
    _saveStatus(status);
    debugPrint('📱 Status set locally to: ${status ? "Online" : "Offline"}');
  }
}
