import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/utils/asset_optimizer.dart';

class OrdersSection extends StatelessWidget {
  const OrdersSection({super.key});
  @override
  Widget build(BuildContext context) {
    // Using ResponsiveUtils for consistent sizing
    final horizontalPadding = ResponsiveUtils.spacingM(context); // 4% of screen width
    final containerMargin = ResponsiveUtils.spacingM(context); // 4% of screen width
    final containerPadding = ResponsiveUtils.spacingM(context); // 4% of screen width
    final containerRadius = ResponsiveUtils.borderRadius(context, BorderRadiusType.medium); // 4% of screen width
    final pendingOrderHeight =
        ResponsiveUtils.containerHeight(context, ContainerHeightType.small); // 6% of screen height
    final orderCardHeight =
        ResponsiveUtils.containerHeight(context, ContainerHeightType.medium); // 10% of screen height
    final iconSize = ResponsiveUtils.iconSize(context, IconSizeType.large); // Responsive icon size
    final pendingIconSize = ResponsiveUtils.iconSize(context, IconSizeType.large); // Responsive icon size

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header outside the container
        Padding(
          padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
          child: Row(
            children: [
              Text(
                AppStrings.get('orders'),
                style: AppTextTheme.cardTitle,
              ),
              const Spacer(),
            ],
          ),
        ),

        const SizedBox(height: 8), // Reduced spacing

        // Container content
        Container(
          width: ResponsiveUtils.width(context, 100) - (containerMargin * 2),
          margin: EdgeInsets.symmetric(horizontal: containerMargin),
          padding: EdgeInsets.all(containerPadding),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(containerRadius),
            boxShadow: [
              BoxShadow(
                color: AppColors.shadowLight,
                blurRadius: ResponsiveUtils.width(context, 2),
                offset: Offset(0, ResponsiveUtils.height(context, 0.2)),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Responsive pending orders box
              Container(
                height: pendingOrderHeight,
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(color: const Color(0xFFFF3B30), width: 1.2),
                  borderRadius: BorderRadius.circular(containerRadius * 0.6),
                ),
                child: Row(
                  children: [
                    SizedBox(width: ResponsiveUtils.spacingS(context)),
                    Text(
                      '${AppStrings.get('pendingOrders')} - 10',
                      style: AppTextTheme.cardSubtitle.copyWith(
                        color: const Color(0xFFFF3B30),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Spacer(),
                    Padding(
                      padding: EdgeInsets.only(right: ResponsiveUtils.spacingS(context)),
                      child: AssetOptimizer.getOptimizedIcon(
                        'online_store',
                        size: pendingIconSize,
                        color: AppColors.green,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: ResponsiveUtils.height(context, 1.5)),
              // Responsive order cards
              Row(
                children: [
                  Expanded(
                    child: OrderCard(
                      label: AppStrings.get('scanOrders'),
                      imagePath: 'assets/images/orders/barcode.png',
                      height: orderCardHeight,
                      iconSize: iconSize,
                    ),
                  ),
                  SizedBox(width: ResponsiveUtils.spacingS(context)),
                  Expanded(
                    child: OrderCard(
                      label: AppStrings.get('selfAssign'),
                      imagePath: 'assets/images/orders/checklist.png',
                      height: orderCardHeight,
                      iconSize: iconSize,
                    ),
                  ),
                  SizedBox(width: ResponsiveUtils.spacingS(context)),
                  Expanded(
                    child: OrderCard(
                      label: AppStrings.get('totalOrders'),
                      imagePath: 'assets/images/orders/logistics-delivery.png',
                      height: orderCardHeight,
                      iconSize: iconSize,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class OrderCard extends StatelessWidget {
  final String label;
  final String imagePath;
  final double height;
  final double iconSize;

  const OrderCard(
      {required this.label, required this.imagePath, required this.height, required this.iconSize, super.key});

  @override
  Widget build(BuildContext context) {
    final cardRadius = ResponsiveUtils.borderRadius(context, BorderRadiusType.small); // Responsive border radius

    return Container(
      height: height,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: AppColors.green, width: 1.2),
        borderRadius: BorderRadius.circular(cardRadius),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AssetOptimizer.getOptimizedIcon(
            imagePath.split('/').last.split('.').first,
            size: iconSize,
            color: AppColors.green,
          ),
          SizedBox(height: ResponsiveUtils.height(context, 1)),
          Text(
            label,
            style: AppTextTheme.cardCaption.copyWith(
              decoration: TextDecoration.none,
              color: AppColors.green,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
