import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../widgets/forms/common_text_field.dart';
import '../../../../controllers/profile_registration_controller.dart';
import '../../../../controllers/common_data_controller.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import '../../../widgets/dialogs/bank_details_skip_dialog.dart';
import '../../../widgets/dialogs/bank_verification_dialogs.dart';

class StepBankDetails extends StatefulWidget {
  final Function(Map<String, String>)? onContinue;
  final VoidCallback? onSkip;
  final VoidCallback? onBack;
  const StepBankDetails({super.key, this.onContinue, this.onSkip, this.onBack});

  @override
  State<StepBankDetails> createState() => _StepBankDetailsState();
}

class _StepBankDetailsState extends State<StepBankDetails> {
  final _formKey = GlobalKey<FormState>();
  final _accountController = TextEditingController();
  final _reAccountController = TextEditingController();
  final _ifscController = TextEditingController();
  String? _selectedBank;
  bool _isLoading = false;

  // IFSC verification state
  IFSCVerificationState _ifscVerificationState = IFSCVerificationState.unverified;
  IFSCDetails? _verifiedIFSCDetails;

  late ProfileRegistrationController _registrationController;
  final CommonDataController _commonDataController = Get.find<CommonDataController>();

  // Dynamic banks from API
  List<String> get _banks {
    final activeBanks = _commonDataController.activeBanks;
    final bankNames = activeBanks.map((bank) => bank.bankName).toList();

    // Add "Other" option at the end
    if (!bankNames.contains('Other')) {
      bankNames.add('Other');
    }

    return bankNames;
  }

  final _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();

    // Initialize ProfileRegistrationController
    try {
      _registrationController = Get.find<ProfileRegistrationController>();
    } catch (e) {
      debugPrint('🚨 ProfileRegistrationController not found, creating new instance: $e');
      _registrationController = Get.put(ProfileRegistrationController());
    }

    // Add listener to IFSC controller to reset verification state when text changes
    _ifscController.addListener(() {
      final currentText = _ifscController.text.trim().toUpperCase();

      // If text is empty or changed from verified IFSC, reset verification
      if (currentText.isEmpty || (_verifiedIFSCDetails != null && currentText != _verifiedIFSCDetails!.ifsc)) {
        if (_ifscVerificationState != IFSCVerificationState.unverified) {
          setState(() {
            _ifscVerificationState = IFSCVerificationState.unverified;
            _verifiedIFSCDetails = null;
          });
        }
      }
    });
  }

  @override
  void dispose() {
    _accountController.dispose();
    _reAccountController.dispose();
    _ifscController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FA),
      resizeToAvoidBottomInset: true,
      body: SafeArea(
        child: SingleChildScrollView(
          controller: _scrollController,
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 16),
                const Text('Enter your bank details', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
                const SizedBox(height: 4),
                const Text('Your earnings will be transferred to this bank account',
                    style: TextStyle(color: Colors.grey)),
                const SizedBox(height: 24),
                _buildTextField(_accountController, 'Account number*',
                    validator: (v) => v!.isEmpty ? 'Required' : null),
                const SizedBox(height: 16),
                _buildTextField(_reAccountController, 'Re-enter account number*',
                    validator: (v) => v != _accountController.text ? 'Account numbers do not match' : null),
                const SizedBox(height: 16),
                // Bank Selection Dropdown
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: DropdownButtonFormField<String>(
                    value: _selectedBank,
                    hint: const Text(
                      'Select Bank*',
                      style: TextStyle(color: Colors.grey),
                    ),
                    items: _banks
                        .map((bank) => DropdownMenuItem(
                              value: bank,
                              child: Text(
                                bank,
                                style: const TextStyle(fontSize: 16),
                              ),
                            ))
                        .toList(),
                    onChanged: (selectedBank) {
                      setState(() {
                        _selectedBank = selectedBank;
                      });
                      debugPrint('🏦 Bank selected: $selectedBank');
                    },
                    decoration: InputDecoration(
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      border: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      focusedBorder: InputBorder.none,
                      errorBorder: InputBorder.none,
                      focusedErrorBorder: InputBorder.none,
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please select a bank';
                      }
                      return null;
                    },
                    dropdownColor: Colors.white,
                    icon: const Icon(Icons.arrow_drop_down, color: AppColors.green),
                  ),
                ),
                const SizedBox(height: 16),
                _buildIFSCField(),
                const SizedBox(height: 32),
                // Action buttons
                Row(
                  children: [
                    // Skip button
                    Expanded(
                      child: OutlinedButton(
                        onPressed: _showSkipDialog,
                        style: OutlinedButton.styleFrom(
                          side: const BorderSide(color: AppColors.green),
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(32)),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: const Text(
                          'Skip for now',
                          style: TextStyle(color: AppColors.green, fontSize: 16),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Confirm button
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _confirmBankDetails,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.green,
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(32)),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: _isLoading
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              )
                            : const Text(
                                'Confirm details',
                                style: TextStyle(fontSize: 16, color: Colors.white),
                              ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Confirm bank details and call RiderRegisterationInsertProfile API
  Future<void> _confirmBankDetails() async {
    try {
      // Safely check form validation
      final formState = _formKey.currentState;
      if (formState == null || !formState.validate()) {
        return;
      }

      // Ensure selected bank is not null
      if (_selectedBank == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Please select a bank'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      setState(() {
        _isLoading = true;
      });

      debugPrint('🏦 Confirming bank details and calling API...');

      // Update bank details in controller
      _registrationController.updateMultipleProfileData({
        'bankName': _selectedBank!,
        'accountNumber': _accountController.text,
        'reAccountNumber': _reAccountController.text,
        'ifscCode': _ifscController.text,
      });

      // Call the RiderRegisterationInsertProfile API
      final success = await _registrationController.submitBasicProfile();

      setState(() {
        _isLoading = false;
      });

      if (success) {
        debugPrint('✅ Bank details confirmed and API call successful');

        // Show bank verification dialog with proper verification
        if (mounted) {
          await BankVerificationDialogs.showBankVerificationDialog(
            context: context,
            accountNumber: _accountController.text,
            ifscCode: _ifscController.text,
            selectedBankName: _selectedBank!,
            onSuccess: () {
              // Call the parent's onContinue to move to next step
              widget.onContinue?.call({
                'bankName': _verifiedIFSCDetails?.bank ?? _selectedBank!,
                'accountNumber': _accountController.text,
                'reAccountNumber': _reAccountController.text,
                'ifscCode': _ifscController.text,
              });
            },
            onFailure: () {
              // Handle verification failure
              debugPrint('❌ Bank verification failed');
            },
            onChangeBankDetails: () {
              // User wants to change bank details - stay on current step
              debugPrint('🔄 User wants to change bank details');
            },
          );
        }
      } else {
        debugPrint('❌ API call failed');

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to register profile. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      debugPrint('🚨 Error confirming bank details: $e');

      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Widget _buildTextField(TextEditingController controller, String hint, {String? Function(String?)? validator}) {
    return CommonTextField(
      controller: controller,
      hint: hint,
      validator: validator,
    );
  }

  /// Build info row for bank details display
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 60,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade800,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build IFSC field with verification button
  Widget _buildIFSCField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: CommonTextField(
                controller: _ifscController,
                hint: 'IFSC Code*',
                validator: (v) {
                  if (v == null || v.isEmpty) return 'Required';
                  if (v.length != 11) return 'IFSC code must be 11 characters';
                  if (_ifscVerificationState == IFSCVerificationState.unverified) {
                    return 'Please verify IFSC code';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 12),
            ValueListenableBuilder<TextEditingValue>(
              valueListenable: _ifscController,
              builder: (context, value, child) {
                return _buildVerificationButton();
              },
            ),
          ],
        ),
        if (_verifiedIFSCDetails != null) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.green.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.green.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.verified,
                      color: Colors.green,
                      size: 18,
                    ),
                    const SizedBox(width: 6),
                    Expanded(
                      child: Text(
                        _verifiedIFSCDetails!.bank,
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          color: Colors.green,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                _buildInfoRow('Branch', _verifiedIFSCDetails!.branch),
                _buildInfoRow('IFSC', _verifiedIFSCDetails!.ifsc),
                _buildInfoRow('City', '${_verifiedIFSCDetails!.city}, ${_verifiedIFSCDetails!.state}'),
                if (_verifiedIFSCDetails!.micr != null) _buildInfoRow('MICR', _verifiedIFSCDetails!.micr!),
                if (_verifiedIFSCDetails!.contact.isNotEmpty) _buildInfoRow('Contact', _verifiedIFSCDetails!.contact),
                const SizedBox(height: 6),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade100,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    'Services: ${_verifiedIFSCDetails!.availableServices}',
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.blue.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  /// Build verification button based on current state
  Widget _buildVerificationButton() {
    final currentText = _ifscController.text.trim().toUpperCase();
    final isValidLength = currentText.length == 11;

    switch (_ifscVerificationState) {
      case IFSCVerificationState.unverified:
        return ElevatedButton(
          onPressed: isValidLength ? _verifyIFSC : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: isValidLength ? AppColors.green : Colors.grey,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: const Text(
            'Verify',
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        );

      case IFSCVerificationState.verifying:
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: AppColors.green,
                ),
              ),
              SizedBox(width: 8),
              Text(
                'Verifying...',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        );

      case IFSCVerificationState.verified:
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.green.shade100,
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 16,
              ),
              SizedBox(width: 8),
              Text(
                'Verified',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.green,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        );

      case IFSCVerificationState.failed:
        return ElevatedButton(
          onPressed: _verifyIFSC,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: const Text(
            'Retry',
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        );
    }
  }

  /// Show skip dialog when user clicks skip for now
  Future<void> _showSkipDialog() async {
    await BankDetailsSkipDialog.show(
      context: context,
      onOkay: () {
        // Call the parent's onSkip callback to proceed to next step
        widget.onSkip?.call();
      },
    );
  }

  /// Verify IFSC code using Razorpay API
  Future<void> _verifyIFSC() async {
    final ifscCode = _ifscController.text.trim().toUpperCase();

    if (ifscCode.length != 11) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('IFSC code must be 11 characters'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _ifscVerificationState = IFSCVerificationState.verifying;
    });

    try {
      final ifscDetails = await IFSCVerificationService.verifyIFSC(ifscCode);

      if (mounted) {
        if (ifscDetails != null) {
          setState(() {
            _ifscVerificationState = IFSCVerificationState.verified;
            _verifiedIFSCDetails = ifscDetails;
            // Auto-select bank if it matches
            if (_banks.contains(ifscDetails.bank)) {
              _selectedBank = ifscDetails.bank;
            }
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('✅ IFSC verified: ${ifscDetails.bank}'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          setState(() {
            _ifscVerificationState = IFSCVerificationState.failed;
            _verifiedIFSCDetails = null;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('❌ Invalid IFSC code. Please check and try again.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _ifscVerificationState = IFSCVerificationState.failed;
          _verifiedIFSCDetails = null;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error verifying IFSC: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
