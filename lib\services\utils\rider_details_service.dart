import 'package:flutter/foundation.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'dart:convert';

/// Service for managing rider details data
/// Handles fetching from API and storing in local storage
class RiderDetailsService {
  static RiderDetailsService? _instance;
  static RiderDetailsService get instance => _instance ??= RiderDetailsService._();

  late ApiService _apiService;
  late SecureStorageService _storage;

  RiderDetailsService._() {
    _apiService = ApiService.instance;
    _storage = SecureStorageService.instance;
  }

  /// Fetch rider details from API and store in local storage
  Future<RiderDetailsResponse?> fetchAndStoreRiderDetails(String mobileNumber) async {
    try {
      debugPrint('📥 Fetching rider details for mobile: $mobileNumber');

      // Call the API
      final response = await _apiService.profile.getRiderDetails(mobileNo: mobileNumber);

      if (response.isSuccess && response.data != null) {
        // Parse the response
        final riderDetailsResponse = RiderDetailsResponse.fromJson(response.data);

        // Store in local storage
        await _storeRiderDetails(riderDetailsResponse);

        debugPrint('✅ Rider details fetched and stored successfully');
        return riderDetailsResponse;
      } else {
        debugPrint('❌ Failed to fetch rider details: ${response.error}');
        return null;
      }
    } catch (e) {
      debugPrint('🚨 Error fetching rider details: $e');
      return null;
    }
  }

  /// Store rider details in local storage
  Future<void> _storeRiderDetails(RiderDetailsResponse riderDetails) async {
    try {
      // Store the complete rider details as JSON
      final jsonString = jsonEncode(riderDetails.toJson());
      await _storage.write(StorageKeys.riderDetails, jsonString);

      // Store the last updated timestamp
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      await _storage.write(StorageKeys.riderDetailsLastUpdated, timestamp);

      // Store individual fields for easy access
      if (riderDetails.riderDetail != null) {
        final detail = riderDetails.riderDetail!;

        // Store key profile information
        await _storage.write('rider_first_name', detail.firstName);
        await _storage.write('rider_last_name', detail.lastName);
        await _storage.write('rider_email', detail.emailID);
        await _storage.write('rider_mobile', detail.mobileNo);
        await _storage.write('rider_full_address', detail.fullAddress);
        await _storage.write('rider_dcid', detail.dcid);
        await _storage.write('rider_vehicle_type', detail.vehicleType.toString());
        await _storage.write('rider_shift_id', detail.shiftID);
        await _storage.write('rider_weekoff_days', detail.weekoffDays);
        await _storage.write('rider_refer_by', detail.referBy);

        // Store bank details
        await _storage.write('rider_bank_name', detail.bankName);
        await _storage.write('rider_account_number', detail.accountNumber);
        await _storage.write('rider_ifsc_code', detail.ifscCode);

        // Store document URLs
        await _storage.write('rider_pan_card_photo', detail.panCardPhoto ?? '');
        await _storage.write('rider_dl_photo', detail.dlPhoto ?? '');
        await _storage.write('rider_self_img', detail.selfImg ?? '');

        // Store personal details
        await _storage.write('rider_dob', detail.dob);
        await _storage.write('rider_gender_type', detail.genderType.toString());
        await _storage.write('rider_marital_status', detail.maritalStatus.toString());
        await _storage.write('rider_pan_no', detail.panNo);
        await _storage.write('rider_dl_no', detail.dlNo);

        // Store family details if available
        if (detail.spouseName != null && detail.spouseName!.isNotEmpty) {
          await _storage.write('rider_spouse_name', detail.spouseName!);
        }
        if (detail.sDob != null && detail.sDob!.isNotEmpty) {
          await _storage.write('rider_spouse_dob', detail.sDob!);
        }
        if (detail.childName != null && detail.childName!.isNotEmpty) {
          await _storage.write('rider_child_name', detail.childName!);
        }
        if (detail.cDob != null && detail.cDob!.isNotEmpty) {
          await _storage.write('rider_child_dob', detail.cDob!);
        }
      }

      debugPrint('💾 Rider details stored in local storage');
    } catch (e) {
      debugPrint('🚨 Error storing rider details: $e');
    }
  }

  /// Get rider details from local storage
  Future<RiderDetailsResponse?> getCachedRiderDetails() async {
    try {
      final jsonString = await _storage.read(StorageKeys.riderDetails);

      if (jsonString != null && jsonString.isNotEmpty) {
        final jsonData = jsonDecode(jsonString);
        return RiderDetailsResponse.fromJson(jsonData);
      }

      return null;
    } catch (e) {
      debugPrint('🚨 Error reading cached rider details: $e');
      return null;
    }
  }

  /// Get specific rider detail field from storage
  Future<String?> getRiderDetailField(String fieldKey) async {
    try {
      return await _storage.read(fieldKey);
    } catch (e) {
      debugPrint('🚨 Error reading rider detail field $fieldKey: $e');
      return null;
    }
  }

  /// Check if rider details are cached and not expired
  Future<bool> isCacheValid({int maxAgeHours = 24}) async {
    try {
      final timestampStr = await _storage.read(StorageKeys.riderDetailsLastUpdated);

      if (timestampStr == null) return false;

      final timestamp = int.tryParse(timestampStr);
      if (timestamp == null) return false;

      final lastUpdated = DateTime.fromMillisecondsSinceEpoch(timestamp);
      final now = DateTime.now();
      final difference = now.difference(lastUpdated);

      return difference.inHours < maxAgeHours;
    } catch (e) {
      debugPrint('🚨 Error checking cache validity: $e');
      return false;
    }
  }

  /// Get rider details with cache-first strategy
  Future<RiderDetailsResponse?> getRiderDetails(String mobileNumber, {bool forceRefresh = false}) async {
    try {
      // Check cache first if not forcing refresh
      if (!forceRefresh && await isCacheValid()) {
        final cached = await getCachedRiderDetails();
        if (cached != null) {
          debugPrint('📱 Using cached rider details');
          return cached;
        }
      }

      // Fetch fresh data from API
      debugPrint('🔄 Fetching fresh rider details from API');
      return await fetchAndStoreRiderDetails(mobileNumber);
    } catch (e) {
      debugPrint('🚨 Error getting rider details: $e');
      return null;
    }
  }

  /// Clear cached rider details
  Future<void> clearCache() async {
    try {
      await _storage.delete(StorageKeys.riderDetails);
      await _storage.delete(StorageKeys.riderDetailsLastUpdated);

      // Clear individual fields
      final fieldsToDelete = [
        'rider_first_name',
        'rider_last_name',
        'rider_email',
        'rider_mobile',
        'rider_full_address',
        'rider_dcid',
        'rider_vehicle_type',
        'rider_shift_id',
        'rider_weekoff_days',
        'rider_refer_by',
        'rider_bank_name',
        'rider_account_number',
        'rider_ifsc_code',
        'rider_pan_card_photo',
        'rider_dl_photo',
        'rider_self_img',
        'rider_dob',
        'rider_gender_type',
        'rider_marital_status',
        'rider_pan_no',
        'rider_dl_no',
        'rider_spouse_name',
        'rider_spouse_dob',
        'rider_child_name',
        'rider_child_dob',
      ];

      await _storage.batchDelete(fieldsToDelete);

      debugPrint('🗑️ Rider details cache cleared');
    } catch (e) {
      debugPrint('🚨 Error clearing rider details cache: $e');
    }
  }

  /// Get rider full name from cache
  Future<String?> getRiderFullName() async {
    try {
      final firstName = await getRiderDetailField('rider_first_name');
      final lastName = await getRiderDetailField('rider_last_name');

      if (firstName != null && lastName != null) {
        return '$firstName $lastName'.trim();
      }

      return null;
    } catch (e) {
      debugPrint('🚨 Error getting rider full name: $e');
      return null;
    }
  }

  /// Get rider vehicle type text from cache
  Future<String?> getRiderVehicleType() async {
    try {
      final vehicleTypeStr = await getRiderDetailField('rider_vehicle_type');
      if (vehicleTypeStr != null) {
        final vehicleType = int.tryParse(vehicleTypeStr) ?? 0;
        switch (vehicleType) {
          case 1:
            return 'Bike';
          case 2:
            return 'Scooter';
          case 3:
            return 'Bicycle';
          case 4:
            return 'EV Scooter';
          default:
            return 'Vehicle';
        }
      }
      return null;
    } catch (e) {
      debugPrint('🚨 Error getting rider vehicle type: $e');
      return null;
    }
  }
}
