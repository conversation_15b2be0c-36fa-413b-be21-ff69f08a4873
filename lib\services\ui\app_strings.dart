import 'package:get/get.dart';
import 'localization_service.dart';

/// Helper class to access localized strings easily throughout the app
class AppStrings {
  static LocalizationService get _localizationService => Get.find<LocalizationService>();

  // Common strings that can be accessed without context
  static const String defaultLocale = 'en';
  static const String hindiLocale = 'hi';
  static const String marathiLocale = 'mr';

  // Currency symbol
  static const String rupeeSymbol = '₹';

  // English strings
  static const Map<String, String> _englishStrings = {
    'appName': 'KisanKonnect Rider',
    'profile': 'Profile',
    'riderName': 'Rider Name',
    'phoneNumber': '+91- 98388 89898',
    'idCard': 'ID Card',
    'cashBalance': 'Cash Balance',
    'myShift': 'My Shift',
    'myKFHLocation': 'My KFH Location',
    'tripHistory': 'Trip History',
    'kisanStore': 'Kissan Store',
    'newTrends': 'New trends',
    'referAndEarn': '<PERSON><PERSON> & <PERSON>arn',
    'referralBonusText': '10,000+ riders are earning referral bonus',
    'referEarn': '<PERSON><PERSON> & <PERSON>arn',
    'referFriendEarn': 'Refer a friend an earn',
    'yourFriendGets': 'Your friend gets',
    'onJoining': 'on joining!',
    'totalReferralEarnings': 'Total referral earnings',
    'friendsReferred': '2 friends referred',
    'shareReferralCode': 'Share your referral code',
    'yourReferrals': 'Your referrals',
    'inviteViaWhatsApp': 'Invite via WhatsApp',
    'referralCodeCopied': 'Referral code copied to clipboard!',
    'joinKisanKonnect': 'Join KisanKonnect and earn',
    'useReferralCode': 'Use my referral code',
    'downloadAppEarn': 'Download the app and start earning today!',
    'helpAndSupport': 'Help & Support',
    'appVersion': 'App version v0.1.10',
    'featuredProducts': 'Featured products',
    'bigDiscount': 'BIG Discount',
    'orderNow': 'Order Now',
    'cartReview': 'Cart review',
    'cartDetails': 'Cart details',
    'wallet': 'Wallet',
    'kisanKonnectWallet': 'KisanKonnect Wallet',
    'kisanKash': 'KisanKash',
    'balance': 'Balance',
    'add': 'Add',
    'redeem': 'Redeem',
    'selectPaymentMode': 'Select your payment mode',
    'cardsUPINetbanking': 'Cards/UPI/Netbanking',
    'payWithUPI': 'Pay with UPI',
    'useAnyUPIApp': 'Use any UPI app on your phone to pay',
    'payNow': 'Pay Now',
    'orderPlacedSuccessfully': 'Your order placed\nsuccessfully',
    'continueShopping': 'Continue Shopping',
    'viewMyOrders': 'View My Orders',
    'selectAddress': 'Select Address',
    'selectYourLocation': 'Select your location',
    'getUpdatedOnWhatsapp': 'Get updated on Whatsapp',
    'continueButton': 'Continue',
    'selectPreferableFilter': 'Select your preferable filter',
    'tripAscending': 'Trip in Ascending order',
    'tripDescending': 'Trip in Descending order',
    'onlySuccessTrip': 'Only Success Trip',
    'onlyFailedTrip': 'Only Failed Trip',
    'okay': 'Okay',

    // Dashboard and Home Screen Strings
    'dashboard': 'Dashboard',
    'home': 'Home',
    'earnings': 'Earnings',
    'payNowTab': 'Pay Now',
    'todaysEarnings': 'Today\'s Earnings',
    'totalEarnings': 'Total Earnings',
    'incentiveBonus': 'Incentive Bonus',
    'referralBonus': 'Referral Bonus',
    'online': 'Online',
    'offline': 'Offline',
    'goOnline': 'Go Online',
    'goOffline': 'Go Offline',
    'shiftStarted': 'Shift Started',
    'shiftEnded': 'Shift Ended',
    'totalTrips': 'Total Trips',
    'completedTrips': 'Completed Trips',
    'cancelledTrips': 'Cancelled Trips',
    'pendingAssets': 'Pending Assets',
    'reports': 'Reports',
    'todaysProgress': 'Today\'s Progress',
    'orders': 'Orders',
    'newOrder': 'New Order',
    'acceptOrder': 'Accept Order',
    'rejectOrder': 'Reject Order',
    'orderDetails': 'Order Details',
    'customerDetails': 'Customer Details',
    'deliveryAddress': 'Delivery Address',
    'pickupAddress': 'Pickup Address',
    'orderValue': 'Order Value',
    'deliveryFee': 'Delivery Fee',
    'distance': 'Distance',
    'estimatedTime': 'Estimated Time',
    'contactCustomer': 'Contact Customer',
    'startDelivery': 'Start Delivery',
    'markDelivered': 'Mark as Delivered',
    'orderCompleted': 'Order Completed',
    'rateCustomer': 'Rate Customer',
    'addFeedback': 'Add Feedback',
    'submit': 'Submit',
    'cancel': 'Cancel',
    'retry': 'Retry',
    'refresh': 'Refresh',
    'loading': 'Loading...',
    'error': 'Error',
    'success': 'Success',
    'warning': 'Warning',
    'info': 'Information',
    'confirm': 'Confirm',
    'yes': 'Yes',
    'no': 'No',
    'save': 'Save',
    'edit': 'Edit',
    'delete': 'Delete',
    'update': 'Update',
    'search': 'Search',
    'filter': 'Filter',
    'sort': 'Sort',
    'clear': 'Clear',
    'apply': 'Apply',
    'reset': 'Reset',
    'back': 'Back',
    'next': 'Next',
    'previous': 'Previous',
    'close': 'Close',
    'done': 'Done',
    'skip': 'Skip',
    'later': 'Later',
    'now': 'Now',
    'today': 'Today',
    'yesterday': 'Yesterday',
    'tomorrow': 'Tomorrow',
    'thisWeek': 'This Week',
    'lastWeek': 'Last Week',
    'thisMonth': 'This Month',
    'lastMonth': 'Last Month',
    'thisYear': 'This Year',
    'lastYear': 'Last Year',
    'all': 'All',
    'none': 'None',
    'select': 'Select',
    'selectAll': 'Select All',
    'deselectAll': 'Deselect All',
    'settings': 'Settings',
    'preferences': 'Preferences',
    'account': 'Account',
    'logout': 'Logout',
    'confirmLogout': 'Confirm Logout',
    'logoutMessage': 'Are you sure you want to logout?',
    'login': 'Login',
    'register': 'Register',
    'forgotPassword': 'Forgot Password',
    'resetPassword': 'Reset Password',
    'changePassword': 'Change Password',
    'currentPassword': 'Current Password',
    'newPassword': 'New Password',
    'confirmPassword': 'Confirm Password',
    'email': 'Email',
    'password': 'Password',
    'username': 'Username',
    'firstName': 'First Name',
    'lastName': 'Last Name',
    'fullName': 'Full Name',
    'dateOfBirth': 'Date of Birth',
    'gender': 'Gender',
    'male': 'Male',
    'female': 'Female',
    'other': 'Other',
    'address': 'Address',
    'city': 'City',
    'state': 'State',
    'country': 'Country',
    'pincode': 'Pincode',
    'landmark': 'Landmark',
    'nearBy': 'Near By',
    'currentLocation': 'Current Location',
    'useCurrentLocation': 'Use Current Location',
    'locationPermission': 'Location Permission',
    'enableLocation': 'Enable Location',
    'locationRequired': 'Location access is required',
    'cameraPermission': 'Camera Permission',
    'enableCamera': 'Enable Camera',
    'cameraRequired': 'Camera access is required',
    'storagePermission': 'Storage Permission',
    'enableStorage': 'Enable Storage',
    'storageRequired': 'Storage access is required',
    'notification': 'Notification',
    'notifications': 'Notifications',
    'enableNotifications': 'Enable Notifications',
    'notificationPermission': 'Notification Permission',
    'language': 'Language',
    'selectLanguage': 'Select Language',
    'english': 'English',
    'hindi': 'हिंदी',
    'marathi': 'मराठी',
    'theme': 'Theme',
    'lightTheme': 'Light Theme',
    'darkTheme': 'Dark Theme',
    'systemTheme': 'System Theme',
    'aboutUs': 'About Us',
    'contactUs': 'Contact Us',
    'privacyPolicy': 'Privacy Policy',
    'termsAndConditions': 'Terms and Conditions',
    'version': 'Version',
    'buildNumber': 'Build Number',
    'developer': 'Developer',
    'copyright': 'Copyright',
    'allRightsReserved': 'All Rights Reserved',

    // Reports section (additional keys)
    'riderNameId': 'Rider Name - 100092',
    'onTimeOrders': 'On Time orders',
    'earlyOrders': 'Early orders',
    'delayOrders': 'Delay orders',
    'viewAllEarnings': 'View all earnings',
    'allEarnings': 'All Earnings',
    'yourEarnedThisWeek': 'Your earned in this week',
    'todaysEarn': 'Today\'s Earn',
    'orderComplete': 'Order Complete',
    'earningDetailsTripWise': 'Earning details trip wise',
    'futureEarningsPerformance': 'Your future earnings are directly tied to your\nperformance',
    'deductions': 'Deductions',

    // Wallet section
    'kisanWallet': 'Kisan Wallet',
    'walletBalance': 'Wallet Balance',
    'kisanKashBalance': 'Kisan Kash Balance',
    'addMoney': 'Add Money',
    'walletTransactions': 'Wallet Transactions',
    'addMoneyButton': 'Add money',
    'addMoneyInWallet': 'Add Money in Wallet',
    'enterAmount': 'Enter Amount',
    'popular': 'POPULAR',
    'addedThroughWallet': 'Added through wallet',
    'kisanKashCredit': 'Kisan Kash Credit',
    'debitAgainstOrder': 'Debit against order #Id 8878887',

    // Kisan Kash section
    'kisanKashNote': 'Note : K 1 Kisan Kash = ₹ 150',
    'kisanKashDescription':
        'Track your Kisan Kash reward points here. You can redeem your Kisan kash at the payment checkout page.',
    'yourActivity': 'Your activity',
    'kashRedeemed': 'Kash Redeemed',
    'kashCredited': 'Kash Credited',
    'expiresSep10': 'Expires Sep 10, 2022',
    'amountAddToWallet': 'Amount add to wallet',
    'amount': 'Amount',
    'myEarnings': 'My Earnings',
    'doMoreTripsToEarn': 'Do 15 more trips to earn ₹600',
    'totalOrderDelivered': 'Total Order delivered',
    'pendingOrders': 'Pending orders',
    'scanOrders': 'Scan Orders',
    'selfAssign': 'Self Assign',
    'totalOrders': 'Total Orders',

    // Shift section
    'hiRiderName': 'Hi Rider Name',
    'storeId': 'Sr-ID - 8989',
    'storeName': 'Store name',
    'shiftWillStart': 'Shift will start',
    'shiftOngoing': 'Shift ongoing',
    'outOf8h': 'out of 8h',
    'takeBreak': 'Take break',
    'currentlyOnBreak': 'Currently on break',
    'onBreak': 'On Break',

    // Earning screen
    'srId': 'SR ID - 78666',
    'seeHistory': 'See History',
    'thisWeekRange': 'This week : 29 Jul - 04 Aug',
    'orderDeliveredCount': 'Order Delivered is 27',
    'getOtpButton': 'Get OTP',
    'payoutsStructure': 'Payouts Structure',
    'getPaidEveryWeek': 'Get paid every week',
    'checkPayoutsHistory': 'Check Payouts History',

    // Verify Payment screen
    'verifyPayment': 'Verify Payment',
    'riderNameDisplay': 'Rider name',
    'srIdNumber': '(Sr ID - 100348)',
    'deliveredOrder': 'Delivered Order',
    'pendingOrder': 'Pending Order',
    'returnOrder': 'Return Order',
    'partialReturnOrder': 'Partial Return Order',
    'cashToBeCollect': 'Cash to be collect',
    'currentTripCod': 'Current trip COD',
    'onlineAmount': 'Online amount',
    'qrAmount': 'QR amount',
    'airtelCash': 'Airtel Cash',
    'saddleBag': 'Saddle Bag',
    'silverBag': 'Silver Bag',
    'chillPad': 'Chill Pad',
    'viewTripInfo': 'View trip info',
    'paymentTripWise': 'Payment trip wise',

    // View Trip Info screen
    'tripDetails': 'Trip Details',
    'tripForCashAmount': 'Trip for cash amount',
    'settlementNote': 'Note - Your settlement pending from DC In-charge first settle then pay amount',
    'paymentReferenceCode': 'Payment Reference Code',
    'failed': 'Failed',
    'enterCodeHere': 'Enter code her',
    'tripId': 'Trip Id',
    'orderId': 'Order Id',
    'orderAmount': 'Order Amount',
    'transferTime': 'Transfer Time',

    // Pay Now screen
    'riderInformation': 'Rider Information',
    'tripIdLabel': 'Trip ID',
    'totalOrderCount': 'Total order count',
    'totalPayableAmount': 'Total Payable Amount',

    // Payment Trip-wise screen (additional keys)
    'selectTrip': 'Select Trip',
    'trip1': 'Trip 1',
    'trip2': 'Trip 2',
    'trip3': 'Trip 3',
    'trip4': 'Trip 4',
    'missingOrder': 'Missing Order',
    'cashEnterBySR': 'Cash Enter By SR',
    'extraAmount': 'Extra Amount',
    'partialReturn': 'Partial Return',
    'partialMissing': 'Partial Missing',
    'cartMissingReturn': 'Cart Missing/Return',
    'advanceAmount': 'Advance Amount',

    // Order Assigned Dialog
    'orderAssigned': 'Order Assigned',
    'orderGettingPacked': 'Order is getting packed',
    'totalOrder': 'Total order',
    'totalEarning': 'Total earning',
    'totalDistance': 'Total distance',
    'totalSaddleBag': 'Total saddle bag',
    'totalSilverBag': 'Total silver bag',
    'ordersCount': 'orders',
    'bagsCount': 'bags',
    'kmUnit': 'km',
    'acceptOrderAction': 'Accept order',

    // Order screens
    'pickupOrder': 'Pickup Order',
    'orderReadyPickup': 'Your order is ready to pickup!',
    'customerAddress': 'Customer address',
    'distanceLandmark': 'Distance & Landmark',
    'scanOrder': 'Scan order',
    'orderPickedUp': 'Order picked up',
    'dragDropOrder': 'Drag & drop as per your preferred order priority',
    'selfAssignOrder': 'Self assign',
    'startDeliveryOrder': 'Start delivery',
    'cantScan': "Can't Scan?",
    'scanQRCode': 'Scan QR Code',
    'distanceLabel': 'Distance',
    'opposite': 'Opposite to',
    'near': 'near',

    // Status dialogs
    'goOfflineConfirm': 'Are you sure you want to go offline?',
    'goOnlineConfirm': 'Are you sure you want to go online?',
    'goOnlineSubtitle': 'You will start receiving delivery requests',
    'yesGoOffline': 'Yes, Go offline',
    'yesGoOnline': 'Yes, Go online',
  };

  // Hindi strings
  static const Map<String, String> _hindiStrings = {
    'appName': 'किसानकनेक्ट राइडर',
    'profile': 'प्रोफ़ाइल',
    'riderName': 'राइडर का नाम',
    'phoneNumber': '+91- 98388 89898',
    'idCard': 'आईडी कार्ड',
    'cashBalance': 'नकद शेष',
    'myShift': 'मेरी शिफ्ट',
    'myKFHLocation': 'मेरा KFH स्थान',
    'tripHistory': 'यात्रा इतिहास',
    'kisanStore': 'किसान स्टोर',
    'newTrends': 'नए ट्रेंड्स',
    'referAndEarn': 'रेफर करें और कमाएं',
    'referralBonusText': '10,000+ राइडर रेफरल बोनस कमा रहे हैं',
    'referEarn': 'रेफर करें और कमाएं',
    'referFriendEarn': 'एक दोस्त को रेफर करें और कमाएं',
    'yourFriendGets': 'आपका दोस्त पाता है',
    'onJoining': 'जॉइन करने पर!',
    'totalReferralEarnings': 'कुल रेफरल कमाई',
    'friendsReferred': '2 दोस्त रेफर किए गए',
    'shareReferralCode': 'अपना रेफरल कोड साझा करें',
    'yourReferrals': 'आपके रेफरल',
    'inviteViaWhatsApp': 'व्हाट्सऐप के माध्यम से आमंत्रित करें',
    'referralCodeCopied': 'रेफरल कोड क्लिपबोर्ड पर कॉपी किया गया!',
    'joinKisanKonnect': 'किसानकनेक्ट में शामिल हों और कमाएं',
    'useReferralCode': 'मेरा रेफरल कोड उपयोग करें',
    'downloadAppEarn': 'ऐप डाउनलोड करें और आज ही कमाना शुरू करें!',
    'helpAndSupport': 'सहायता और समर्थन',
    'appVersion': 'ऐप संस्करण v0.1.10',
    'featuredProducts': 'फीचर्ड उत्पाद',
    'bigDiscount': 'बड़ी छूट',
    'orderNow': 'अभी ऑर्डर करें',
    'cartReview': 'कार्ट समीक्षा',
    'cartDetails': 'कार्ट विवरण',
    'wallet': 'वॉलेट',
    'kisanKonnectWallet': 'किसानकनेक्ट वॉलेट',
    'kisanKash': 'किसानकैश',
    'balance': 'शेष',
    'add': 'जोड़ें',
    'redeem': 'रिडीम करें',
    'selectPaymentMode': 'अपना भुगतान मोड चुनें',
    'cardsUPINetbanking': 'कार्ड/UPI/नेटबैंकिंग',
    'payWithUPI': 'UPI से भुगतान करें',
    'useAnyUPIApp': 'भुगतान के लिए अपने फोन पर कोई भी UPI ऐप का उपयोग करें',
    'payNow': 'अभी भुगतान करें',
    'orderPlacedSuccessfully': 'आपका ऑर्डर सफलतापूर्वक\nदिया गया',
    'continueShopping': 'खरीदारी जारी रखें',
    'viewMyOrders': 'मेरे ऑर्डर देखें',
    'selectAddress': 'पता चुनें',
    'selectYourLocation': 'अपना स्थान चुनें',
    'getUpdatedOnWhatsapp': 'व्हाट्सऐप पर अपडेट प्राप्त करें',
    'continueButton': 'जारी रखें',
    'selectPreferableFilter': 'अपना पसंदीदा फिल्टर चुनें',
    'tripAscending': 'आरोही क्रम में यात्रा',
    'tripDescending': 'अवरोही क्रम में यात्रा',
    'onlySuccessTrip': 'केवल सफल यात्रा',
    'onlyFailedTrip': 'केवल असफल यात्रा',
    'okay': 'ठीक है',

    // Dashboard and Home Screen Strings
    'dashboard': 'डैशबोर्ड',
    'home': 'होम',
    'earnings': 'कमाई',
    'payNowTab': 'अभी भुगतान करें',
    'todaysEarnings': 'आज की कमाई',
    'totalEarnings': 'कुल कमाई',
    'incentiveBonus': 'प्रोत्साहन बोनस',
    'referralBonus': 'रेफरल बोनस',
    'online': 'ऑनलाइन',
    'offline': 'ऑफलाइन',
    'goOnline': 'ऑनलाइन जाएं',
    'goOffline': 'ऑफलाइन जाएं',
    'shiftStarted': 'शिफ्ट शुरू',
    'shiftEnded': 'शिफ्ट समाप्त',
    'totalTrips': 'कुल यात्राएं',
    'completedTrips': 'पूर्ण यात्राएं',
    'cancelledTrips': 'रद्द यात्राएं',
    'pendingAssets': 'लंबित संपत्ति',
    'reports': 'रिपोर्ट',
    'todaysProgress': 'आज की प्रगति',
    'orders': 'ऑर्डर',
    'newOrder': 'नया ऑर्डर',
    'acceptOrder': 'ऑर्डर स्वीकार करें',
    'rejectOrder': 'ऑर्डर अस्वीकार करें',
    'orderDetails': 'ऑर्डर विवरण',
    'customerDetails': 'ग्राहक विवरण',
    'deliveryAddress': 'डिलीवरी पता',
    'pickupAddress': 'पिकअप पता',
    'orderValue': 'ऑर्डर मूल्य',
    'deliveryFee': 'डिलीवरी शुल्क',
    'distance': 'दूरी',
    'estimatedTime': 'अनुमानित समय',
    'contactCustomer': 'ग्राहक से संपर्क करें',
    'startDelivery': 'डिलीवरी शुरू करें',
    'markDelivered': 'डिलीवर के रूप में चिह्नित करें',
    'orderCompleted': 'ऑर्डर पूर्ण',
    'rateCustomer': 'ग्राहक को रेट करें',
    'addFeedback': 'फीडबैक जोड़ें',
    'submit': 'जमा करें',
    'cancel': 'रद्द करें',
    'retry': 'पुनः प्रयास करें',
    'refresh': 'रीफ्रेश करें',
    'loading': 'लोड हो रहा है...',
    'error': 'त्रुटि',
    'success': 'सफलता',
    'warning': 'चेतावनी',
    'info': 'जानकारी',
    'confirm': 'पुष्टि करें',
    'yes': 'हाँ',
    'no': 'नहीं',
    'save': 'सेव करें',
    'edit': 'संपादित करें',
    'delete': 'हटाएं',
    'update': 'अपडेट करें',
    'search': 'खोजें',
    'filter': 'फिल्टर',
    'sort': 'क्रमबद्ध करें',
    'clear': 'साफ करें',
    'apply': 'लागू करें',
    'reset': 'रीसेट करें',
    'back': 'वापस',
    'next': 'अगला',
    'previous': 'पिछला',
    'close': 'बंद करें',
    'done': 'हो गया',
    'skip': 'छोड़ें',
    'later': 'बाद में',
    'now': 'अभी',
    'today': 'आज',
    'yesterday': 'कल',
    'tomorrow': 'कल',
    'thisWeek': 'इस सप्ताह',
    'lastWeek': 'पिछले सप्ताह',
    'thisMonth': 'इस महीने',
    'lastMonth': 'पिछले महीने',
    'thisYear': 'इस साल',
    'lastYear': 'पिछले साल',
    'all': 'सभी',
    'none': 'कोई नहीं',
    'select': 'चुनें',
    'selectAll': 'सभी चुनें',
    'deselectAll': 'सभी अचयनित करें',
    'settings': 'सेटिंग्स',
    'preferences': 'प्राथमिकताएं',
    'account': 'खाता',
    'logout': 'लॉगआउट',
    'confirmLogout': 'लॉगआउट की पुष्टि करें',
    'logoutMessage': 'क्या आप वाकई लॉगआउट करना चाहते हैं?',
    'login': 'लॉगिन',
    'register': 'पंजीकरण',
    'forgotPassword': 'पासवर्ड भूल गए',
    'resetPassword': 'पासवर्ड रीसेट करें',
    'changePassword': 'पासवर्ड बदलें',
    'currentPassword': 'वर्तमान पासवर्ड',
    'newPassword': 'नया पासवर्ड',
    'confirmPassword': 'पासवर्ड की पुष्टि करें',
    'email': 'ईमेल',
    'password': 'पासवर्ड',
    'username': 'उपयोगकर्ता नाम',
    'firstName': 'पहला नाम',
    'lastName': 'अंतिम नाम',
    'fullName': 'पूरा नाम',
    'dateOfBirth': 'जन्म तिथि',
    'gender': 'लिंग',
    'male': 'पुरुष',
    'female': 'महिला',
    'other': 'अन्य',
    'address': 'पता',
    'city': 'शहर',
    'state': 'राज्य',
    'country': 'देश',
    'pincode': 'पिनकोड',
    'landmark': 'लैंडमार्क',
    'nearBy': 'पास में',
    'currentLocation': 'वर्तमान स्थान',
    'useCurrentLocation': 'वर्तमान स्थान का उपयोग करें',
    'locationPermission': 'स्थान अनुमति',
    'enableLocation': 'स्थान सक्षम करें',
    'locationRequired': 'स्थान पहुंच आवश्यक है',
    'cameraPermission': 'कैमरा अनुमति',
    'enableCamera': 'कैमरा सक्षम करें',
    'cameraRequired': 'कैमरा पहुंच आवश्यक है',
    'storagePermission': 'स्टोरेज अनुमति',
    'enableStorage': 'स्टोरेज सक्षम करें',
    'storageRequired': 'स्टोरेज पहुंच आवश्यक है',
    'notification': 'सूचना',
    'notifications': 'सूचनाएं',
    'enableNotifications': 'सूचनाएं सक्षम करें',
    'notificationPermission': 'सूचना अनुमति',
    'language': 'भाषा',
    'selectLanguage': 'भाषा चुनें',
    'english': 'English',
    'hindi': 'हिंदी',
    'marathi': 'मराठी',
    'theme': 'थीम',
    'lightTheme': 'लाइट थीम',
    'darkTheme': 'डार्क थीम',
    'systemTheme': 'सिस्टम थीम',
    'aboutUs': 'हमारे बारे में',
    'contactUs': 'संपर्क करें',
    'privacyPolicy': 'गोपनीयता नीति',
    'termsAndConditions': 'नियम और शर्तें',
    'version': 'संस्करण',
    'buildNumber': 'बिल्ड नंबर',
    'developer': 'डेवलपर',
    'copyright': 'कॉपीराइट',
    'allRightsReserved': 'सभी अधिकार सुरक्षित',

    // Reports section (additional keys)
    'riderNameId': 'राइडर नाम - 100092',
    'onTimeOrders': 'समय पर ऑर्डर',
    'earlyOrders': 'जल्दी ऑर्डर',
    'delayOrders': 'देर से ऑर्डर',
    'viewAllEarnings': 'सभी कमाई देखें',
    'allEarnings': 'सभी कमाई',
    'yourEarnedThisWeek': 'इस सप्ताह आपकी कमाई',
    'todaysEarn': 'आज की कमाई',
    'orderComplete': 'ऑर्डर पूर्ण',
    'earningDetailsTripWise': 'यात्रा के अनुसार कमाई का विवरण',
    'futureEarningsPerformance': 'आपकी भविष्य की कमाई सीधे आपके\nप्रदर्शन से जुड़ी है',
    'deductions': 'कटौती',

    // Wallet section
    'kisanWallet': 'किसान वॉलेट',
    'walletBalance': 'वॉलेट बैलेंस',
    'kisanKashBalance': 'किसान कैश बैलेंस',
    'addMoney': 'पैसे जोड़ें',
    'walletTransactions': 'वॉलेट लेनदेन',
    'addMoneyButton': 'पैसे जोड़ें',
    'addMoneyInWallet': 'वॉलेट में पैसे जोड़ें',
    'enterAmount': 'राशि दर्ज करें',
    'popular': 'लोकप्रिय',
    'addedThroughWallet': 'वॉलेट के माध्यम से जोड़ा गया',
    'kisanKashCredit': 'किसान कैश क्रेडिट',
    'debitAgainstOrder': 'ऑर्डर #Id 8878887 के विरुद्ध डेबिट',

    // Kisan Kash section
    'kisanKashNote': 'नोट : K 1 किसान कैश = ₹ 150',
    'kisanKashDescription':
        'यहाँ अपने किसान कैश रिवार्ड पॉइंट्स को ट्रैक करें। आप भुगतान चेकआउट पेज पर अपना किसान कैश रिडीम कर सकते हैं।',
    'yourActivity': 'आपकी गतिविधि',
    'kashRedeemed': 'कैश रिडीम किया गया',
    'kashCredited': 'कैश क्रेडिट किया गया',
    'expiresSep10': 'समाप्ति 10 सितंबर, 2022',
    'amountAddToWallet': 'वॉलेट में राशि जोड़ें',
    'amount': 'राशि',
    'myEarnings': 'मेरी कमाई',
    'doMoreTripsToEarn': '₹600 कमाने के लिए 15 और यात्राएं करें',
    'totalOrderDelivered': 'कुल ऑर्डर डिलीवर किए गए',
    'pendingOrders': 'लंबित ऑर्डर',
    'scanOrders': 'ऑर्डर स्कैन करें',
    'selfAssign': 'स्वयं असाइन करें',
    'totalOrders': 'कुल ऑर्डर',

    // Shift section
    'hiRiderName': 'हाय राइडर नाम',
    'storeId': 'Sr-ID - 8989',
    'storeName': 'स्टोर नाम',
    'shiftWillStart': 'शिफ्ट शुरू होगी',
    'shiftOngoing': 'शिफ्ट चल रही है',
    'outOf8h': '8 घंटे में से',
    'takeBreak': 'ब्रेक लें',
    'currentlyOnBreak': 'वर्तमान में ब्रेक पर',
    'onBreak': 'ब्रेक पर',

    // Earning screen
    'srId': 'SR ID - 78666',
    'seeHistory': 'इतिहास देखें',
    'thisWeekRange': 'इस सप्ताह : 29 जुलाई - 04 अगस्त',
    'orderDeliveredCount': 'ऑर्डर डिलीवर किया गया है 27',
    'getOtpButton': 'OTP प्राप्त करें',
    'payoutsStructure': 'भुगतान संरचना',
    'getPaidEveryWeek': 'हर सप्ताह भुगतान प्राप्त करें',
    'checkPayoutsHistory': 'भुगतान इतिहास जांचें',

    // Verify Payment screen
    'verifyPayment': 'भुगतान सत्यापित करें',
    'riderNameDisplay': 'राइडर नाम',
    'srIdNumber': '(Sr ID - 100348)',
    'deliveredOrder': 'डिलीवर किया गया ऑर्डर',
    'pendingOrder': 'लंबित ऑर्डर',
    'returnOrder': 'वापसी ऑर्डर',
    'partialReturnOrder': 'आंशिक वापसी ऑर्डर',
    'cashToBeCollect': 'एकत्रित करने के लिए नकद',
    'currentTripCod': 'वर्तमान यात्रा COD',
    'onlineAmount': 'ऑनलाइन राशि',
    'qrAmount': 'QR राशि',
    'airtelCash': 'एयरटेल कैश',
    'saddleBag': 'सैडल बैग',
    'silverBag': 'सिल्वर बैग',
    'chillPad': 'चिल पैड',
    'viewTripInfo': 'यात्रा जानकारी देखें',
    'paymentTripWise': 'यात्रा के अनुसार भुगतान',

    // View Trip Info screen
    'tripDetails': 'यात्रा विवरण',
    'tripForCashAmount': 'नकद राशि के लिए यात्रा',
    'settlementNote': 'नोट - आपका निपटान DC प्रभारी से लंबित है पहले निपटान करें फिर राशि का भुगतान करें',
    'paymentReferenceCode': 'भुगतान संदर्भ कोड',
    'failed': 'असफल',
    'enterCodeHere': 'यहाँ कोड दर्ज करें',
    'tripId': 'यात्रा आईडी',
    'orderId': 'ऑर्डर आईडी',
    'orderAmount': 'ऑर्डर राशि',
    'transferTime': 'स्थानांतरण समय',

    // Pay Now screen
    'riderInformation': 'राइडर जानकारी',
    'tripIdLabel': 'यात्रा आईडी',
    'totalOrderCount': 'कुल ऑर्डर संख्या',
    'totalPayableAmount': 'कुल देय राशि',

    // Payment Trip-wise screen (additional keys)
    'selectTrip': 'यात्रा चुनें',
    'trip1': 'यात्रा 1',
    'trip2': 'यात्रा 2',
    'trip3': 'यात्रा 3',
    'trip4': 'यात्रा 4',
    'missingOrder': 'गुम ऑर्डर',
    'cashEnterBySR': 'SR द्वारा दर्ज नकद',
    'extraAmount': 'अतिरिक्त राशि',
    'partialReturn': 'आंशिक वापसी',
    'partialMissing': 'आंशिक गुम',
    'cartMissingReturn': 'कार्ट गुम/वापसी',
    'advanceAmount': 'अग्रिम राशि',

    // Order Assigned Dialog
    'orderAssigned': 'ऑर्डर असाइन किया गया',
    'orderGettingPacked': 'ऑर्डर पैक हो रहा है',
    'totalOrder': 'कुल ऑर्डर',
    'totalEarning': 'कुल कमाई',
    'totalDistance': 'कुल दूरी',
    'totalSaddleBag': 'कुल सैडल बैग',
    'totalSilverBag': 'कुल सिल्वर बैग',
    'ordersCount': 'ऑर्डर',
    'bagsCount': 'बैग',
    'kmUnit': 'किमी',
    'acceptOrderAction': 'ऑर्डर स्वीकार करें',

    // Order screens
    'pickupOrder': 'ऑर्डर पिकअप करें',
    'orderReadyPickup': 'आपका ऑर्डर पिकअप के लिए तैयार है!',
    'customerAddress': 'ग्राहक का पता',
    'distanceLandmark': 'दूरी और लैंडमार्क',
    'scanOrder': 'ऑर्डर स्कैन करें',
    'orderPickedUp': 'ऑर्डर पिकअप किया गया',
    'dragDropOrder': 'अपनी पसंदीदा ऑर्डर प्राथमिकता के अनुसार ड्रैग और ड्रॉप करें',
    'selfAssignOrder': 'स्वयं असाइन करें',
    'startDeliveryOrder': 'डिलीवरी शुरू करें',
    'cantScan': 'स्कैन नहीं कर सकते?',
    'scanQRCode': 'QR कोड स्कैन करें',
    'distanceLabel': 'दूरी',
    'opposite': 'के सामने',
    'near': 'के पास',

    // Status dialogs
    'goOfflineConfirm': 'क्या आप वाकई ऑफलाइन जाना चाहते हैं?',
    'goOnlineConfirm': 'क्या आप वाकई ऑनलाइन जाना चाहते हैं?',
    'goOnlineSubtitle': 'आपको डिलीवरी अनुरोध मिलना शुरू हो जाएंगे',
    'yesGoOffline': 'हाँ, ऑफलाइन जाएं',
    'yesGoOnline': 'हाँ, ऑनलाइन जाएं',
  };

  // Marathi strings
  static const Map<String, String> _marathiStrings = {
    'appName': 'किसानकनेक्ट रायडर',
    'profile': 'प्रोफाइल',
    'riderName': 'रायडरचे नाव',
    'phoneNumber': '+91- 98388 89898',
    'idCard': 'ओळखपत्र',
    'cashBalance': 'रोख शिल्लक',
    'myShift': 'माझी शिफ्ट',
    'myKFHLocation': 'माझे KFH स्थान',
    'tripHistory': 'प्रवास इतिहास',
    'kisanStore': 'किसान स्टोअर',
    'newTrends': 'नवीन ट्रेंड्स',
    'referAndEarn': 'रेफर करा आणि कमवा',
    'referralBonusText': '10,000+ रायडर रेफरल बोनस कमावत आहेत',
    'referEarn': 'रेफर करा आणि कमवा',
    'referFriendEarn': 'मित्राला रेफर करा आणि कमवा',
    'yourFriendGets': 'तुमच्या मित्राला मिळते',
    'onJoining': 'सामील होताना!',
    'totalReferralEarnings': 'एकूण रेफरल कमाई',
    'friendsReferred': '2 मित्र रेफर केले',
    'shareReferralCode': 'तुमचा रेफरल कोड शेअर करा',
    'yourReferrals': 'तुमचे रेफरल',
    'inviteViaWhatsApp': 'व्हाट्सअॅपद्वारे आमंत्रित करा',
    'referralCodeCopied': 'रेफरल कोड क्लिपबोर्डवर कॉपी केला!',
    'joinKisanKonnect': 'किसानकनेक्टमध्ये सामील व्हा आणि कमवा',
    'useReferralCode': 'माझा रेफरल कोड वापरा',
    'downloadAppEarn': 'अॅप डाउनलोड करा आणि आजच कमवायला सुरुवात करा!',
    'helpAndSupport': 'मदत आणि सहाय्य',
    'appVersion': 'अॅप आवृत्ती v0.1.10',
    'featuredProducts': 'वैशिष्ट्यीकृत उत्पादने',
    'bigDiscount': 'मोठी सूट',
    'orderNow': 'आता ऑर्डर करा',
    'cartReview': 'कार्ट पुनरावलोकन',
    'cartDetails': 'कार्ट तपशील',
    'wallet': 'वॉलेट',
    'kisanKonnectWallet': 'किसानकनेक्ट वॉलेट',
    'kisanKash': 'किसानकॅश',
    'balance': 'शिल्लक',
    'add': 'जोडा',
    'redeem': 'रिडीम करा',
    'selectPaymentMode': 'तुमचा पेमेंट मोड निवडा',
    'cardsUPINetbanking': 'कार्ड/UPI/नेटबँकिंग',
    'payWithUPI': 'UPI ने पेमेंट करा',
    'useAnyUPIApp': 'पेमेंटसाठी तुमच्या फोनवरील कोणतेही UPI अॅप वापरा',
    'payNow': 'आता पेमेंट करा',
    'orderPlacedSuccessfully': 'तुमचा ऑर्डर यशस्वीरित्या\nदिला गेला',
    'continueShopping': 'खरेदी सुरू ठेवा',
    'viewMyOrders': 'माझे ऑर्डर पहा',
    'selectAddress': 'पत्ता निवडा',
    'selectYourLocation': 'तुमचे स्थान निवडा',
    'getUpdatedOnWhatsapp': 'व्हाट्सअॅपवर अपडेट मिळवा',
    'continueButton': 'सुरू ठेवा',
    'selectPreferableFilter': 'तुमचा पसंतीचा फिल्टर निवडा',
    'tripAscending': 'चढत्या क्रमाने प्रवास',
    'tripDescending': 'उतरत्या क्रमाने प्रवास',
    'onlySuccessTrip': 'फक्त यशस्वी प्रवास',
    'onlyFailedTrip': 'फक्त अयशस्वी प्रवास',
    'okay': 'ठीक आहे',

    // Dashboard and Home Screen Strings
    'dashboard': 'डॅशबोर्ड',
    'home': 'होम',
    'earnings': 'कमाई',
    'payNowTab': 'आता पेमेंट करा',
    'todaysEarnings': 'आजची कमाई',
    'totalEarnings': 'एकूण कमाई',
    'incentiveBonus': 'प्रोत्साहन बोनस',
    'referralBonus': 'रेफरल बोनस',
    'online': 'ऑनलाइन',
    'offline': 'ऑफलाइन',
    'goOnline': 'ऑनलाइन जा',
    'goOffline': 'ऑफलाइन जा',
    'shiftStarted': 'शिफ्ट सुरू',
    'shiftEnded': 'शिफ्ट संपली',
    'totalTrips': 'एकूण प्रवास',
    'completedTrips': 'पूर्ण प्रवास',
    'cancelledTrips': 'रद्द प्रवास',
    'pendingAssets': 'प्रलंबित मालमत्ता',
    'reports': 'अहवाल',
    'todaysProgress': 'आजची प्रगती',
    'orders': 'ऑर्डर',
    'newOrder': 'नवीन ऑर्डर',
    'acceptOrder': 'ऑर्डर स्वीकारा',
    'rejectOrder': 'ऑर्डर नाकारा',
    'orderDetails': 'ऑर्डर तपशील',
    'customerDetails': 'ग्राहक तपशील',
    'deliveryAddress': 'डिलिव्हरी पत्ता',
    'pickupAddress': 'पिकअप पत्ता',
    'orderValue': 'ऑर्डर मूल्य',
    'deliveryFee': 'डिलिव्हरी शुल्क',
    'distance': 'अंतर',
    'estimatedTime': 'अंदाजित वेळ',
    'contactCustomer': 'ग्राहकाशी संपर्क साधा',
    'startDelivery': 'डिलिव्हरी सुरू करा',
    'markDelivered': 'डिलिव्हर म्हणून चिन्हांकित करा',
    'orderCompleted': 'ऑर्डर पूर्ण',
    'rateCustomer': 'ग्राहकाला रेट करा',
    'addFeedback': 'फीडबॅक जोडा',
    'submit': 'सबमिट करा',
    'cancel': 'रद्द करा',
    'retry': 'पुन्हा प्रयत्न करा',
    'refresh': 'रिफ्रेश करा',
    'loading': 'लोड होत आहे...',
    'error': 'त्रुटी',
    'success': 'यश',
    'warning': 'चेतावणी',
    'info': 'माहिती',
    'confirm': 'पुष्टी करा',
    'yes': 'होय',
    'no': 'नाही',
    'save': 'सेव्ह करा',
    'edit': 'संपादित करा',
    'delete': 'हटवा',
    'update': 'अपडेट करा',
    'search': 'शोधा',
    'filter': 'फिल्टर',
    'sort': 'क्रमवारी लावा',
    'clear': 'साफ करा',
    'apply': 'लागू करा',
    'reset': 'रीसेट करा',
    'back': 'मागे',
    'next': 'पुढे',
    'previous': 'मागील',
    'close': 'बंद करा',
    'done': 'पूर्ण',
    'skip': 'वगळा',
    'later': 'नंतर',
    'now': 'आता',
    'today': 'आज',
    'yesterday': 'काल',
    'tomorrow': 'उद्या',
    'thisWeek': 'हा आठवडा',
    'lastWeek': 'मागील आठवडा',
    'thisMonth': 'हा महिना',
    'lastMonth': 'मागील महिना',
    'thisYear': 'हे वर्ष',
    'lastYear': 'मागील वर्ष',
    'all': 'सर्व',
    'none': 'काहीही नाही',
    'select': 'निवडा',
    'selectAll': 'सर्व निवडा',
    'deselectAll': 'सर्व अनिवडा',
    'settings': 'सेटिंग्ज',
    'preferences': 'प्राधान्ये',
    'account': 'खाते',
    'logout': 'लॉगआउट',
    'confirmLogout': 'लॉगआउटची पुष्टी करा',
    'logoutMessage': 'तुम्हाला खरोखर लॉगआउट करायचे आहे का?',
    'login': 'लॉगिन',
    'register': 'नोंदणी',
    'forgotPassword': 'पासवर्ड विसरलात',
    'resetPassword': 'पासवर्ड रीसेट करा',
    'changePassword': 'पासवर्ड बदला',
    'currentPassword': 'सध्याचा पासवर्ड',
    'newPassword': 'नवीन पासवर्ड',
    'confirmPassword': 'पासवर्डची पुष्टी करा',
    'email': 'ईमेल',
    'password': 'पासवर्ड',
    'username': 'वापरकर्तानाव',
    'firstName': 'पहिले नाव',
    'lastName': 'आडनाव',
    'fullName': 'पूर्ण नाव',
    'dateOfBirth': 'जन्मतारीख',
    'gender': 'लिंग',
    'male': 'पुरुष',
    'female': 'स्त्री',
    'other': 'इतर',
    'address': 'पत्ता',
    'city': 'शहर',
    'state': 'राज्य',
    'country': 'देश',
    'pincode': 'पिनकोड',
    'landmark': 'लँडमार्क',
    'nearBy': 'जवळपास',
    'currentLocation': 'सध्याचे स्थान',
    'useCurrentLocation': 'सध्याचे स्थान वापरा',
    'locationPermission': 'स्थान परवानगी',
    'enableLocation': 'स्थान सक्षम करा',
    'locationRequired': 'स्थान प्रवेश आवश्यक आहे',
    'cameraPermission': 'कॅमेरा परवानगी',
    'enableCamera': 'कॅमेरा सक्षम करा',
    'cameraRequired': 'कॅमेरा प्रवेश आवश्यक आहे',
    'storagePermission': 'स्टोरेज परवानगी',
    'enableStorage': 'स्टोरेज सक्षम करा',
    'storageRequired': 'स्टोरेज प्रवेश आवश्यक आहे',
    'notification': 'सूचना',
    'notifications': 'सूचना',
    'enableNotifications': 'सूचना सक्षम करा',
    'notificationPermission': 'सूचना परवानगी',
    'language': 'भाषा',
    'selectLanguage': 'भाषा निवडा',
    'english': 'English',
    'hindi': 'हिंदी',
    'marathi': 'मराठी',
    'theme': 'थीम',
    'lightTheme': 'लाइट थीम',
    'darkTheme': 'डार्क थीम',
    'systemTheme': 'सिस्टम थीम',
    'aboutUs': 'आमच्याबद्दल',
    'contactUs': 'आमच्याशी संपर्क साधा',
    'privacyPolicy': 'गोपनीयता धोरण',
    'termsAndConditions': 'नियम आणि अटी',
    'version': 'आवृत्ती',
    'buildNumber': 'बिल्ड नंबर',
    'developer': 'डेव्हलपर',
    'copyright': 'कॉपीराइट',
    'allRightsReserved': 'सर्व हक्क राखीव',

    // Reports section (additional keys)
    'riderNameId': 'रायडरचे नाव - 100092',
    'onTimeOrders': 'वेळेवर ऑर्डर',
    'earlyOrders': 'लवकर ऑर्डर',
    'delayOrders': 'विलंबित ऑर्डर',
    'viewAllEarnings': 'सर्व कमाई पहा',
    'allEarnings': 'सर्व कमाई',
    'yourEarnedThisWeek': 'या आठवड्यात तुमची कमाई',
    'todaysEarn': 'आजची कमाई',
    'orderComplete': 'ऑर्डर पूर्ण',
    'earningDetailsTripWise': 'प्रवासानुसार कमाईचे तपशील',
    'futureEarningsPerformance': 'तुमची भविष्यातील कमाई थेट तुमच्या\nकामगिरीशी जोडलेली आहे',
    'deductions': 'कपात',

    // Wallet section
    'kisanWallet': 'किसान वॉलेट',
    'walletBalance': 'वॉलेट बॅलन्स',
    'kisanKashBalance': 'किसान कॅश बॅलन्स',
    'addMoney': 'पैसे जोडा',
    'walletTransactions': 'वॉलेट व्यवहार',
    'addMoneyButton': 'पैसे जोडा',
    'addMoneyInWallet': 'वॉलेटमध्ये पैसे जोडा',
    'enterAmount': 'रक्कम टाका',
    'popular': 'लोकप्रिय',
    'addedThroughWallet': 'वॉलेटद्वारे जोडले',
    'kisanKashCredit': 'किसान कॅश क्रेडिट',
    'debitAgainstOrder': 'ऑर्डर #Id 8878887 विरुद्ध डेबिट',

    // Kisan Kash section
    'kisanKashNote': 'टीप : K 1 किसान कॅश = ₹ 150',
    'kisanKashDescription':
        'येथे तुमचे किसान कॅश रिवॉर्ड पॉइंट्स ट्रॅक करा. तुम्ही पेमेंट चेकआउट पेजवर तुमचा किसान कॅश रिडीम करू शकता.',
    'yourActivity': 'तुमची क्रिया',
    'kashRedeemed': 'कॅश रिडीम केला',
    'kashCredited': 'कॅश क्रेडिट केला',
    'expiresSep10': 'समाप्ती 10 सप्टेंबर, 2022',
    'amountAddToWallet': 'वॉलेटमध्ये रक्कम जोडा',
    'amount': 'रक्कम',
    'myEarnings': 'माझी कमाई',
    'doMoreTripsToEarn': '₹600 कमवण्यासाठी आणखी 15 प्रवास करा',
    'totalOrderDelivered': 'एकूण ऑर्डर डिलिव्हर केले',
    'pendingOrders': 'प्रलंबित ऑर्डर',
    'scanOrders': 'ऑर्डर स्कॅन करा',
    'selfAssign': 'स्वयं नियुक्त करा',
    'totalOrders': 'एकूण ऑर्डर',

    // Shift section
    'hiRiderName': 'हाय रायडरचे नाव',
    'storeId': 'Sr-ID - 8989',
    'storeName': 'स्टोअरचे नाव',
    'shiftWillStart': 'शिफ्ट सुरू होईल',
    'shiftOngoing': 'शिफ्ट चालू आहे',
    'outOf8h': '8 तासांपैकी',
    'takeBreak': 'ब्रेक घ्या',
    'currentlyOnBreak': 'सध्या ब्रेकवर',
    'onBreak': 'ब्रेकवर',

    // Earning screen
    'srId': 'SR ID - 78666',
    'seeHistory': 'इतिहास पहा',
    'thisWeekRange': 'हा आठवडा : 29 जुलै - 04 ऑगस्ट',
    'orderDeliveredCount': 'ऑर्डर डिलिव्हर केले आहे 27',
    'getOtpButton': 'OTP मिळवा',
    'payoutsStructure': 'पेआउट संरचना',
    'getPaidEveryWeek': 'दर आठवड्याला पेमेंट मिळवा',
    'checkPayoutsHistory': 'पेआउट इतिहास तपासा',

    // Verify Payment screen
    'verifyPayment': 'पेमेंट सत्यापित करा',
    'riderNameDisplay': 'रायडरचे नाव',
    'srIdNumber': '(Sr ID - 100348)',
    'deliveredOrder': 'डिलिव्हर केलेला ऑर्डर',
    'pendingOrder': 'प्रलंबित ऑर्डर',
    'returnOrder': 'परत ऑर्डर',
    'partialReturnOrder': 'आंशिक परत ऑर्डर',
    'cashToBeCollect': 'गोळा करण्यासाठी रोख',
    'currentTripCod': 'सध्याचा प्रवास COD',
    'onlineAmount': 'ऑनलाइन रक्कम',
    'qrAmount': 'QR रक्कम',
    'airtelCash': 'एअरटेल कॅश',
    'saddleBag': 'सॅडल बॅग',
    'silverBag': 'सिल्व्हर बॅग',
    'chillPad': 'चिल पॅड',
    'viewTripInfo': 'प्रवास माहिती पहा',
    'paymentTripWise': 'प्रवासानुसार पेमेंट',

    // View Trip Info screen
    'tripDetails': 'प्रवास तपशील',
    'tripForCashAmount': 'रोख रक्कमेसाठी प्रवास',
    'settlementNote': 'टीप - तुमचे सेटलमेंट DC प्रभारी कडून प्रलंबित आहे प्रथम सेटल करा नंतर रक्कम भरा',
    'paymentReferenceCode': 'पेमेंट संदर्भ कोड',
    'failed': 'अयशस्वी',
    'enterCodeHere': 'येथे कोड टाका',
    'tripId': 'प्रवास आयडी',
    'orderId': 'ऑर्डर आयडी',
    'orderAmount': 'ऑर्डर रक्कम',
    'transferTime': 'स्थानांतरण वेळ',

    // Pay Now screen
    'riderInformation': 'रायडर माहिती',
    'tripIdLabel': 'प्रवास आयडी',
    'totalOrderCount': 'एकूण ऑर्डर संख्या',
    'totalPayableAmount': 'एकूण देय रक्कम',

    // Payment Trip-wise screen (additional keys)
    'selectTrip': 'प्रवास निवडा',
    'trip1': 'प्रवास 1',
    'trip2': 'प्रवास 2',
    'trip3': 'प्रवास 3',
    'trip4': 'प्रवास 4',
    'missingOrder': 'गहाळ ऑर्डर',
    'cashEnterBySR': 'SR द्वारे टाकलेली रोकड',
    'extraAmount': 'अतिरिक्त रक्कम',
    'partialReturn': 'आंशिक परत',
    'partialMissing': 'आंशिक गहाळ',
    'cartMissingReturn': 'कार्ट गहाळ/परत',
    'advanceAmount': 'आगाऊ रक्कम',

    // Order Assigned Dialog
    'orderAssigned': 'ऑर्डर नियुक्त केले',
    'orderGettingPacked': 'ऑर्डर पॅक होत आहे',
    'totalOrder': 'एकूण ऑर्डर',
    'totalEarning': 'एकूण कमाई',
    'totalDistance': 'एकूण अंतर',
    'totalSaddleBag': 'एकूण सॅडल बॅग',
    'totalSilverBag': 'एकूण सिल्व्हर बॅग',
    'ordersCount': 'ऑर्डर',
    'bagsCount': 'बॅग',
    'kmUnit': 'किमी',
    'acceptOrderAction': 'ऑर्डर स्वीकारा',

    // Order screens
    'pickupOrder': 'ऑर्डर पिकअप करा',
    'orderReadyPickup': 'तुमचा ऑर्डर पिकअपसाठी तयार आहे!',
    'customerAddress': 'ग्राहकाचा पत्ता',
    'distanceLandmark': 'अंतर आणि लँडमार्क',
    'scanOrder': 'ऑर्डर स्कॅन करा',
    'orderPickedUp': 'ऑर्डर पिकअप केला',
    'dragDropOrder': 'तुमच्या पसंतीच्या ऑर्डर प्राधान्यानुसार ड्रॅग आणि ड्रॉप करा',
    'selfAssignOrder': 'स्वयं नियुक्त करा',
    'startDeliveryOrder': 'डिलिव्हरी सुरू करा',
    'cantScan': 'स्कॅन करू शकत नाही?',
    'scanQRCode': 'QR कोड स्कॅन करा',
    'distanceLabel': 'अंतर',
    'opposite': 'च्या समोर',
    'near': 'जवळ',

    // Status dialogs
    'goOfflineConfirm': 'तुम्हाला खरोखर ऑफलाइन जायचे आहे का?',
    'goOnlineConfirm': 'तुम्हाला खरोखर ऑनलाइन जायचे आहे का?',
    'goOnlineSubtitle': 'तुम्हाला डिलिव्हरी विनंत्या मिळू लागतील',
    'yesGoOffline': 'होय, ऑफलाइन जा',
    'yesGoOnline': 'होय, ऑनलाइन जा',
  };

  // Get localized string
  static String get(String key) {
    final currentLocale = _localizationService.locale.languageCode;
    Map<String, String> strings;

    switch (currentLocale) {
      case 'hi':
        strings = _hindiStrings;
        break;
      case 'mr':
        strings = _marathiStrings;
        break;
      case 'en':
      default:
        strings = _englishStrings;
        break;
    }

    return strings[key] ?? key;
  }

  // Common patterns
  static String formatCurrency(double amount) {
    return '$rupeeSymbol ${amount.toStringAsFixed(0)}';
  }

  static String formatBalance(String amount) {
    final balanceText = get('balance');
    return '$balanceText : $amount';
  }

  // Phone number formatting
  static String formatPhoneNumber(String number) {
    return '+91- $number';
  }

  // Version formatting
  static String formatVersion(String version) {
    return 'App version $version';
  }
}
