# KisanKonnect Rider App - Production Environment
# This file contains production-specific environment variables

# =============================================================================
# APP CONFIGURATION
# =============================================================================
APP_NAME=KisanKonnect Rider
BUNDLE_ID=com.kisankonnect.rider

# =============================================================================
# API CONFIGURATION
# =============================================================================
API_BASE_URL=http://knet.kisankonnect.com/SRIT3O/api
SOCKET_URL=wss://socket.kisankonnect.com
REQUEST_TIMEOUT=15
MAX_RETRIES=2

# =============================================================================
# FEATURE FLAGS - PRODUCTION
# =============================================================================
ENABLE_LOGGING=false
ENABLE_CRASHLYTICS=true
ENABLE_ANALYTICS=true
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_DEVICE_PREVIEW=false
ENABLE_FLAVOR_BANNER=false

# =============================================================================
# CACHE & STORAGE - PRODUCTION
# =============================================================================
CACHE_TIMEOUT_MINUTES=60
DATABASE_NAME=kisankonnect_rider.db
LOG_LEVEL=error

# =============================================================================
# SECURITY - PRODUCTION (Strict)
# =============================================================================
ENABLE_SSL_PINNING=true
ENABLE_CERTIFICATE_VALIDATION=true
ENABLE_NETWORK_SECURITY=true

# =============================================================================
# DEVELOPMENT TOOLS - DISABLED
# =============================================================================
ENABLE_DEBUG_MENU=false
ENABLE_NETWORK_INSPECTOR=false
ENABLE_PERFORMANCE_OVERLAY=false

# =============================================================================
# PRODUCTION SPECIFIC
# =============================================================================
# Performance optimizations
ENABLE_CODE_OBFUSCATION=true
ENABLE_TREE_SHAKING=true
ENABLE_MINIFICATION=true

# Monitoring
ENABLE_ERROR_REPORTING=true
ENABLE_PERFORMANCE_TRACKING=true
ENABLE_USER_ANALYTICS=true

# Security
ENABLE_ROOT_DETECTION=true
ENABLE_DEBUGGER_DETECTION=true
ENABLE_TAMPER_DETECTION=true

# Production logging
LOG_ONLY_ERRORS=true
SEND_LOGS_TO_SERVER=true

# =============================================================================
# THIRD PARTY INTEGRATIONS - PRODUCTION
# =============================================================================
GOOGLE_MAPS_API_KEY=AIzaSyDRL7BCTTbpAXgKb5pZgM8S05uFlpLZx4s
RAZORPAY_KEY_ID=your_razorpay_key_id
