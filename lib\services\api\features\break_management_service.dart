import '../api_helper.dart';

/// Simple Break Management Service
class BreakManagementService {
  static BreakManagementService? _instance;
  static BreakManagementService get instance => _instance ??= BreakManagementService._();

  late ApiHelper _apiHelper;

  BreakManagementService._() {
    _apiHelper = ApiHelper.instance;
  }

  Future<ApiResponse<dynamic>> submitBreakTime({
    required String userId,
    required String breakType,
    required String duration,
  }) async {
    return await _apiHelper.post<dynamic>(
      '/Rider/IN_RiderBreakTime',
      data: {
        'riderId': int.tryParse(userId) ?? 0,
        'kfhid': 0,
        'cdcType': 0,
        'breakTime': duration,
        'creactedBy': int.tryParse(userId) ?? 0,
      },
    );
  }

  Future<ApiResponse<dynamic>> getBreakHistory({
    required String userId,
    required String dateRange,
  }) async {
    return await _apiHelper.get<dynamic>(
      '/Rider/GetBreakHistory',
      queryParameters: {
        'userId': userId,
        'dateRange': dateRange,
      },
    );
  }

  Future<ApiResponse<bool>> updateBreakStatus({
    required String userId,
    required String status,
  }) async {
    final response = await _apiHelper.post<dynamic>(
      '/Rider/UpdateBreakStatus',
      data: {
        'userId': userId,
        'status': status,
      },
    );
    
    return ApiResponse.success(response.isSuccess);
  }
}
