class CurrentRiderStatusModel {
  final String status;
  final String msg;
  final CurrentRiderStatusData? fECurrentRiderStatus;

  CurrentRiderStatusModel({
    required this.status,
    required this.msg,
    this.fECurrentRiderStatus,
  });

  factory CurrentRiderStatusModel.fromJson(Map<String, dynamic> json) {
    return CurrentRiderStatusModel(
      status: json['status']?.toString() ?? '',
      msg: json['msg']?.toString() ?? '',
      fECurrentRiderStatus: json['fE_CurrentRiderStatus'] != null
          ? CurrentRiderStatusData.fromJson(json['fE_CurrentRiderStatus'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'msg': msg,
      'fE_CurrentRiderStatus': fECurrentRiderStatus?.toJson(),
    };
  }
}

class CurrentRiderStatusData {
  final String remark;
  final int attendanceStatus;
  final int selfAssignStatus;

  CurrentRiderStatusData({
    required this.remark,
    required this.attendanceStatus,
    required this.selfAssignStatus,
  });

  factory CurrentRiderStatusData.fromJson(Map<String, dynamic> json) {
    return CurrentRiderStatusData(
      remark: json['remark']?.toString() ?? '',
      attendanceStatus: json['attendanceStatus'] ?? 0,
      selfAssignStatus: json['selfAssignStatus'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'remark': remark,
      'attendanceStatus': attendanceStatus,
      'selfAssignStatus': selfAssignStatus,
    };
  }

  // Helper methods
  bool get isAvailable => remark.toLowerCase() == 'available';
  bool get isUnavailable => remark.toLowerCase() == 'unavailable';
  bool get hasAttendance => attendanceStatus == 1;
  bool get canSelfAssign => selfAssignStatus == 1;
}
