import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/all_services.dart';

/// Mixin to provide easy analytics tracking for screens and widgets
mixin AnalyticsMixin {
  /// Get the Microsoft Clarity service instance
  MicrosoftClarityService get _analytics => MicrosoftClarityService.to;

  /// Track screen view when screen is opened
  void trackScreenView(String screenName, {Map<String, dynamic>? properties}) {
    _analytics.trackScreenView(screenName, properties: properties);
  }

  /// Track user actions (button clicks, interactions, etc.)
  void trackUserAction(String action, {Map<String, dynamic>? properties}) {
    _analytics.trackUserAction(action, properties: properties);
  }

  /// Track order-related events
  void trackOrderEvent(String orderAction, String orderId, {Map<String, dynamic>? additionalData}) {
    _analytics.trackOrderEvent(orderAction, orderId, additionalData: additionalData);
  }

  /// Track delivery events
  void trackDeliveryEvent(String deliveryAction, {Map<String, dynamic>? deliveryData}) {
    _analytics.trackDeliveryEvent(deliveryAction, deliveryData: deliveryData);
  }

  /// Track QR scan events
  void trackQRScanEvent(String scanType, bool successful, {String? orderId}) {
    _analytics.trackQRScanEvent(scanType, successful, orderId: orderId);
  }

  /// Track navigation between screens
  void trackNavigation(String fromScreen, String toScreen) {
    _analytics.trackNavigation(fromScreen, toScreen);
  }

  /// Track rider status changes
  void trackRiderStatusChange(bool isOnline, {String? reason}) {
    _analytics.trackRiderStatusChange(isOnline, reason: reason);
  }

  /// Track earnings events
  void trackEarningsEvent(String earningsAction, {Map<String, dynamic>? earningsData}) {
    _analytics.trackEarningsEvent(earningsAction, earningsData: earningsData);
  }

  /// Track app errors
  void trackError(String errorType, String errorMessage, {String? stackTrace}) {
    _analytics.trackError(errorType, errorMessage, stackTrace: stackTrace);
  }

  /// Set user properties
  void setUserProperties(Map<String, dynamic> properties) {
    _analytics.setUserProperties(properties);
  }

  /// Set rider information
  void setRiderInfo({
    required String riderId,
    required String riderName,
    String? storeId,
    String? city,
  }) {
    _analytics.setRiderInfo(
      riderId: riderId,
      riderName: riderName,
      storeId: storeId,
      city: city,
    );
  }
}

/// Widget mixin for StatefulWidget analytics tracking
mixin StatefulAnalyticsMixin<T extends StatefulWidget> on State<T> implements AnalyticsMixin {
  /// Get the Microsoft Clarity service instance
  @override
  MicrosoftClarityService get _analytics => MicrosoftClarityService.to;

  /// Screen name for analytics (override in your widget)
  String get screenName => runtimeType.toString().replaceAll('_', '').replaceAll('State', '');

  @override
  void initState() {
    super.initState();
    // Track screen view when widget is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      trackScreenView(screenName);
    });
  }

  /// Track screen view when screen is opened
  @override
  void trackScreenView(String screenName, {Map<String, dynamic>? properties}) {
    _analytics.trackScreenView(screenName, properties: properties);
  }

  /// Track user actions (button clicks, interactions, etc.)
  @override
  void trackUserAction(String action, {Map<String, dynamic>? properties}) {
    _analytics.trackUserAction(action, properties: properties);
  }

  /// Track order-related events
  @override
  void trackOrderEvent(String orderAction, String orderId, {Map<String, dynamic>? additionalData}) {
    _analytics.trackOrderEvent(orderAction, orderId, additionalData: additionalData);
  }

  /// Track delivery events
  @override
  void trackDeliveryEvent(String deliveryAction, {Map<String, dynamic>? deliveryData}) {
    _analytics.trackDeliveryEvent(deliveryAction, deliveryData: deliveryData);
  }

  /// Track QR scan events
  @override
  void trackQRScanEvent(String scanType, bool successful, {String? orderId}) {
    _analytics.trackQRScanEvent(scanType, successful, orderId: orderId);
  }

  /// Track navigation between screens
  @override
  void trackNavigation(String fromScreen, String toScreen) {
    _analytics.trackNavigation(fromScreen, toScreen);
  }

  /// Track rider status changes
  @override
  void trackRiderStatusChange(bool isOnline, {String? reason}) {
    _analytics.trackRiderStatusChange(isOnline, reason: reason);
  }

  /// Track earnings events
  @override
  void trackEarningsEvent(String earningsAction, {Map<String, dynamic>? earningsData}) {
    _analytics.trackEarningsEvent(earningsAction, earningsData: earningsData);
  }

  /// Track app errors
  @override
  void trackError(String errorType, String errorMessage, {String? stackTrace}) {
    _analytics.trackError(errorType, errorMessage, stackTrace: stackTrace);
  }

  /// Set user properties
  @override
  void setUserProperties(Map<String, dynamic> properties) {
    _analytics.setUserProperties(properties);
  }

  /// Set rider information
  @override
  void setRiderInfo({
    required String riderId,
    required String riderName,
    String? storeId,
    String? city,
  }) {
    _analytics.setRiderInfo(
      riderId: riderId,
      riderName: riderName,
      storeId: storeId,
      city: city,
    );
  }
}

/// GetX Controller mixin for analytics tracking
mixin GetXAnalyticsMixin on GetxController implements AnalyticsMixin {
  /// Get the Microsoft Clarity service instance
  @override
  MicrosoftClarityService get _analytics => MicrosoftClarityService.to;

  /// Track screen view when screen is opened
  @override
  void trackScreenView(String screenName, {Map<String, dynamic>? properties}) {
    _analytics.trackScreenView(screenName, properties: properties);
  }

  /// Track user actions (button clicks, interactions, etc.)
  @override
  void trackUserAction(String action, {Map<String, dynamic>? properties}) {
    _analytics.trackUserAction(action, properties: properties);
  }

  /// Track order-related events
  @override
  void trackOrderEvent(String orderAction, String orderId, {Map<String, dynamic>? additionalData}) {
    _analytics.trackOrderEvent(orderAction, orderId, additionalData: additionalData);
  }

  /// Track delivery events
  @override
  void trackDeliveryEvent(String deliveryAction, {Map<String, dynamic>? deliveryData}) {
    _analytics.trackDeliveryEvent(deliveryAction, deliveryData: deliveryData);
  }

  /// Track QR scan events
  @override
  void trackQRScanEvent(String scanType, bool successful, {String? orderId}) {
    _analytics.trackQRScanEvent(scanType, successful, orderId: orderId);
  }

  /// Track navigation between screens
  @override
  void trackNavigation(String fromScreen, String toScreen) {
    _analytics.trackNavigation(fromScreen, toScreen);
  }

  /// Track rider status changes
  @override
  void trackRiderStatusChange(bool isOnline, {String? reason}) {
    _analytics.trackRiderStatusChange(isOnline, reason: reason);
  }

  /// Track earnings events
  @override
  void trackEarningsEvent(String earningsAction, {Map<String, dynamic>? earningsData}) {
    _analytics.trackEarningsEvent(earningsAction, earningsData: earningsData);
  }

  /// Track app errors
  @override
  void trackError(String errorType, String errorMessage, {String? stackTrace}) {
    _analytics.trackError(errorType, errorMessage, stackTrace: stackTrace);
  }

  /// Set user properties
  @override
  void setUserProperties(Map<String, dynamic> properties) {
    _analytics.setUserProperties(properties);
  }

  /// Set rider information
  @override
  void setRiderInfo({
    required String riderId,
    required String riderName,
    String? storeId,
    String? city,
  }) {
    _analytics.setRiderInfo(
      riderId: riderId,
      riderName: riderName,
      storeId: storeId,
      city: city,
    );
  }
}
