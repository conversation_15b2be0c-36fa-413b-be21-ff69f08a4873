import 'package:flutter/material.dart';

class ResponsiveUtils {
  // Private constructor to prevent instantiation
  ResponsiveUtils._();

  // Base design dimensions (reference screen size)
  static const double _baseWidth = 390.0; // iPhone 12/13/14 width
  static const double _baseHeight = 844.0; // iPhone 12/13/14 height

  // Screen size breakpoints
  static const double _smallScreenWidth = 360.0;
  static const double _mediumScreenWidth = 390.0;
  static const double _largeScreenWidth = 428.0;

  /// Get screen width
  static double screenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  /// Get screen height
  static double screenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  /// Get screen size category
  static ScreenSize getScreenSize(BuildContext context) {
    final width = screenWidth(context);
    if (width < _smallScreenWidth) {
      return ScreenSize.small;
    } else if (width < _largeScreenWidth) {
      return ScreenSize.medium;
    } else {
      return ScreenSize.large;
    }
  }

  /// Calculate responsive width based on percentage of screen width
  static double width(BuildContext context, double percentage) {
    return screenWidth(context) * (percentage / 100);
  }

  /// Calculate responsive height based on percentage of screen height
  static double height(BuildContext context, double percentage) {
    return screenHeight(context) * (percentage / 100);
  }

  /// Calculate responsive size based on screen width with scale factor
  static double scale(BuildContext context, double baseSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final scaleFactor = screenWidth / _baseWidth;
    return baseSize * scaleFactor.clamp(0.8, 1.3);
  }

  /// Calculate responsive font size
  static double fontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final scaleFactor = screenWidth / _baseWidth;
    return baseFontSize * scaleFactor.clamp(0.85, 1.15);
  }

  // MARK: - Predefined Spacing Methods

  /// Extra small spacing (1% of screen width)
  static double spacingXS(BuildContext context) => width(context, 1);

  /// Small spacing (2% of screen width)
  static double spacingS(BuildContext context) => width(context, 2);

  /// Medium spacing (4% of screen width)
  static double spacingM(BuildContext context) => width(context, 4);

  /// Large spacing (6% of screen width)
  static double spacingL(BuildContext context) => width(context, 6);

  /// Extra large spacing (8% of screen width)
  static double spacingXL(BuildContext context) => width(context, 8);

  // MARK: - Predefined Padding Methods

  /// Horizontal padding (4% of screen width)
  static EdgeInsets paddingHorizontal(BuildContext context) {
    return EdgeInsets.symmetric(horizontal: width(context, 4));
  }

  /// Vertical padding (2% of screen height)
  static EdgeInsets paddingVertical(BuildContext context) {
    return EdgeInsets.symmetric(vertical: height(context, 2));
  }

  /// All sides padding (4% of screen width)
  static EdgeInsets paddingAll(BuildContext context) {
    return EdgeInsets.all(width(context, 4));
  }

  /// Custom padding with responsive values
  static EdgeInsets padding(
    BuildContext context, {
    double? top,
    double? bottom,
    double? left,
    double? right,
  }) {
    return EdgeInsets.only(
      top: top != null ? height(context, top) : 0,
      bottom: bottom != null ? height(context, bottom) : 0,
      left: left != null ? width(context, left) : 0,
      right: right != null ? width(context, right) : 0,
    );
  }

  // MARK: - Predefined Size Methods

  /// Icon size based on screen size category
  static double iconSize(BuildContext context, IconSizeType type) {
    final screenSize = getScreenSize(context);
    switch (type) {
      case IconSizeType.small:
        return screenSize == ScreenSize.small
            ? 16
            : screenSize == ScreenSize.medium
                ? 18
                : 20;
      case IconSizeType.medium:
        return screenSize == ScreenSize.small
            ? 20
            : screenSize == ScreenSize.medium
                ? 24
                : 28;
      case IconSizeType.large:
        return screenSize == ScreenSize.small
            ? 28
            : screenSize == ScreenSize.medium
                ? 32
                : 36;
      case IconSizeType.extraLarge:
        return screenSize == ScreenSize.small
            ? 36
            : screenSize == ScreenSize.medium
                ? 40
                : 44;
    }
  }

  /// Border radius based on screen width
  static double borderRadius(BuildContext context, BorderRadiusType type) {
    switch (type) {
      case BorderRadiusType.small:
        return width(context, 2); // 2% of screen width
      case BorderRadiusType.medium:
        return width(context, 4); // 4% of screen width
      case BorderRadiusType.large:
        return width(context, 6); // 6% of screen width
      case BorderRadiusType.extraLarge:
        return width(context, 8); // 8% of screen width
    }
  }

  /// Container height based on screen height
  static double containerHeight(BuildContext context, ContainerHeightType type) {
    switch (type) {
      case ContainerHeightType.small:
        return height(context, 6); // 6% of screen height
      case ContainerHeightType.medium:
        return height(context, 10); // 10% of screen height
      case ContainerHeightType.large:
        return height(context, 15); // 15% of screen height
      case ContainerHeightType.extraLarge:
        return height(context, 25); // 25% of screen height
    }
  }

  // MARK: - Utility Methods

  /// Check if screen is small
  static bool isSmallScreen(BuildContext context) {
    return getScreenSize(context) == ScreenSize.small;
  }

  /// Check if screen is medium
  static bool isMediumScreen(BuildContext context) {
    return getScreenSize(context) == ScreenSize.medium;
  }

  /// Check if screen is large
  static bool isLargeScreen(BuildContext context) {
    return getScreenSize(context) == ScreenSize.large;
  }

  /// Get responsive value based on screen size
  static T responsiveValue<T>(
    BuildContext context, {
    required T small,
    required T medium,
    required T large,
  }) {
    final screenSize = getScreenSize(context);
    switch (screenSize) {
      case ScreenSize.small:
        return small;
      case ScreenSize.medium:
        return medium;
      case ScreenSize.large:
        return large;
    }
  }
}

// MARK: - Enums

enum ScreenSize { small, medium, large }

enum IconSizeType { small, medium, large, extraLarge }

enum BorderRadiusType { small, medium, large, extraLarge }

enum ContainerHeightType { small, medium, large, extraLarge }

// MARK: - Extension Methods

extension ResponsiveExtension on BuildContext {
  /// Quick access to ResponsiveUtils methods
  // ResponsiveUtils get responsive => ResponsiveUtils; // Removed as it causes type error

  /// Quick access to screen width
  double get screenWidth => ResponsiveUtils.screenWidth(this);

  /// Quick access to screen height
  double get screenHeight => ResponsiveUtils.screenHeight(this);

  /// Quick access to responsive width
  double widthPercent(double percentage) => ResponsiveUtils.width(this, percentage);

  /// Quick access to responsive height
  double heightPercent(double percentage) => ResponsiveUtils.height(this, percentage);

  /// Quick access to responsive scale
  double scale(double baseSize) => ResponsiveUtils.scale(this, baseSize);

  /// Quick access to responsive font size
  double responsiveFontSize(double baseFontSize) => ResponsiveUtils.fontSize(this, baseFontSize);
}
