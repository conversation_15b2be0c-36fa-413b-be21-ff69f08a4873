import 'package:get/get.dart';
import 'package:flutter/foundation.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import '../utils/error_handler.dart';
import '../constants/storage_keys.dart';

/// Controller for managing cash balance data
class CashBalanceController extends GetxController {
  final ApiService _apiService = ApiService.instance;
  final SecureStorageService _storage = SecureStorageService.instance;

  // Loading state
  final RxBool _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  // Cash balance data
  final RxString _cashBalance = '0'.obs;
  String get cashBalance => _cashBalance.value;

  // Error state
  final RxString _error = ''.obs;
  String get error => _error.value;
  bool get hasError => _error.value.isNotEmpty;

  // Cash balance will be loaded when needed (e.g., when profile screen is accessed)
  // No automatic loading on initialization to avoid showing errors on dashboard

  /// Load cash balance from API
  Future<void> loadCashBalance() async {
    try {
      _isLoading.value = true;
      _error.value = '';

      // Get Userid from stored login info (from LoginInfo.userName field)
      final userId = await _storage.read(StorageKeys.userName);

      if (userId == null || userId.isEmpty || userId == '0') {
        throw Exception('Userid not found in stored login info');
      }

      debugPrint('💰 Loading cash balance for Userid: $userId');

      final response = await _apiService.cashBalance.getCashBalance(userId: userId);

      if (response.isSuccess && response.data != null) {
        // Parse the response and extract cash balance
        final data = response.data;
        debugPrint('💰 Cash balance response: $data');

        // Extract cash balance from response
        // API Response structure: {"status":"402","msg":"0 Records Found.","fE_KFHRiderCODLimit":null}
        if (data is Map<String, dynamic>) {
          final status = data['status']?.toString() ?? '';
          final msg = data['msg']?.toString() ?? '';
          final codLimitData = data['fE_KFHRiderCODLimit'];

          debugPrint('💰 API Status: $status, Message: $msg');

          if (status == '200' && codLimitData != null) {
            // Success case - extract balance from fE_KFHRiderCODLimit
            if (codLimitData is Map<String, dynamic>) {
              final balance = codLimitData['codLimit'] ??
                  codLimitData['balance'] ??
                  codLimitData['amount'] ??
                  codLimitData['availableBalance'] ??
                  '0';
              _cashBalance.value = balance.toString();
            } else if (codLimitData is List && codLimitData.isNotEmpty) {
              final firstItem = codLimitData.first;
              if (firstItem is Map<String, dynamic>) {
                final balance = firstItem['codLimit'] ??
                    firstItem['balance'] ??
                    firstItem['amount'] ??
                    firstItem['availableBalance'] ??
                    '0';
                _cashBalance.value = balance.toString();
              }
            } else {
              _cashBalance.value = codLimitData.toString();
            }
          } else {
            // No records found or error case
            _cashBalance.value = '0';
            debugPrint('💰 No cash balance records found: $msg');
          }

          debugPrint('💰 Final cash balance: ${_cashBalance.value}');
        } else {
          // Fallback for unexpected response format
          _cashBalance.value = '0';
          debugPrint('💰 Unexpected response format: $data');
        }
      } else {
        throw Exception(response.error ?? 'Failed to load cash balance');
      }
    } catch (e) {
      debugPrint('🚨 Error loading cash balance: $e');
      _error.value = e.toString();
      _cashBalance.value = '0';

      // Show error snackbar for cash balance loading failure
      ErrorHandler.showErrorSnackbar(
        title: 'Cash Balance Error',
        message: 'Unable to load your cash balance.',
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// Refresh cash balance data
  Future<void> refreshCashBalance() async {
    await loadCashBalance();
  }

  /// Get formatted cash balance with currency symbol
  String get formattedCashBalance {
    try {
      final balance = double.tryParse(_cashBalance.value) ?? 0.0;
      return '₹${balance.toStringAsFixed(0)}';
    } catch (e) {
      return '₹0';
    }
  }

  /// Clear error state
  void clearError() {
    _error.value = '';
  }
}
