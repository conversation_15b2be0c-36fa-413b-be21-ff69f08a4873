import 'package:get/get.dart';
import 'package:kisankonnect_rider/models/dashboard_story_models.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

class DashboardStoryController extends GetxController {
  // Use ApiServiceMain instance
  final ApiService _apiService = ApiService.instance;

  // Observable variables
  final RxBool isLoading = false.obs;
  final RxString error = ''.obs;
  final RxList<StoryBanner> storyBanners = <StoryBanner>[].obs;
  final RxList<TopBanner> topBanners = <TopBanner>[].obs;
  final RxSet<int> viewedStories = <int>{}.obs;

  @override
  void onInit() {
    super.onInit();
    fetchDashboardStoryData();
  }

  String get riderId => '67056057';
  String get kfhId => '67056057';
  String get storyType => '67056057';

  Future<void> fetchDashboardStoryData() async {
    try {
      isLoading.value = true;
      error.value = '';
      print('🔄 Fetching dashboard story data...');

      final response = await _apiService.dashboard.getDashboardStoryBanner(
        id: riderId,
        kfhId: kfhId,
        storyType: storyType,
      );

      print('📡 API Response: ${response.isSuccess ? 'Success' : 'Failed'}');
      if (response.data != null) {
        print('📊 Response data: ${response.data}');
      }

      if (response.isSuccess) {
        final dashboardStoryResponse = DashboardStoryResponse.fromJson(response.data);
        print('✅ Parsed response - Status: ${dashboardStoryResponse.status}');
        print('📋 Story banners count: ${dashboardStoryResponse.result.length}');
        print('🎯 Top banners count: ${dashboardStoryResponse.topBanner.length}');

        if (dashboardStoryResponse.status == '200') {
          storyBanners.value = dashboardStoryResponse.result;
          topBanners.value = dashboardStoryResponse.topBanner;
          print('✅ Successfully loaded ${storyBanners.length} story banners');
        } else {
          error.value = dashboardStoryResponse.msg;
          print('❌ API returned error: ${dashboardStoryResponse.msg}');
        }
      } else {
        error.value = 'Failed to load dashboard data';
        print('❌ API call failed: ${response.error}');
      }
    } catch (e) {
      error.value = 'Error: ${e.toString()}';
      print('💥 Exception in fetchDashboardStoryData: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// Refresh the dashboard story data
  Future<void> refreshData() async {
    await fetchDashboardStoryData();
  }

  /// Mark a story as viewed
  void markStoryAsViewed(int index) {
    viewedStories.add(index);
  }

  /// Check if a story has been viewed
  bool isStoryViewed(int index) {
    return viewedStories.contains(index);
  }

  /// Get story banner by index
  StoryBanner? getStoryBanner(int index) {
    if (index >= 0 && index < storyBanners.length) {
      return storyBanners[index];
    }
    return null;
  }

  /// Get top banner by index
  TopBanner? getTopBanner(int index) {
    if (index >= 0 && index < topBanners.length) {
      return topBanners[index];
    }
    return null;
  }

  /// Get sorted story banners by priority
  List<StoryBanner> get sortedStoryBanners {
    final sorted = List<StoryBanner>.from(storyBanners);
    sorted.sort((a, b) => a.priority.compareTo(b.priority));
    return sorted;
  }

  /// Clear all viewed stories (for testing purposes)
  void clearViewedStories() {
    viewedStories.clear();
  }
}
