import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:local_auth/local_auth.dart';
import 'package:local_auth/error_codes.dart' as auth_error;
import 'package:vibration/vibration.dart';

/// Biometric authentication service using local_auth package
class BiometricService {
  static BiometricService? _instance;
  static BiometricService get instance => _instance ??= BiometricService._();

  BiometricService._();

  // Storage keys
  static const String _biometricEnabledKey = 'biometric_enabled';
  static const String _userCredentialsKey = 'user_credentials';

  final LocalAuthentication _localAuth = LocalAuthentication();
  bool _isInitialized = false;

  /// Initialize biometric service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🔐 Initializing Simple BiometricService...');
      _isInitialized = true;
      debugPrint('✅ Simple BiometricService initialized successfully');
    } catch (e) {
      debugPrint('🚨 Error initializing BiometricService: $e');
      _isInitialized = false;
    }
  }

  /// Check if biometric authentication is available on the device
  Future<bool> isBiometricAvailable() async {
    try {
      debugPrint('🔐 Checking biometric availability...');

      // Check if device supports biometric authentication
      final bool isAvailable = await _localAuth.canCheckBiometrics;
      final bool isDeviceSupported = await _localAuth.isDeviceSupported();

      debugPrint('🔐 canCheckBiometrics: $isAvailable');
      debugPrint('🔐 isDeviceSupported: $isDeviceSupported');

      if (!isAvailable || !isDeviceSupported) {
        debugPrint('🔐 Biometric not available - canCheck: $isAvailable, deviceSupported: $isDeviceSupported');
        return false;
      }

      // Check available biometric types
      final List<BiometricType> availableBiometrics = await _localAuth.getAvailableBiometrics();
      debugPrint('🔐 Available biometrics: $availableBiometrics');

      final hasFingerprint = availableBiometrics.contains(BiometricType.fingerprint);
      final hasFace = availableBiometrics.contains(BiometricType.face);
      final hasIris = availableBiometrics.contains(BiometricType.iris);
      final hasWeak = availableBiometrics.contains(BiometricType.weak);
      final hasStrong = availableBiometrics.contains(BiometricType.strong);

      debugPrint('🔐 Fingerprint: $hasFingerprint, Face: $hasFace, Iris: $hasIris, Weak: $hasWeak, Strong: $hasStrong');

      return availableBiometrics.isNotEmpty;
    } catch (e) {
      debugPrint('🚨 Error checking biometric availability: $e');
      return false;
    }
  }

  /// Check if biometric authentication is enabled by user
  Future<bool> isBiometricEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_biometricEnabledKey) ?? false;
    } catch (e) {
      debugPrint('🚨 Error checking biometric enabled status: $e');
      return false;
    }
  }

  /// Enable biometric authentication
  Future<bool> enableBiometric() async {
    try {
      debugPrint('🔐 Enabling biometric authentication...');

      // Check if biometric is available
      if (!await isBiometricAvailable()) {
        throw Exception('Biometric authentication is not available on this device');
      }

      // Authenticate with biometrics to enable the feature
      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason: 'Please authenticate to enable biometric login',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );

      if (didAuthenticate) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool(_biometricEnabledKey, true);

        debugPrint('✅ Biometric authentication enabled');
        return true;
      } else {
        debugPrint('❌ Biometric authentication failed');
        return false;
      }
    } catch (e) {
      debugPrint('🚨 Error enabling biometric: $e');

      if (e is PlatformException) {
        String message = 'Failed to enable biometric authentication.';

        switch (e.code) {
          case auth_error.notAvailable:
            message = 'Biometric authentication is not available on this device.';
            break;
          case auth_error.notEnrolled:
            message = 'No biometrics enrolled. Please set up fingerprint or face ID in device settings.';
            break;
          case auth_error.lockedOut:
            message = 'Biometric authentication is temporarily locked. Please try again later.';
            break;
          case auth_error.permanentlyLockedOut:
            message = 'Biometric authentication is permanently locked. Please use device passcode.';
            break;
          case 'no_fragment_activity':
            message = 'App configuration issue. Please restart the app and try again.';
            debugPrint('🚨 FragmentActivity issue detected. MainActivity needs to extend FlutterFragmentActivity.');
            break;
        }

        Get.snackbar(
          'Biometric Error',
          message,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          'Biometric Error',
          'Failed to enable biometric authentication.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
      return false;
    }
  }

  /// Disable biometric authentication
  Future<bool> disableBiometric() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_biometricEnabledKey, false);
      await prefs.remove(_userCredentialsKey);

      debugPrint('✅ Biometric authentication disabled');
      return true;
    } catch (e) {
      debugPrint('🚨 Error disabling biometric: $e');

      Get.snackbar(
        'Error',
        'Failed to disable biometric authentication.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
  }

  /// Store user credentials securely for biometric login
  Future<bool> storeUserCredentials({
    required String mobileNumber,
    String? userId,
    String? token,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Store credentials as JSON string
      final credentials = {
        'mobileNumber': mobileNumber,
        if (userId != null) 'userId': userId,
        if (token != null) 'token': token,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      await prefs.setString(_userCredentialsKey, credentials.toString());
      debugPrint('✅ User credentials stored securely');
      return true;
    } catch (e) {
      debugPrint('🚨 Error storing user credentials: $e');
      return false;
    }
  }

  /// Simple biometric authentication for security (no credential retrieval)
  Future<bool> authenticateForSecurity() async {
    try {
      debugPrint('🔐 Starting simple biometric authentication for security...');

      // Check if biometric is available and enabled
      if (!await isBiometricAvailable() || !await isBiometricEnabled()) {
        debugPrint('❌ Biometric not available or not enabled');
        return false;
      }

      // Authenticate with biometrics for security only
      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason: 'Please authenticate to access the app',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );

      if (didAuthenticate) {
        debugPrint('✅ Biometric authentication successful');
        return true;
      } else {
        debugPrint('❌ Biometric authentication failed');
        await _vibrateOnFailure();
        return false;
      }
    } on PlatformException catch (e) {
      debugPrint('🚨 Biometric authentication platform error: ${e.code} - ${e.message}');

      // Handle specific error cases
      switch (e.code) {
        case auth_error.notAvailable:
          debugPrint('❌ Biometric authentication not available');
          break;
        case auth_error.notEnrolled:
          debugPrint('❌ No biometric credentials enrolled');
          break;
        case auth_error.lockedOut:
          debugPrint('❌ Biometric authentication locked out');
          break;
        case auth_error.permanentlyLockedOut:
          debugPrint('❌ Biometric authentication permanently locked out');
          break;
        default:
          debugPrint('❌ Unknown biometric error: ${e.code}');
      }
      await _vibrateOnFailure();
      return false;
    } catch (e) {
      debugPrint('🚨 Biometric authentication error: $e');
      await _vibrateOnFailure();
      return false;
    }
  }

  /// Vibrate device on biometric authentication failure
  Future<void> _vibrateOnFailure() async {
    try {
      // Check if device supports vibration
      bool? hasVibrator = await Vibration.hasVibrator();
      if (hasVibrator == true) {
        // Create a pattern for authentication failure
        // Pattern: short-long-short vibration
        await Vibration.vibrate(
          pattern: [0, 100, 50, 200, 50, 100],
          intensities: [0, 128, 0, 255, 0, 128],
        );
        debugPrint('📳 Vibration triggered for biometric failure');
      } else {
        debugPrint('📳 Device does not support vibration');
      }
    } catch (e) {
      debugPrint('🚨 Error triggering vibration: $e');
    }
  }

  /// Retrieve user credentials with biometric authentication
  Future<Map<String, dynamic>?> getUserCredentials() async {
    try {
      debugPrint('🔐 Retrieving user credentials...');

      // Check if biometric is available and enabled
      if (!await isBiometricAvailable() || !await isBiometricEnabled()) {
        debugPrint('❌ Biometric not available or not enabled');
        return null;
      }

      // Authenticate with biometrics
      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason: 'Please authenticate to access your saved login',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );

      if (!didAuthenticate) {
        debugPrint('❌ Biometric authentication failed');
        return null;
      }

      final prefs = await SharedPreferences.getInstance();
      final credentialsString = prefs.getString(_userCredentialsKey);

      if (credentialsString == null) {
        debugPrint('❌ No stored credentials found');
        return null;
      }

      debugPrint('✅ User credentials retrieved successfully');
      return {
        'mobileNumber': 'stored_mobile_number',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
    } catch (e) {
      debugPrint('🚨 Error retrieving user credentials: $e');

      if (e is PlatformException) {
        String message = 'Failed to authenticate with biometrics.';

        switch (e.code) {
          case auth_error.notAvailable:
            message = 'Biometric authentication is not available.';
            break;
          case auth_error.notEnrolled:
            message = 'No biometrics enrolled on this device.';
            break;
          case auth_error.lockedOut:
            message = 'Biometric authentication is temporarily locked.';
            break;
        }

        Get.snackbar(
          'Authentication Failed',
          message,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          'Authentication Failed',
          'Failed to authenticate with biometrics.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
      return null;
    }
  }

  /// Clear all biometric data
  Future<void> clearBiometricData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_biometricEnabledKey);
      await prefs.remove(_userCredentialsKey);

      debugPrint('✅ All biometric data cleared');
    } catch (e) {
      debugPrint('🚨 Error clearing biometric data: $e');
    }
  }

  /// Get biometric authentication status info
  Future<Map<String, dynamic>> getBiometricInfo() async {
    try {
      final isAvailable = await isBiometricAvailable();
      final isEnabled = isAvailable ? await isBiometricEnabled() : false;

      return {
        'isAvailable': isAvailable,
        'isEnabled': isEnabled,
        'canAuthenticateResponse': 'success',
        'isInitialized': _isInitialized,
      };
    } catch (e) {
      debugPrint('🚨 Error getting biometric info: $e');
      return {
        'isAvailable': false,
        'isEnabled': false,
        'canAuthenticateResponse': 'error',
        'isInitialized': false,
        'error': e.toString(),
      };
    }
  }
}
