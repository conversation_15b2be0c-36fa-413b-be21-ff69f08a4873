import 'package:flutter/foundation.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

/// WhatsApp OTP Service for sending OTP via WhatsApp Business API
class WhatsAppOtpService {
  static WhatsAppOtpService? _instance;
  static WhatsAppOtpService get instance => _instance ??= WhatsAppOtpService._();

  late ApiHelper _apiHelper;

  WhatsAppOtpService._() {
    _apiHelper = ApiHelper.instance;
  }

  /// Send OTP via WhatsApp using Business API
  Future<ApiResponse<dynamic>> sendWhatsAppOtp({
    required String phoneNumber,
    required String otp,
    String? templateName,
  }) async {
    try {
      // Option 1: If KisanKonnect backend supports WhatsApp OTP
      return await _apiHelper.post<dynamic>(
        ApiEndpoints.sendWhatsAppOtp, // Backend endpoint for WhatsApp OTP
        data: {
          'MobileNo': phoneNumber,
          'OTP': otp,
          'Channel': 'whatsapp',
          'TemplateName': templateName ?? 'otp_template',
        },
      );
    } catch (e) {
      debugPrint('❌ WhatsApp OTP Error: $e');
      return ApiResponse.error('Failed to send WhatsApp OTP: $e');
    }
  }

  /// Send OTP via third-party WhatsApp service (Twilio, MessageBird, etc.)
  Future<ApiResponse<dynamic>> sendWhatsAppOtpViaThirdParty({
    required String phoneNumber,
    required String otp,
    String? serviceName = 'twilio',
  }) async {
    try {
      switch (serviceName) {
        case 'twilio':
          return await _sendViaTwilio(phoneNumber, otp);
        case 'messagebird':
          return await _sendViaMessageBird(phoneNumber, otp);
        case 'gupshup':
          return await _sendViaGupshup(phoneNumber, otp);
        default:
          return ApiResponse.error('Unsupported WhatsApp service: $serviceName');
      }
    } catch (e) {
      debugPrint('❌ Third-party WhatsApp OTP Error: $e');
      return ApiResponse.error('Failed to send WhatsApp OTP: $e');
    }
  }

  /// Send via Twilio WhatsApp API
  Future<ApiResponse<dynamic>> _sendViaTwilio(String phoneNumber, String otp) async {
    // These should be configured in environment variables or flavor config
    const twilioAccountSid = 'YOUR_TWILIO_ACCOUNT_SID';
    const twilioAuthToken = 'YOUR_TWILIO_AUTH_TOKEN';
    const twilioWhatsAppNumber = 'whatsapp:+***********'; // Twilio sandbox number

    final message = 'Your KisanKonnect OTP is: $otp. Do not share this with anyone.';

    // Use configurable Twilio API URL
    return await _apiHelper.post<dynamic>(
      '${ApiEndpoints.twilioApi}/Accounts/$twilioAccountSid/Messages.json',
      data: {
        'From': twilioWhatsAppNumber,
        'To': 'whatsapp:+$phoneNumber',
        'Body': message,
      },
      // headers: {
      //   'Authorization': 'Basic ${_encodeBase64('$twilioAccountSid:$twilioAuthToken')}',
      //   'Content-Type': 'application/x-www-form-urlencoded',
      // },
    );
  }

  /// Send via MessageBird WhatsApp API
  Future<ApiResponse<dynamic>> _sendViaMessageBird(String phoneNumber, String otp) async {
    const messageBirdApiKey = 'YOUR_MESSAGEBIRD_API_KEY';
    const messageBirdChannelId = 'YOUR_WHATSAPP_CHANNEL_ID';

    final message = 'Your KisanKonnect OTP is: $otp. Do not share this with anyone.';

    // Use configurable MessageBird API URL
    return await _apiHelper.post<dynamic>(
      '${ApiEndpoints.messageBirdApi}/conversations',
      data: {
        'to': phoneNumber,
        'type': 'text',
        'content': {'text': message},
        'channelId': messageBirdChannelId,
      },
      // headers: {
      //   'Authorization': 'AccessKey $messageBirdApiKey',
      //   'Content-Type': 'application/json',
      // },
    );
  }

  /// Send via Gupshup WhatsApp API
  Future<ApiResponse<dynamic>> _sendViaGupshup(String phoneNumber, String otp) async {
    const gupshupApiKey = 'YOUR_GUPSHUP_API_KEY';
    const gupshupAppName = 'YOUR_GUPSHUP_APP_NAME';

    final message = 'Your KisanKonnect OTP is: $otp. Do not share this with anyone.';

    // Use configurable Gupshup API URL
    return await _apiHelper.post<dynamic>(
      '${ApiEndpoints.gupshupApi}/msg',
      data: {
        'channel': 'whatsapp',
        'source': gupshupAppName,
        'destination': phoneNumber,
        'message': message,
        'src.name': gupshupAppName,
      },
      // headers: {
      //   'apikey': gupshupApiKey,
      //   'Content-Type': 'application/x-www-form-urlencoded',
      // },
    );
  }

  /// Helper method to encode base64
  String _encodeBase64(String input) {
    // Implementation depends on your base64 encoding method
    return input; // Placeholder
  }

  /// Check WhatsApp availability for a phone number
  Future<bool> isWhatsAppAvailable(String phoneNumber) async {
    try {
      // This would typically check if the number is registered on WhatsApp
      // Implementation depends on the service provider
      return true; // Placeholder
    } catch (e) {
      debugPrint('❌ WhatsApp availability check failed: $e');
      return false;
    }
  }

  /// Get WhatsApp OTP delivery status
  Future<ApiResponse<dynamic>> getDeliveryStatus(String messageId) async {
    try {
      // Implementation depends on the service provider
      return ApiResponse.success({'status': 'delivered'});
    } catch (e) {
      debugPrint('❌ WhatsApp delivery status check failed: $e');
      return ApiResponse.error('Failed to check delivery status');
    }
  }
}
