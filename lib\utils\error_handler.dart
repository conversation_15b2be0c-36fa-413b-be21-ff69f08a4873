import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

class ErrorHandler {
  // Show error dialog
  static void showErrorDialog({
    String? title,
    String? message,
    String? errorCode,
    VoidCallback? onRetry,
    VoidCallback? onOk,
    String? retryButtonText,
    String? okButtonText,
    bool showRetryButton = false,
  }) {
    Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 24,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                title ?? 'Error',
                style: AppTextTheme.cardTitle.copyWith(
                  color: Colors.black87,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              message ?? 'An unexpected error occurred.',
              style: AppTextTheme.cardSubtitle.copyWith(
                color: Colors.grey[600],
              ),
            ),
            if (errorCode != null) ...[
              const SizedBox(height: 12),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Text(
                  'Error Code: $errorCode',
                  style: AppTextTheme.cardCaption.copyWith(
                    color: Colors.grey[700],
                    fontFamily: 'monospace',
                  ),
                ),
              ),
            ],
          ],
        ),
        actions: [
          if (showRetryButton)
            TextButton(
              onPressed: () {
                Get.back();
                onRetry?.call();
              },
              child: Text(
                retryButtonText ?? 'Retry',
                style: AppTextTheme.buttonMedium.copyWith(
                  color: const Color(0xFF2E7D32),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          TextButton(
            onPressed: () {
              Get.back();
              onOk?.call();
            },
            child: Text(
              okButtonText ?? 'OK',
              style: AppTextTheme.buttonMedium.copyWith(
                color: Colors.grey[700],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  // Show error snackbar
  static void showErrorSnackbar({
    String? title,
    String? message,
    Duration? duration,
    VoidCallback? onTap,
  }) {
    Get.snackbar(
      title ?? 'Error',
      message ?? 'Something went wrong',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red,
      colorText: Colors.white,
      duration: duration ?? const Duration(seconds: 4),
      margin: const EdgeInsets.all(16),
      borderRadius: 12,
      icon: const Icon(
        Icons.error_outline,
        color: Colors.white,
      ),
      shouldIconPulse: false,
      onTap: onTap != null ? (_) => onTap() : null,
      mainButton: onTap != null
          ? TextButton(
              onPressed: onTap,
              child: Text(
                'View',
                style: AppTextTheme.buttonMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            )
          : null,
    );
  }

  // Handle API errors specifically
  static void handleApiError({
    required dynamic error,
    VoidCallback? onRetry,
    bool showAsDialog = false,
    bool showAsSnackbar = false,
  }) {
    String title = 'Request Failed';
    String message = 'Failed to process your request. Please try again.';
    String? errorCode;

    // Parse different types of errors
    if (error is String) {
      message = error;
    } else if (error is Map<String, dynamic>) {
      title = error['title'] ?? title;
      message = error['message'] ?? error['error'] ?? message;
      errorCode = error['code']?.toString();
    }

    if (showAsDialog) {
      showErrorDialog(
        title: title,
        message: message,
        errorCode: errorCode,
        onRetry: onRetry,
        showRetryButton: onRetry != null,
      );
    } else {
      showErrorSnackbar(
        title: title,
        message: message,
        onTap: onRetry,
      );
    }
  }

  // Predefined error snackbars
  static void showNetworkError({VoidCallback? onRetry}) {
    showErrorSnackbar(
      title: 'No Internet Connection',
      message: 'Please check your internet connection and try again.',
      onTap: onRetry,
    );
  }

  static void showServerError({String? errorCode, VoidCallback? onRetry}) {
    showErrorSnackbar(
      title: 'Server Error',
      message: 'Our servers are currently experiencing issues. Please try again later.',
      onTap: onRetry,
    );
  }

  static void showAuthError({VoidCallback? onRetry}) {
    showErrorSnackbar(
      title: 'Authentication Failed',
      message: 'Your session has expired. Please login again.',
      onTap: onRetry,
    );
  }

  static void showPermissionError({String? message, VoidCallback? onRetry}) {
    showErrorSnackbar(
      title: 'Permission Denied',
      message: message ?? 'You don\'t have permission to access this feature.',
      onTap: onRetry,
    );
  }

  // Quick error snackbars
  static void networkErrorSnackbar() {
    showErrorSnackbar(
      title: 'No Internet',
      message: 'Please check your connection',
    );
  }

  static void serverErrorSnackbar() {
    showErrorSnackbar(
      title: 'Server Error',
      message: 'Please try again later',
    );
  }

  static void authErrorSnackbar() {
    showErrorSnackbar(
      title: 'Session Expired',
      message: 'Please login again',
    );
  }
}
