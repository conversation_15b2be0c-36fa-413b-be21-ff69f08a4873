import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import '../models/current_rider_status_model.dart';
import '../constants/storage_keys.dart';

class ShiftController extends GetxController {
  final ApiService _apiService = ApiService.instance;
  final SecureStorageService _storage = SecureStorageService.instance;

  // Reactive variables
  final _currentRiderStatus = Rxn<CurrentRiderStatusModel>();
  final _isOnline = false.obs;
  final _isLoading = false.obs;
  final _isOnBreak = false.obs;
  final _breakStartTime = Rxn<DateTime>();

  // Getters
  CurrentRiderStatusModel? get currentRiderStatus => _currentRiderStatus.value;
  bool get isOnline => _isOnline.value;
  bool get isLoading => _isLoading.value;
  bool get isOnBreak => _isOnBreak.value;
  DateTime? get breakStartTime => _breakStartTime.value;

  @override
  void onInit() {
    super.onInit();
    _initializeController();
  }

  /// Initialize controller with async operations
  Future<void> _initializeController() async {
    await _loadStoredStatus();
    await getCurrentRiderStatus();
  }

  /// Load stored status from local storage
  Future<void> _loadStoredStatus() async {
    final storedOnlineStatus = await _storage.read(StorageKeys.riderOnlineStatus);
    final storedBreakStatus = await _storage.read(StorageKeys.riderBreakStatus);
    final storedBreakTime = await _storage.read(StorageKeys.riderBreakStartTime);

    if (storedOnlineStatus != null) {
      _isOnline.value = storedOnlineStatus.toLowerCase() == 'true';
    }

    if (storedBreakStatus != null) {
      _isOnBreak.value = storedBreakStatus.toLowerCase() == 'true';
    }

    if (storedBreakTime != null) {
      _breakStartTime.value = DateTime.tryParse(storedBreakTime);
    }
  }

  /// Get current rider status from API
  Future<void> getCurrentRiderStatus() async {
    try {
      _isLoading.value = true;

      // Get SRID from local storage
      final srid = await _storage.read(StorageKeys.userName) ?? '';
      if (srid.isEmpty) {
        debugPrint('❌ SRID not found in local storage');
        return;
      }

      final response = await _apiService.shift.getCurrentRiderStatus(userId: srid);

      if (response.isSuccess && response.data != null) {
        _currentRiderStatus.value = CurrentRiderStatusModel.fromJson(response.data);
        debugPrint('✅ Current rider status loaded successfully');
      } else {
        debugPrint('❌ Failed to get current rider status: ${response.error}');
      }
    } catch (e) {
      debugPrint('❌ Error getting current rider status: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Update rider online/offline status
  Future<bool> updateOnlineStatus(bool isOnline) async {
    try {
      _isLoading.value = true;

      // Get required data from local storage
      final userid = await _storage.read(StorageKeys.userName);
      final dcid = await _storage.read(StorageKeys.dcid);
      final cdcType = await _storage.read(StorageKeys.cdcType);

      if (userid == null || dcid == null || cdcType == null) {
        debugPrint('❌ Required data not found in local storage');
        return false;
      }

      final deliveryDate = DateTime.now().toIso8601String().split('T')[0];
      final remark = isOnline ? 'Available' : 'Unavailable';
      final status = isOnline ? 1 : 0;

      final response = await _apiService.shift.updateRiderStatus(
        deliveryDate: deliveryDate,
        userid: userid.toString(),
        remark: remark,
        dcid: dcid.toString(),
        cdcType: cdcType.toString(),
        status: status.toString(),
      );

      if (response.isSuccess) {
        _isOnline.value = isOnline;
        await _storage.write(StorageKeys.riderOnlineStatus, isOnline.toString());
        debugPrint('✅ Rider status updated successfully: ${isOnline ? 'Online' : 'Offline'}');

        // Refresh current status
        await getCurrentRiderStatus();
        return true;
      } else {
        debugPrint('❌ Failed to update rider status: ${response.error}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error updating rider status: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Start break
  Future<bool> startBreak(String breakDuration) async {
    try {
      _isLoading.value = true;

      // Get required data from local storage
      final riderId = await _storage.read(StorageKeys.userName);
      final kfhid = await _storage.read(StorageKeys.kfhid);
      final cdcType = await _storage.read(StorageKeys.cdcType);

      if (riderId == null || kfhid == null || cdcType == null) {
        debugPrint('❌ Required data not found in local storage for break');
        return false;
      }

      final response = await _apiService.shift.submitBreakTime(
        userId: riderId.toString(),
        breakType: 'break',
        breakDuration: breakDuration.toString(),
      );

      if (response.isSuccess) {
        _isOnBreak.value = true;
        _breakStartTime.value = DateTime.now();

        await _storage.write(StorageKeys.riderBreakStatus, 'true');
        await _storage.write(StorageKeys.riderBreakStartTime, DateTime.now().toIso8601String());

        debugPrint('✅ Break started successfully');
        return true;
      } else {
        debugPrint('❌ Failed to start break: ${response.error}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error starting break: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Stop break
  Future<void> stopBreak() async {
    _isOnBreak.value = false;
    _breakStartTime.value = null;

    await _storage.delete(StorageKeys.riderBreakStatus);
    await _storage.delete(StorageKeys.riderBreakStartTime);

    debugPrint('✅ Break stopped');
  }

  /// Get break duration in minutes
  int getBreakDurationMinutes() {
    if (_breakStartTime.value == null) return 0;
    return DateTime.now().difference(_breakStartTime.value!).inMinutes;
  }

  /// Check if rider has active orders or is in progress
  bool get hasActiveWork {
    final status = _currentRiderStatus.value?.fECurrentRiderStatus;
    if (status == null) return false;

    return status.attendanceStatus == 1 || status.selfAssignStatus == 1;
  }
}
