import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/controllers/profile_registration_controller.dart';
import 'package:kisankonnect_rider/controllers/auth_controller.dart';
import '../../../widgets/dialogs/bank_verification_dialogs.dart';
import 'step_basic_details.dart';
import 'step_gender.dart';
import 'step_marital_status.dart';
import 'step_address.dart';
import 'step_bank_details.dart';
import 'step_document_upload.dart';
import 'step_work_details.dart';

import 'profile_summary_screen.dart';
import 'package:get/get.dart';
import '../../../../constants/storage_keys.dart';
import '../../../../routes/app_pages.dart';

class CreateProfileScreen extends StatefulWidget {
  final int? initialStep;

  const CreateProfileScreen({super.key, this.initialStep});

  @override
  State<CreateProfileScreen> createState() => _CreateProfileScreenState();
}

class _CreateProfileScreenState extends State<CreateProfileScreen> {
  int _currentStep = 0;
  late ProfileRegistrationController _registrationController;
  AuthController? _authController;
  final SecureStorageService _storage = SecureStorageService.instance;
  int? _regStatus;

  @override
  void initState() {
    super.initState();

    // Get the controllers from GetX dependency injection
    _registrationController = Get.find<ProfileRegistrationController>();

    // Try to get auth controller, but don't fail if it's not available
    try {
      _authController = Get.find<AuthController>();
    } catch (e) {
      debugPrint('⚠️ AuthController not found: $e');
      _authController = null;
    }

    // Load registration status and step progress
    _loadRegistrationStatus();
    _loadStepProgress().then((_) {
      // Log state after loading
      _logCurrentState();
    });
  }

  Future<void> _loadStepProgress() async {
    try {
      // Get mobile number for storage key
      final mobileNumber = await _storage.read(StorageKeys.mobileNumber) ?? '';
      if (mobileNumber.isEmpty) {
        _currentStep = 0;
        debugPrint('🎯 No mobile number found, starting from beginning');
        return;
      }

      // Check regStatus first to determine appropriate step (highest priority for app restart)
      final regStatusStr = await _storage.read(StorageKeys.regStatus);
      final regStatus = regStatusStr != null ? int.tryParse(regStatusStr) : null;

      debugPrint('📋 Checking regStatus: $regStatus');

      // Get initial step from arguments (for direct navigation from login)
      final args = Get.arguments as Map<String, dynamic>?;
      final argStep = args?['initialStep'] ?? widget.initialStep;

      if (argStep != null) {
        // Use argument step but validate against regStatus
        _currentStep = argStep;
        debugPrint('🎯 Using step from arguments: $_currentStep');

        // Validate that the argument step matches regStatus expectations
        if (regStatus != null) {
          final expectedStep = _getStepFromRegStatus(regStatus);
          if (_currentStep != expectedStep) {
            debugPrint('⚠️ Argument step $_currentStep does not match regStatus $regStatus (expected: $expectedStep)');
            debugPrint('🔄 Using regStatus-based step: $expectedStep');
            _currentStep = expectedStep;
          }
        }
        setState(() {}); // Refresh UI with loaded step
        return;
      }

      if (regStatus != null) {
        _currentStep = _getStepFromRegStatus(regStatus);
        debugPrint('🎯 Setting step based on regStatus $regStatus: $_currentStep');
        setState(() {}); // Refresh UI with loaded step
        return;
      }

      // Fallback: Load saved step progress from storage
      final stepKey = StorageKeys.getCurrentStepKey(mobileNumber);
      final savedStepStr = await _storage.read(stepKey);
      if (savedStepStr != null) {
        final savedStep = int.tryParse(savedStepStr);
        if (savedStep != null && savedStep >= 0 && savedStep < _steps.length) {
          _currentStep = savedStep;
          debugPrint('🔄 Resuming from saved step: $_currentStep for mobile: $mobileNumber');
          setState(() {}); // Refresh UI with loaded step
          return;
        }
      }

      // Default to step 0 if no saved progress
      _currentStep = 0;
      debugPrint('🎯 Starting from beginning: $_currentStep');
    } catch (e) {
      debugPrint('🚨 Error loading step progress: $e');
      _currentStep = 0;
    }
  }

  int _getStepFromRegStatus(int regStatus) {
    int step;
    switch (regStatus) {
      case 1:
        // Profile completed - go to documents upload
        step = 5; // Document upload step (was 6, now 5 after removing selfie)
        break;
      case 2:
        // Documents completed - go to work details
        step = 6; // Work details step (was 7, now 6)
        break;
      case 3:
        // Registration complete - go to profile summary
        step = 7; // Profile summary step (was 8, now 7)
        break;
      default:
        // Unknown status - start from beginning
        step = 0;
        break;
    }

    debugPrint('🎯 RegStatus $regStatus mapped to step $step');
    return step;
  }

  void _logCurrentState() {
    debugPrint('📊 === CURRENT STEPPER STATE ===');
    debugPrint('📊 Current Step: $_currentStep');
    debugPrint('📊 RegStatus: $_regStatus');
    debugPrint('📊 Can Navigate Back: $_canNavigateBack');
    debugPrint('📊 Total Steps: ${_steps.length}');
    debugPrint('📊 Step Name: ${_getStepName(_currentStep)}');
    debugPrint('📊 ================================');
  }

  String _getStepName(int step) {
    const stepNames = [
      'Basic Details',
      'Gender',
      'Marital Status',
      'Address',
      'Bank Details',
      'Document Upload',
      'Work Details',
      'Profile Summary'
    ];

    if (step >= 0 && step < stepNames.length) {
      return stepNames[step];
    }
    return 'Unknown Step';
  }

  Future<void> _saveStepProgress() async {
    try {
      final mobileNumber = await _storage.read(StorageKeys.mobileNumber) ?? '';
      if (mobileNumber.isNotEmpty) {
        final stepKey = StorageKeys.getCurrentStepKey(mobileNumber);
        await _storage.write(stepKey, _currentStep.toString());
        debugPrint('💾 Saved step progress: $_currentStep for mobile: $mobileNumber');
      }
    } catch (e) {
      debugPrint('🚨 Error saving step progress: $e');
    }
  }

  Future<void> _loadRegistrationStatus() async {
    try {
      final regStatusStr = await _storage.read(StorageKeys.regStatus);
      if (regStatusStr != null) {
        _regStatus = int.tryParse(regStatusStr);
        debugPrint('📋 Registration Status: $_regStatus');
        setState(() {}); // Refresh UI with loaded status
      }
    } catch (e) {
      debugPrint('🚨 Error loading registration status: $e');
    }
  }

  bool get _canNavigateBack {
    // Don't allow back navigation if regStatus is 2 or 3 (completed registration)
    if (_regStatus != null && (_regStatus == 2 || _regStatus == 3)) {
      return false;
    }

    return _canNavigateBackFromCurrentStep();
  }

  /// Check if back navigation is allowed from current step based on API success
  bool _canNavigateBackFromCurrentStep() {
    try {
      final registrationController = Get.find<ProfileRegistrationController>();

      // Step mapping (after removing selfie step):
      // 0: Basic Details, 1: Gender, 2: Marital, 3: Address, 4: Bank Details,
      // 5: Document Upload, 6: Work Details, 7: Summary

      // If document API was successful (200 OK), don't allow back to work details or earlier
      if (registrationController.documentApiSuccess && _currentStep >= 6) {
        debugPrint('🚫 Back navigation blocked: Document API successful, current step: $_currentStep');
        return false;
      }

      // If profile API was successful (200 OK), don't allow back to profile-related steps
      if (registrationController.profileApiSuccess && _currentStep >= 5) {
        debugPrint('🚫 Back navigation blocked: Profile API successful, current step: $_currentStep');
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('⚠️ Controller not found for navigation check: $e');
      return true; // Allow navigation if controller not available
    }
  }

  void _nextStep() {
    if (_currentStep < _steps.length - 1) {
      setState(() => _currentStep++);
      _saveStepProgress(); // Save progress when moving forward
    }
  }

  void _prevStep() {
    if (_currentStep > 0 && _canNavigateBackFromCurrentStep()) {
      setState(() => _currentStep--);
      _saveStepProgress(); // Save progress when moving backward
    } else if (!_canNavigateBackFromCurrentStep()) {
      // Show message why back navigation is not allowed
      String message = 'Cannot go back from this step.';

      try {
        final registrationController = Get.find<ProfileRegistrationController>();
        if (registrationController.documentApiSuccess && _currentStep >= 6) {
          message = 'Cannot go back - Documents have been successfully uploaded and submitted.';
        } else if (registrationController.profileApiSuccess && _currentStep >= 5) {
          message = 'Cannot go back - Profile details have been successfully submitted.';
        }
      } catch (e) {
        // Use default message if controller not found
      }

      Get.snackbar(
        'Navigation Restricted',
        message,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 3),
      );
    }
  }

  /// Show bank details info dialog
  void _showBankDetailsInfoDialog() {
    BankVerificationDialogs.showBankDetailsInfoDialog(
      context: context,
      onOkay: () {
        // Proceed to next step after user acknowledges
        setState(() => _currentStep++);
        _saveStepProgress();
      },
    );
  }

  /// Show logout confirmation dialog
  void _showLogoutDialog() {
    // Check if auth controller is available
    if (_authController == null) {
      Get.snackbar(
        'Error',
        'Authentication service not available. Please restart the app.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    Get.dialog(
      AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout? Your progress will be saved.'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          Obx(() => TextButton(
                onPressed: (_authController?.isLoading ?? false)
                    ? null
                    : () {
                        Get.back();
                        _performLogout();
                      },
                child: (_authController?.isLoading ?? false)
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.red),
                        ),
                      )
                    : const Text('Logout', style: TextStyle(color: Colors.red)),
              )),
        ],
      ),
    );
  }

  /// Perform logout with loading dialog
  void _performLogout() async {
    try {
      // Check if auth controller is available
      if (_authController == null) {
        Get.snackbar(
          'Error',
          'Authentication service not available. Please restart the app.',
          backgroundColor: Colors.red,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
        );
        return;
      }

      // Show loading dialog
      Get.dialog(
        const AlertDialog(
          content: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Logging out...'),
            ],
          ),
        ),
        barrierDismissible: false,
      );

      // Perform logout
      await _authController!.logout();

      // Close loading dialog if still open
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }
    } catch (e) {
      // Close loading dialog if still open
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      Get.snackbar(
        'Error',
        'Failed to logout properly. Please try again.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// Get step names for display
  List<String> get _stepNames => [
        'Basic Details',
        'Gender',
        'Marital Status',
        'Address',
        'Bank Details',
        'Document Upload',
        'Work Details',
        'Profile Summary'
      ];

  /// Get current step name
  String get _currentStepName {
    if (_currentStep < _stepNames.length) {
      return _stepNames[_currentStep];
    }
    return 'Profile Creation';
  }

  List<Widget> get _steps => [
        StepBasicDetails(onContinue: (data) {
          debugPrint('📝 Basic Details Data: $data');
          _registrationController.updateMultipleProfileData(data);
          setState(() => _currentStep++);
          _saveStepProgress();
        }),
        StepGender(onContinue: (gender) {
          debugPrint('👤 Gender Data: $gender');
          _registrationController.updateProfileData('gender', gender['gender']);
          setState(() => _currentStep++);
          _saveStepProgress();
        }),
        StepMaritalStatus(onContinue: (data) {
          debugPrint('💍 Marital Status Data: $data');

          // Update marital status
          _registrationController.updateProfileData('maritalStatus', data['status']);

          // Update family information if provided
          if (data.containsKey('spouseName')) {
            _registrationController.updateProfileData('spouseName', data['spouseName']);
          }
          if (data.containsKey('spouseDob')) {
            _registrationController.updateProfileData('spouseDob', data['spouseDob']);
          }
          if (data.containsKey('childName')) {
            _registrationController.updateProfileData('childName', data['childName']);
          }
          if (data.containsKey('childDob')) {
            _registrationController.updateProfileData('childDob', data['childDob']);
          }

          setState(() => _currentStep++);
          _saveStepProgress();
        }),
        StepAddress(onContinue: (address) {
          debugPrint('🏠 Address Data: $address');
          _registrationController.updateMultipleProfileData(address);
          setState(() => _currentStep++);
          _saveStepProgress();
        }),
        StepBankDetails(
          onContinue: (data) {
            debugPrint('🏦 Bank Details Data: $data');
            _registrationController.updateMultipleProfileData(data);
            // Move to next step directly - bank verification is handled within the step
            setState(() => _currentStep++);
            _saveStepProgress();
          },
          onSkip: () {
            debugPrint('⏭️ Bank details skipped');
            _showBankDetailsInfoDialog();
          },
        ),
        StepDocumentUpload(
          onContinue: (data) {
            // Document API is called within the step itself
            // Just move to next step - data is already updated in the step
            setState(() => _currentStep++);
            _saveStepProgress();
          },
          onBack: () {},
        ),
        StepWorkDetails(
          onContinue: (data) async {
            data.forEach((key, value) {
              _registrationController.updateWorkData(key, value);
            });

            // Submit work details API before proceeding to summary
            debugPrint('📝 Submitting work details API...');
            final success = await _registrationController.submitWorkDetails();

            if (success) {
              debugPrint('✅ Work details submitted successfully');
              // Proceed to profile summary screen
              setState(() => _currentStep++);
              _saveStepProgress();
            } else {
              debugPrint('❌ Failed to submit work details');
              Get.snackbar(
                'Error',
                'Failed to submit work details. Please try again.',
                backgroundColor: Colors.red,
                colorText: Colors.white,
                snackPosition: SnackPosition.BOTTOM,
              );
            }
          },
          // onSkip is no longer needed as it's handled internally
        ),
        ProfileSummaryScreen(
          onConfirm: () {
            // After profile summary, navigate to delivery partner welcome screen
            debugPrint('✅ Profile summary completed, navigating to delivery partner welcome screen');
            Get.offAllNamed(AppRoutes.deliveryPartnerWelcome);
          },
        ),
      ];

  @override
  Widget build(BuildContext context) {
    // Steps that handle their own navigation: all steps have their own Continue buttons
    final stepsWithOwnNav = {
      0, // basic details
      1, // gender
      2, // marital status
      3, // address
      4, // bank details
      5, // document upload
      6, // work details
      7 // profile summary
    };
    return PopScope(
      canPop: _canNavigateBack,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop && !_canNavigateBack) {
          // Show specific message based on why navigation is blocked
          String message = 'You cannot go back as your registration is already completed.';

          try {
            final registrationController = Get.find<ProfileRegistrationController>();
            if (registrationController.documentApiSuccess && _currentStep >= 6) {
              message = 'Cannot go back - Documents have been successfully uploaded and submitted.';
            } else if (registrationController.profileApiSuccess && _currentStep >= 5) {
              message = 'Cannot go back - Profile details have been successfully submitted.';
            }
          } catch (e) {
            // Use default message if controller not found
          }

          Get.snackbar(
            'Navigation Restricted',
            message,
            backgroundColor: Colors.orange,
            colorText: Colors.white,
            snackPosition: SnackPosition.BOTTOM,
            duration: const Duration(seconds: 3),
          );
        }
      },
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          backgroundColor: AppColors.green,
          elevation: 0,
          leading: _canNavigateBack
              ? IconButton(
                  icon: const Icon(Icons.arrow_back, color: Colors.white),
                  onPressed: () {
                    if (_currentStep > 0) {
                      _prevStep();
                    } else {
                      Get.back();
                    }
                  },
                )
              : null, // Hide back button if navigation not allowed
          title: Text(
            _currentStepName,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          centerTitle: true,
          actions: [
            if (_authController != null)
              IconButton(
                icon: const Icon(Icons.logout, color: Colors.white),
                onPressed: _showLogoutDialog,
                tooltip: 'Logout',
              ),
          ],
        ),
        body: Column(
          children: [
            _ProfileStepper(currentStep: _currentStep, totalSteps: _steps.length),
            Expanded(child: _steps[_currentStep]),
          ],
        ),
        bottomNavigationBar: (!stepsWithOwnNav.contains(_currentStep) && _currentStep < _steps.length - 1)
            ? Container(
                color: Colors.white,
                child: Padding(
                  padding: EdgeInsets.only(
                    left: 16.0,
                    right: 16.0,
                    top: 16.0,
                    bottom: MediaQuery.of(context).viewInsets.bottom + 16.0,
                  ),
                  child: Row(
                    children: [
                      if (_currentStep > 0 && _canNavigateBack)
                        Expanded(
                          child: OutlinedButton(
                            onPressed: _prevStep,
                            style: OutlinedButton.styleFrom(
                              side: const BorderSide(color: AppColors.green),
                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(32)),
                            ),
                            child: const Text('Back', style: TextStyle(color: AppColors.green)),
                          ),
                        ),
                      if (_currentStep > 0 && _canNavigateBack) const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _nextStep,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.green,
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(32)),
                          ),
                          child: Text(_currentStep == _steps.length - 2 ? 'Finish' : 'Continue'),
                        ),
                      ),
                    ],
                  ),
                ),
              )
            : null,
      ),
    );
  }
}

class _ProfileStepper extends StatelessWidget {
  final int currentStep;
  final int totalSteps;
  const _ProfileStepper({required this.currentStep, required this.totalSteps});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 4, offset: Offset(0, 2))],
      ),
      child: Row(
        children: List.generate(totalSteps, (index) {
          final isCompleted = index < currentStep;
          final isCurrent = index == currentStep;

          return Expanded(
            child: Container(
              height: 4,
              margin: EdgeInsets.only(right: index < totalSteps - 1 ? 8 : 0),
              decoration: BoxDecoration(
                color: isCompleted || isCurrent ? AppColors.green : Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          );
        }),
      ),
    );
  }
}
