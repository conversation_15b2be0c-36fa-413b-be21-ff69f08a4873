/// Model for Rider Details API response
class RiderDetailsResponse {
  final String status;
  final String msg;
  final RiderDetail? riderDetail;

  RiderDetailsResponse({
    required this.status,
    required this.msg,
    this.riderDetail,
  });

  factory RiderDetailsResponse.fromJson(Map<String, dynamic> json) {
    return RiderDetailsResponse(
      status: json['status'] ?? '',
      msg: json['msg'] ?? '',
      riderDetail: json['fE_RiderDetail'] != null ? RiderDetail.fromJson(json['fE_RiderDetail']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'msg': msg,
      'fE_RiderDetail': riderDetail?.toJson(),
    };
  }

  bool get isSuccess => status == '200';
}

/// Model for individual rider details
class RiderDetail {
  final String firstName;
  final String lastName;
  final String mobileNo;
  final String emailID;
  final String dcid;
  final int vehicleType;
  final String? panCardPhoto;
  final String? dlPhoto;
  final String? selfImg;
  final String dlNo;
  final String shiftID;
  final int riderType;
  final String referBy;
  final String dob;
  final String bankName;
  final String branchName;
  final String accountNumber;
  final String ifscCode;
  final String fullAddress;
  final String landmark;
  final String panNo;
  final int isFlag;
  final int genderType;
  final int maritalStatus;
  final String weekoffDays;
  final int slotName;
  final String societyName;
  final String roomFlatNo;
  final String subSlotName;
  final String? spouseName;
  final String? sDob;
  final String? childName;
  final String? cDob;

  RiderDetail({
    required this.firstName,
    required this.lastName,
    required this.mobileNo,
    required this.emailID,
    required this.dcid,
    required this.vehicleType,
    this.panCardPhoto,
    this.dlPhoto,
    this.selfImg,
    required this.dlNo,
    required this.shiftID,
    required this.riderType,
    required this.referBy,
    required this.dob,
    required this.bankName,
    required this.branchName,
    required this.accountNumber,
    required this.ifscCode,
    required this.fullAddress,
    required this.landmark,
    required this.panNo,
    required this.isFlag,
    required this.genderType,
    required this.maritalStatus,
    required this.weekoffDays,
    required this.slotName,
    required this.societyName,
    required this.roomFlatNo,
    required this.subSlotName,
    this.spouseName,
    this.sDob,
    this.childName,
    this.cDob,
  });

  factory RiderDetail.fromJson(Map<String, dynamic> json) {
    return RiderDetail(
      firstName: json['firstName'] ?? '',
      lastName: json['lastName'] ?? '',
      mobileNo: json['mobileNo'] ?? '',
      emailID: json['emailID'] ?? '',
      dcid: json['dcid'] ?? '',
      vehicleType: json['vehicleType'] ?? 0,
      panCardPhoto: json['panCardPhoto'],
      dlPhoto: json['dlPhoto'],
      selfImg: json['selfImg'],
      dlNo: json['dlNo'] ?? '',
      shiftID: json['shiftID'] ?? '',
      riderType: json['riderType'] ?? 0,
      referBy: json['referBy'] ?? '',
      dob: json['dob'] ?? '',
      bankName: json['bankName'] ?? '',
      branchName: json['branchName'] ?? '',
      accountNumber: json['accountNumber'] ?? '',
      ifscCode: json['ifscCode'] ?? '',
      fullAddress: json['fullAddress'] ?? '',
      landmark: json['landmark'] ?? '',
      panNo: json['panNo'] ?? '',
      isFlag: json['isFlag'] ?? 0,
      genderType: json['genderType'] ?? 0,
      maritalStatus: json['maritalStatus'] ?? 0,
      weekoffDays: json['weekoffDays'] ?? '',
      slotName: json['slotName'] ?? 0,
      societyName: json['societyName'] ?? '',
      roomFlatNo: json['roomFlatNo'] ?? '',
      subSlotName: json['subSlotName'] ?? '',
      spouseName: json['spouseName'],
      sDob: json['sDob'],
      childName: json['childName'],
      cDob: json['cDob'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'firstName': firstName,
      'lastName': lastName,
      'mobileNo': mobileNo,
      'emailID': emailID,
      'dcid': dcid,
      'vehicleType': vehicleType,
      'panCardPhoto': panCardPhoto,
      'dlPhoto': dlPhoto,
      'selfImg': selfImg,
      'dlNo': dlNo,
      'shiftID': shiftID,
      'riderType': riderType,
      'referBy': referBy,
      'dob': dob,
      'bankName': bankName,
      'branchName': branchName,
      'accountNumber': accountNumber,
      'ifscCode': ifscCode,
      'fullAddress': fullAddress,
      'landmark': landmark,
      'panNo': panNo,
      'isFlag': isFlag,
      'genderType': genderType,
      'maritalStatus': maritalStatus,
      'weekoffDays': weekoffDays,
      'slotName': slotName,
      'societyName': societyName,
      'roomFlatNo': roomFlatNo,
      'subSlotName': subSlotName,
      'spouseName': spouseName,
      'sDob': sDob,
      'childName': childName,
      'cDob': cDob,
    };
  }

  /// Get full name
  String get fullName => '$firstName $lastName'.trim();

  /// Get formatted gender
  String get genderText {
    switch (genderType) {
      case 1:
        return 'Male';
      case 2:
        return 'Female';
      case 3:
        return 'Other';
      default:
        return 'Male';
    }
  }

  /// Get formatted marital status
  String get maritalStatusText {
    switch (maritalStatus) {
      case 1:
        return 'Single';
      case 2:
        return 'Married';
      default:
        return 'Single';
    }
  }

  /// Get formatted date of birth
  String get formattedDob {
    try {
      final date = DateTime.parse(dob);
      return '${date.day.toString().padLeft(2, '0')}-${date.month.toString().padLeft(2, '0')}-${date.year}';
    } catch (e) {
      return dob;
    }
  }

  /// Get vehicle type text
  String get vehicleTypeText {
    switch (vehicleType) {
      case 1:
        return 'Bike';
      case 2:
        return 'Scooter';
      case 3:
        return 'Bicycle';
      case 4:
        return 'EV Scooter';
      default:
        return 'Vehicle';
    }
  }

  /// Check if profile is complete
  bool get isProfileComplete => isFlag == 1;

  /// Get complete address
  String get completeAddress {
    final parts = [societyName, roomFlatNo, fullAddress, landmark].where((part) => part.isNotEmpty).toList();
    return parts.join(', ');
  }
}
