# OTP Autofill Troubleshooting Guide

## Overview

The KisanKonnect Rider app uses the `otp_autofill` package with `pinput` for OTP input. This guide helps troubleshoot common autofill issues.

## Current Implementation

### Package Used
- `otp_autofill: ^4.1.0` - For SMS reading and OTP extraction
- `pinput: ^5.0.0` - For OTP input UI

### How It Works

1. **SMS Detection**: `OTPTextEditController` listens for incoming SMS
2. **OTP Extraction**: Multiple regex patterns extract OTP from SMS
3. **UI Update**: Extracted OTP is set in the `Pinput` widget
4. **Auto-verification**: Complete OTP triggers automatic verification

## Common Issues & Solutions

### 1. OTP Reading but Not Filling

**Symptoms:**
- Console shows "Extracted OTP code: XXXXXX"
- Pinput fields remain empty
- Manual input works fine

**Causes & Solutions:**

#### Controller Mismatch
```dart
// ❌ Wrong: Using OTPTextEditController directly with Pinput
Pinput(controller: _otpController, ...)

// ✅ Correct: Using separate TextEditingController
Pinput(controller: _pinputController, ...)
```

#### Missing UI Updates
```dart
// ✅ Ensure UI updates on main thread
WidgetsBinding.instance.addPostFrameCallback((_) {
  if (mounted) {
    _pinputController.text = extractedCode;
    widget.onChanged(extractedCode);
    widget.onCompleted(extractedCode);
  }
});
```

### 2. SMS Not Being Detected

**Check App Signature:**
```dart
final otpInteractor = OTPInteractor();
final signature = await otpInteractor.getAppSignature();
debugPrint('App signature: $signature');
```

**Verify Permissions:**
- SMS reading permission in AndroidManifest.xml
- User consent for SMS access

### 3. OTP Extraction Failing

**Test Extraction Patterns:**
```dart
// Current patterns used:
final patterns = [
  RegExp(r'(\d{6})'), // 6 digits
  RegExp(r'OTP[:\s]*(\d+)', caseSensitive: false),
  RegExp(r'code[:\s]*(\d+)', caseSensitive: false),
  RegExp(r'verification[:\s]*(\d+)', caseSensitive: false),
];
```

**Test with Sample Messages:**
```dart
OtpDebugHelper.testOtpExtraction();
```

## Debug Tools

### 1. Debug Buttons (Debug Mode Only)

In the login screen, two debug buttons are available:

- **Test OTP**: Simulates OTP autofill with "123456"
- **Debug OTP**: Prints current status and tests extraction

### 2. Debug Helper Methods

```dart
// Print current OTP status
await OtpDebugHelper.printStatus();

// Test extraction with sample messages
OtpDebugHelper.testOtpExtraction();

// Simulate OTP SMS
OtpDebugHelper.simulateOtpSms('123456');
```

### 3. Console Logs

Look for these log messages:

```
📱 App signature: [signature]
📱 Raw SMS received: [message]
📱 Extracted OTP code: [code]
📱 OTP controller initialized successfully
```

## Testing Steps

### 1. Basic Functionality Test

1. Run app in debug mode
2. Navigate to OTP screen
3. Use "Test OTP" button
4. Verify OTP fills in Pinput fields
5. Check auto-verification works

### 2. Real SMS Test

1. Send actual OTP SMS to device
2. Check console for SMS detection logs
3. Verify OTP extraction and filling
4. Test with different SMS formats

### 3. Permission Test

1. Check SMS permissions in device settings
2. Verify app has SMS access
3. Test user consent flow

## Known Issues & Workarounds

### Issue 1: Android 14+ SMS Restrictions
**Problem**: Stricter SMS access in newer Android versions
**Workaround**: Ensure proper permissions and user consent

### Issue 2: Custom SMS Formats
**Problem**: OTP not extracted from custom SMS formats
**Solution**: Add custom regex patterns

### Issue 3: Timing Issues
**Problem**: SMS received before listener is ready
**Workaround**: Initialize listener early in app lifecycle

## Alternative Implementation

If current implementation fails, try the enhanced OTP widget:

```dart
// Use EnhancedOtpWidget instead of PinputOtpWidget
EnhancedOtpWidget(
  length: 6,
  onCompleted: (otp) => verifyOtp(),
  onChanged: (otp) => setState(() => _currentOtp = otp),
)
```

## Best Practices

1. **Early Initialization**: Initialize OTP listener as early as possible
2. **Error Handling**: Always wrap OTP operations in try-catch
3. **User Feedback**: Provide fallback for manual OTP entry
4. **Testing**: Test on multiple devices and Android versions
5. **Permissions**: Request SMS permissions gracefully

## Debugging Checklist

- [ ] App signature is correct
- [ ] SMS permissions granted
- [ ] OTP listener initialized successfully
- [ ] SMS detection logs appear
- [ ] OTP extraction works with test messages
- [ ] Pinput controller receives extracted OTP
- [ ] UI updates on main thread
- [ ] Auto-verification triggers correctly

## Support

For additional help:
1. Check console logs for error messages
2. Use debug tools to isolate issues
3. Test with known working SMS formats
4. Verify device permissions and settings
