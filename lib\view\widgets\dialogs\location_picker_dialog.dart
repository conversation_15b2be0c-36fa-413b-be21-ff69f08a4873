import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'address_details_dialog.dart';

/// Location Picker Dialog with Map Integration
class LocationPickerDialog extends StatefulWidget {
  final Function(Map<String, String>)? onLocationSelected;

  const LocationPickerDialog({
    super.key,
    this.onLocationSelected,
  });

  @override
  State<LocationPickerDialog> createState() => _LocationPickerDialogState();

  /// Show the location picker dialog
  static Future<Map<String, String>?> show({
    required BuildContext context,
    Function(Map<String, String>)? onLocationSelected,
  }) async {
    return await showDialog<Map<String, String>>(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return LocationPickerDialog(onLocationSelected: onLocationSelected);
      },
    );
  }
}

class _LocationPickerDialogState extends State<LocationPickerDialog> {
  final _searchController = TextEditingController();
  bool _isLoading = false;
  String _selectedAddress = '';
  double? _selectedLat;
  double? _selectedLng;

  // Mock map center position
  Offset _pinPosition = const Offset(0.5, 0.5); // Center of the map (0.0 to 1.0)
  bool _isDragging = false;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.arrow_back, color: Colors.black),
                  ),
                  const Expanded(
                    child: Text(
                      'Select your location',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Search Bar
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: TextField(
                controller: _searchController,
                decoration: const InputDecoration(
                  hintText: 'Search for Area, street name...',
                  prefixIcon: Icon(Icons.search, color: Colors.grey),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
                onChanged: (value) {
                  // Implement search functionality
                  _searchLocation(value);
                },
              ),
            ),

            const SizedBox(height: 16),

            // Google Maps Container
            Expanded(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Stack(
                    children: [
                      // Interactive Map Simulation
                      GestureDetector(
                        onPanUpdate: (details) {
                          setState(() {
                            _isDragging = true;
                            // Convert global position to relative position (0.0 to 1.0)
                            RenderBox box = context.findRenderObject() as RenderBox;
                            Offset localPosition = box.globalToLocal(details.globalPosition);
                            _pinPosition = Offset(
                              (localPosition.dx / box.size.width).clamp(0.0, 1.0),
                              (localPosition.dy / box.size.height).clamp(0.0, 1.0),
                            );
                          });
                        },
                        onPanEnd: (details) {
                          setState(() {
                            _isDragging = false;
                          });
                          _updateAddressFromPinPosition();
                        },
                        onTapUp: (details) {
                          setState(() {
                            // Convert tap position to relative position
                            RenderBox box = context.findRenderObject() as RenderBox;
                            Offset localPosition = box.globalToLocal(details.globalPosition);
                            _pinPosition = Offset(
                              (localPosition.dx / box.size.width).clamp(0.0, 1.0),
                              (localPosition.dy / box.size.height).clamp(0.0, 1.0),
                            );
                          });
                          _updateAddressFromPinPosition();
                        },
                        child: Container(
                          width: double.infinity,
                          height: double.infinity,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                Colors.green.shade100,
                                Colors.blue.shade100,
                                Colors.grey.shade200,
                              ],
                            ),
                          ),
                          child: Stack(
                            children: [
                              // Map-like pattern overlay
                              CustomPaint(
                                size: Size.infinite,
                                painter: MapPatternPainter(),
                              ),
                              // Interactive area indicator
                              Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  color: Colors.blue.withValues(alpha: 0.05),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      // Map overlay message
                      Positioned(
                        top: 16,
                        left: 16,
                        right: 16,
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.black87,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Text(
                            'Order will be delivered here\nPlace the pin accurately on the map',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),

                      // Draggable pin
                      Positioned(
                        left: _pinPosition.dx * (MediaQuery.of(context).size.width - 32) - 16,
                        top: _pinPosition.dy * (MediaQuery.of(context).size.height * 0.6) - 20,
                        child: AnimatedScale(
                          scale: _isDragging ? 1.2 : 1.0,
                          duration: const Duration(milliseconds: 200),
                          child: const Icon(
                            Icons.location_pin,
                            color: Colors.red,
                            size: 40,
                            shadows: [
                              Shadow(
                                color: Colors.black26,
                                offset: Offset(2, 2),
                                blurRadius: 4,
                              ),
                            ],
                          ),
                        ),
                      ),

                      // Locate Me Button
                      Positioned(
                        bottom: 80,
                        right: 16,
                        child: FloatingActionButton(
                          onPressed: _getCurrentLocation,
                          backgroundColor: AppColors.green,
                          child: _isLoading
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                )
                              : const Icon(
                                  Icons.my_location,
                                  color: Colors.white,
                                ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Selected Location Info
            if (_selectedAddress.isNotEmpty)
              Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.location_on, color: AppColors.green),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Nerul',
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                          Text(
                            _selectedAddress,
                            style: const TextStyle(
                              color: Colors.grey,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        // Change location
                      },
                      child: const Text(
                        'Change',
                        style: TextStyle(color: AppColors.green),
                      ),
                    ),
                  ],
                ),
              ),

            // Confirm Location Button
            Container(
              padding: const EdgeInsets.all(16),
              child: SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton(
                  onPressed: _selectedAddress.isNotEmpty ? _confirmLocation : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.green,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'Confirm location',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _searchLocation(String query) {
    // Implement location search functionality
    // This would typically call a geocoding service
    if (query.isNotEmpty) {
      setState(() {
        _selectedAddress = query;
      });
    }
  }

  void _updateAddressFromPinPosition() {
    // Convert pin position to mock coordinates (Navi Mumbai area)
    double baseLat = 19.0330;
    double baseLng = 73.0297;

    // Add some variation based on pin position
    double lat = baseLat + (_pinPosition.dy - 0.5) * 0.01; // ±0.005 degrees
    double lng = baseLng + (_pinPosition.dx - 0.5) * 0.01; // ±0.005 degrees

    _updateAddressFromCoordinates(lat, lng);
  }

  Future<void> _updateAddressFromCoordinates(double lat, double lng) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(lat, lng);

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        String address = '${place.street}, ${place.locality}, ${place.administrativeArea}, ${place.country}';

        setState(() {
          _selectedAddress = address;
          _selectedLat = lat;
          _selectedLng = lng;
        });
      }
    } catch (e) {
      // Handle error silently for map movements
      debugPrint('Error getting address: $e');
    }
  }

  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Check location permission
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('Location permissions are denied');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('Location permissions are permanently denied');
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
        ),
      );

      // Update pin position to center (simulating current location)
      setState(() {
        _pinPosition = const Offset(0.5, 0.5); // Center the pin
      });

      // Update address
      await _updateAddressFromCoordinates(
        position.latitude,
        position.longitude,
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error getting location: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _confirmLocation() async {
    if (_selectedAddress.isNotEmpty) {
      // Show address details dialog
      final addressDetails = await AddressDetailsDialog.show(
        context: context,
        selectedLocation: 'Nerul', // Extract location name from address
        selectedAddress: _selectedAddress,
      );

      if (addressDetails != null) {
        // Combine location data with address details
        final completeLocationData = {
          'address': _selectedAddress,
          'latitude': _selectedLat?.toString() ?? '',
          'longitude': _selectedLng?.toString() ?? '',
          'societyName': addressDetails['societyName'] ?? '',
          'flatDetails': addressDetails['flatDetails'] ?? '',
          'nearestArea': addressDetails['nearestArea'] ?? '',
          'landmark': addressDetails['landmark'] ?? '',
        };

        if (mounted) {
          Navigator.of(context).pop(completeLocationData);
          widget.onLocationSelected?.call(completeLocationData);
        }
      }
    }
  }
}

/// Custom painter to create a map-like pattern
class MapPatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey.withValues(alpha: 0.3)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // Draw grid lines to simulate map streets
    const gridSpacing = 40.0;

    // Vertical lines
    for (double x = 0; x < size.width; x += gridSpacing) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // Horizontal lines
    for (double y = 0; y < size.height; y += gridSpacing) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }

    // Draw some "buildings" (rectangles)
    final buildingPaint = Paint()
      ..color = Colors.grey.withValues(alpha: 0.2)
      ..style = PaintingStyle.fill;

    final buildings = [
      Rect.fromLTWH(50, 60, 30, 40),
      Rect.fromLTWH(120, 80, 25, 35),
      Rect.fromLTWH(200, 50, 40, 30),
      Rect.fromLTWH(80, 150, 35, 45),
      Rect.fromLTWH(180, 140, 30, 25),
      Rect.fromLTWH(250, 120, 20, 40),
    ];

    for (final building in buildings) {
      if (building.right < size.width && building.bottom < size.height) {
        canvas.drawRect(building, buildingPaint);
      }
    }

    // Draw some "roads" (thicker lines)
    final roadPaint = Paint()
      ..color = Colors.grey.withValues(alpha: 0.4)
      ..strokeWidth = 3.0
      ..style = PaintingStyle.stroke;

    // Main horizontal road
    canvas.drawLine(
      Offset(0, size.height * 0.6),
      Offset(size.width, size.height * 0.6),
      roadPaint,
    );

    // Main vertical road
    canvas.drawLine(
      Offset(size.width * 0.4, 0),
      Offset(size.width * 0.4, size.height),
      roadPaint,
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
