class KFHServiceLevelReportResponse {
  final String status;
  final String msg;
  final ServiceLevelReportData reportData;
  final List<RiderData> riders;

  KFHServiceLevelReportResponse({
    required this.status,
    required this.msg,
    required this.reportData,
    required this.riders,
  });

  factory KFHServiceLevelReportResponse.fromJson(Map<String, dynamic> json) {
    return KFHServiceLevelReportResponse(
      status: json['status'] ?? '',
      msg: json['msg'] ?? '',
      reportData: ServiceLevelReportData.fromJson(
        json['fE_KFHServiceLevelReportForDCIncharge'] ?? {},
      ),
      riders:
          (json['rider'] as List<dynamic>?)?.map((item) => RiderData.fromJson(item as Map<String, dynamic>)).toList() ??
              [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'msg': msg,
      'fE_KFHServiceLevelReportForDCIncharge': reportData.toJson(),
      'rider': riders.map((item) => item.toJson()).toList(),
    };
  }
}

class ServiceLevelReportData {
  final String? depoName;
  final int totalOrder;
  final int delivered;
  final int cancelled;
  final int crtReturn;
  final int crtMissing;
  final int pending;
  final int partiallyreturn;
  final int partiallymissing;
  final int nonassignorder;
  final int ontime;
  final int delay;
  final int early;
  final int thirdPartyOrder; // _3PlOrder
  final int inprogress;
  final int orderAllocated;

  ServiceLevelReportData({
    this.depoName,
    required this.totalOrder,
    required this.delivered,
    required this.cancelled,
    required this.crtReturn,
    required this.crtMissing,
    required this.pending,
    required this.partiallyreturn,
    required this.partiallymissing,
    required this.nonassignorder,
    required this.ontime,
    required this.delay,
    required this.early,
    required this.thirdPartyOrder,
    required this.inprogress,
    required this.orderAllocated,
  });

  factory ServiceLevelReportData.fromJson(Map<String, dynamic> json) {
    return ServiceLevelReportData(
      depoName: json['depoName'],
      totalOrder: json['totalOrder'] ?? 0,
      delivered: json['delivered'] ?? 0,
      cancelled: json['cancelled'] ?? 0,
      crtReturn: json['crtReturn'] ?? 0,
      crtMissing: json['crtMissing'] ?? 0,
      pending: json['pending'] ?? 0,
      partiallyreturn: json['partiallyreturn'] ?? 0,
      partiallymissing: json['partiallymissing'] ?? 0,
      nonassignorder: json['nonassignorder'] ?? 0,
      ontime: json['ontime'] ?? 0,
      delay: json['delay'] ?? 0,
      early: json['early'] ?? 0,
      thirdPartyOrder: json['_3PlOrder'] ?? 0,
      inprogress: json['inprogress'] ?? 0,
      orderAllocated: json['orderAllocated'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'depoName': depoName,
      'totalOrder': totalOrder,
      'delivered': delivered,
      'cancelled': cancelled,
      'crtReturn': crtReturn,
      'crtMissing': crtMissing,
      'pending': pending,
      'partiallyreturn': partiallyreturn,
      'partiallymissing': partiallymissing,
      'nonassignorder': nonassignorder,
      'ontime': ontime,
      'delay': delay,
      'early': early,
      '_3PlOrder': thirdPartyOrder,
      'inprogress': inprogress,
      'orderAllocated': orderAllocated,
    };
  }

  // Helper methods for UI
  double get ontimePercentage {
    final total = ontime + delay + early;
    return total > 0 ? (ontime / total * 100) : 0.0;
  }

  double get delayPercentage {
    final total = ontime + delay + early;
    return total > 0 ? (delay / total * 100) : 0.0;
  }

  double get earlyPercentage {
    final total = ontime + delay + early;
    return total > 0 ? (early / total * 100) : 0.0;
  }
}

class RiderData {
  // This class can be expanded when rider data is available in the API response
  final String? riderId;
  final String? riderName;

  RiderData({
    this.riderId,
    this.riderName,
  });

  factory RiderData.fromJson(Map<String, dynamic> json) {
    return RiderData(
      riderId: json['riderId'],
      riderName: json['riderName'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'riderId': riderId,
      'riderName': riderName,
    };
  }
}
