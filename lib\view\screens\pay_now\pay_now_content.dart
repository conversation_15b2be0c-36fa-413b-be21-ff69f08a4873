import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

import 'package:kisankonnect_rider/view/widgets/common/common_app_bar.dart';

class PayNowContent extends StatefulWidget {
  final VoidCallback? onBackPressed;

  const PayNowContent({
    super.key,
    this.onBackPressed,
  });

  @override
  State<PayNowContent> createState() => _PayNowContentState();
}

class _PayNowContentState extends State<PayNowContent> {
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _simulateLoading();
  }

  void _simulateLoading() {
    // Show skeleton loading for 5 seconds
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return _buildPayNowContent(context);
  }

  Widget _buildPayNowContent(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    // Responsive sizing
    final horizontalPadding = screenWidth * 0.04; // 4% of screen width
    final verticalPadding = screenHeight * 0.015; // Reduced from 0.02 to 0.015

    return Column(
      children: [
        // Common App Bar
        CommonAppBar(
          titleKey: 'verifyPayment',
          onBackPressed: widget.onBackPressed,
        ),

        // Content
        Expanded(
          child: Container(
            color: Colors.grey.shade100,
            child: SingleChildScrollView(
              padding: EdgeInsets.all(horizontalPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Rider Profile Card
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(screenWidth * 0.04), // Reduced from 0.05 to 0.04
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(screenWidth * 0.04),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withValues(alpha: 0.1),
                          spreadRadius: 1,
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        // Profile Avatar - Exact same as in image
                        Container(
                          width: screenWidth * 0.2, // 20% of screen width
                          height: screenWidth * 0.2,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                Color(0xFFFF6B6B), // Red-pink
                                Color(0xFFFFE66D), // Yellow
                              ],
                            ),
                          ),
                          child: Icon(
                            Icons.admin_panel_settings, // Admin icon to match the image
                            size: screenWidth * 0.1,
                            color: Colors.white,
                          ),
                        ),

                        SizedBox(height: screenHeight * 0.01), // Reduced from 0.015

                        Text(
                          AppStrings.get('riderNameDisplay'),
                          style: AppTextTheme.cardTitle.copyWith(
                            fontSize: AppTextTheme.getResponsiveFontSize(context, 20), // Increased from 18
                            fontWeight: FontWeight.w600,
                            color: AppColors.textPrimary,
                          ),
                        ),

                        SizedBox(height: screenHeight * 0.003), // Reduced from 0.005

                        Text(
                          AppStrings.get('srIdNumber'),
                          style: AppTextTheme.cardSubtitle.copyWith(
                            fontSize: AppTextTheme.getResponsiveFontSize(context, 16), // Increased from 14
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: verticalPadding),

                  // Stats Grid
                  Row(
                    children: [
                      // Delivered Order
                      Expanded(
                        child: Container(
                          padding: EdgeInsets.all(screenWidth * 0.03), // Reduced from 0.04
                          decoration: BoxDecoration(
                            color: AppColors.green.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(screenWidth * 0.03),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                AppStrings.get('deliveredOrder'),
                                style: AppTextTheme.cardSubtitle.copyWith(
                                  fontSize: AppTextTheme.getResponsiveFontSize(context, 14), // Increased from 12
                                  color: AppColors.green,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(height: screenHeight * 0.008), // Reduced from 0.01
                              Row(
                                children: [
                                  Text(
                                    '10',
                                    style: AppTextTheme.cardTitle.copyWith(
                                      fontSize: AppTextTheme.getResponsiveFontSize(context, 26), // Increased from 24
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.green,
                                    ),
                                  ),
                                  Text(
                                    '/10',
                                    style: AppTextTheme.cardSubtitle.copyWith(
                                      fontSize: AppTextTheme.getResponsiveFontSize(context, 16), // Increased from 14
                                      color: AppColors.green,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),

                      SizedBox(width: screenWidth * 0.03),

                      // Pending Order
                      Expanded(
                        child: Container(
                          padding: EdgeInsets.all(screenWidth * 0.03), // Reduced from 0.04
                          decoration: BoxDecoration(
                            color: Colors.red.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(screenWidth * 0.03),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                AppStrings.get('pendingOrder'),
                                style: AppTextTheme.cardSubtitle.copyWith(
                                  fontSize: AppTextTheme.getResponsiveFontSize(context, 14), // Increased from 12
                                  color: Colors.red,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(height: screenHeight * 0.008), // Reduced from 0.01
                              Row(
                                children: [
                                  Text(
                                    '0',
                                    style: AppTextTheme.cardTitle.copyWith(
                                      fontSize: AppTextTheme.getResponsiveFontSize(context, 26), // Increased from 24
                                      fontWeight: FontWeight.bold,
                                      color: Colors.red,
                                    ),
                                  ),
                                  Text(
                                    '/10',
                                    style: AppTextTheme.cardSubtitle.copyWith(
                                      fontSize: AppTextTheme.getResponsiveFontSize(context, 16), // Increased from 14
                                      color: Colors.red,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: verticalPadding),

                  // Second Row Stats
                  Row(
                    children: [
                      // Return Order
                      Expanded(
                        child: Container(
                          padding: EdgeInsets.all(screenWidth * 0.03), // Reduced from 0.04
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(screenWidth * 0.03),
                            border: Border.all(color: Colors.grey.shade200),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                AppStrings.get('returnOrder'),
                                style: AppTextTheme.cardSubtitle.copyWith(
                                  fontSize: AppTextTheme.getResponsiveFontSize(context, 14), // Increased from 12
                                  color: AppColors.textSecondary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(height: screenHeight * 0.008), // Reduced from 0.01
                              Text(
                                '0',
                                style: AppTextTheme.cardTitle.copyWith(
                                  fontSize: AppTextTheme.getResponsiveFontSize(context, 26), // Increased from 24
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      SizedBox(width: screenWidth * 0.03),

                      // Partial Return Order
                      Expanded(
                        child: Container(
                          padding: EdgeInsets.all(screenWidth * 0.03), // Reduced from 0.04
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(screenWidth * 0.03),
                            border: Border.all(color: Colors.grey.shade200),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                AppStrings.get('partialReturnOrder'),
                                style: AppTextTheme.cardSubtitle.copyWith(
                                  fontSize: AppTextTheme.getResponsiveFontSize(context, 14), // Increased from 12
                                  color: AppColors.textSecondary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(height: screenHeight * 0.008), // Reduced from 0.01
                              Text(
                                '0',
                                style: AppTextTheme.cardTitle.copyWith(
                                  fontSize: AppTextTheme.getResponsiveFontSize(context, 26), // Increased from 24
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: verticalPadding),

                  // Third Row Stats
                  Row(
                    children: [
                      // Cash to be collect
                      Expanded(
                        child: Container(
                          padding: EdgeInsets.all(screenWidth * 0.03), // Reduced from 0.04
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(screenWidth * 0.03),
                            border: Border.all(color: Colors.grey.shade200),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                AppStrings.get('cashToBeCollect'),
                                style: AppTextTheme.cardSubtitle.copyWith(
                                  fontSize: AppTextTheme.getResponsiveFontSize(context, 14), // Increased from 12
                                  color: AppColors.textSecondary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(height: screenHeight * 0.008), // Reduced from 0.01
                              Text(
                                '1414',
                                style: AppTextTheme.cardTitle.copyWith(
                                  fontSize: AppTextTheme.getResponsiveFontSize(context, 26), // Increased from 24
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      SizedBox(width: screenWidth * 0.03),

                      // Current trip COD
                      Expanded(
                        child: Container(
                          padding: EdgeInsets.all(screenWidth * 0.03), // Reduced from 0.04
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(screenWidth * 0.03),
                            border: Border.all(color: Colors.grey.shade200),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                AppStrings.get('currentTripCod'),
                                style: AppTextTheme.cardSubtitle.copyWith(
                                  fontSize: AppTextTheme.getResponsiveFontSize(context, 14), // Increased from 12
                                  color: AppColors.textSecondary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(height: screenHeight * 0.008), // Reduced from 0.01
                              Text(
                                '500',
                                style: AppTextTheme.cardTitle.copyWith(
                                  fontSize: AppTextTheme.getResponsiveFontSize(context, 26), // Increased from 24
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: verticalPadding),

                  // Fourth Row Stats
                  Row(
                    children: [
                      // Online amount
                      Expanded(
                        child: Container(
                          padding: EdgeInsets.all(screenWidth * 0.03), // Reduced from 0.04
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(screenWidth * 0.03),
                            border: Border.all(color: Colors.grey.shade200),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                AppStrings.get('onlineAmount'),
                                style: AppTextTheme.cardSubtitle.copyWith(
                                  fontSize: AppTextTheme.getResponsiveFontSize(context, 14), // Increased from 12
                                  color: AppColors.textSecondary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(height: screenHeight * 0.008), // Reduced from 0.01
                              Text(
                                '714',
                                style: AppTextTheme.cardTitle.copyWith(
                                  fontSize: AppTextTheme.getResponsiveFontSize(context, 26), // Increased from 24
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      SizedBox(width: screenWidth * 0.03),

                      // QR amount
                      Expanded(
                        child: Container(
                          padding: EdgeInsets.all(screenWidth * 0.03), // Reduced from 0.04
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(screenWidth * 0.03),
                            border: Border.all(color: Colors.grey.shade200),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                AppStrings.get('qrAmount'),
                                style: AppTextTheme.cardSubtitle.copyWith(
                                  fontSize: AppTextTheme.getResponsiveFontSize(context, 14), // Increased from 12
                                  color: AppColors.textSecondary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(height: screenHeight * 0.008), // Reduced from 0.01
                              Text(
                                '567',
                                style: AppTextTheme.cardTitle.copyWith(
                                  fontSize: AppTextTheme.getResponsiveFontSize(context, 26), // Increased from 24
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      SizedBox(width: screenWidth * 0.03),

                      // Airtel Cash
                      Expanded(
                        child: Container(
                          padding: EdgeInsets.all(screenWidth * 0.03), // Reduced from 0.04
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(screenWidth * 0.03),
                            border: Border.all(color: Colors.grey.shade200),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                AppStrings.get('airtelCash'),
                                style: AppTextTheme.cardSubtitle.copyWith(
                                  fontSize: AppTextTheme.getResponsiveFontSize(context, 14), // Increased from 12
                                  color: AppColors.textSecondary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(height: screenHeight * 0.008), // Reduced from 0.01
                              Text(
                                '345',
                                style: AppTextTheme.cardTitle.copyWith(
                                  fontSize: AppTextTheme.getResponsiveFontSize(context, 26), // Increased from 24
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: verticalPadding),

                  // Bag Stats Row
                  Row(
                    children: [
                      // Saddle Bag
                      Expanded(
                        child: Container(
                          padding: EdgeInsets.all(screenWidth * 0.03), // Reduced from 0.04
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(screenWidth * 0.03),
                            border: Border.all(color: Colors.grey.shade200),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                AppStrings.get('saddleBag'),
                                style: AppTextTheme.cardSubtitle.copyWith(
                                  fontSize: AppTextTheme.getResponsiveFontSize(context, 14), // Increased from 12
                                  color: AppColors.textSecondary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(height: screenHeight * 0.008), // Reduced from 0.01
                              Text(
                                '5',
                                style: AppTextTheme.cardTitle.copyWith(
                                  fontSize: AppTextTheme.getResponsiveFontSize(context, 26), // Increased from 24
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      SizedBox(width: screenWidth * 0.03),

                      // Silver Bag
                      Expanded(
                        child: Container(
                          padding: EdgeInsets.all(screenWidth * 0.03), // Reduced from 0.04
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(screenWidth * 0.03),
                            border: Border.all(color: Colors.grey.shade200),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                AppStrings.get('silverBag'),
                                style: AppTextTheme.cardSubtitle.copyWith(
                                  fontSize: AppTextTheme.getResponsiveFontSize(context, 14), // Increased from 12
                                  color: AppColors.textSecondary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(height: screenHeight * 0.008), // Reduced from 0.01
                              Text(
                                '10',
                                style: AppTextTheme.cardTitle.copyWith(
                                  fontSize: AppTextTheme.getResponsiveFontSize(context, 26), // Increased from 24
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      SizedBox(width: screenWidth * 0.03),

                      // Chill Pad
                      Expanded(
                        child: Container(
                          padding: EdgeInsets.all(screenWidth * 0.03), // Reduced from 0.04
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(screenWidth * 0.03),
                            border: Border.all(color: Colors.grey.shade200),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                AppStrings.get('chillPad'),
                                style: AppTextTheme.cardSubtitle.copyWith(
                                  fontSize: AppTextTheme.getResponsiveFontSize(context, 14), // Increased from 12
                                  color: AppColors.textSecondary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(height: screenHeight * 0.008), // Reduced from 0.01
                              Text(
                                '13',
                                style: AppTextTheme.cardTitle.copyWith(
                                  fontSize: AppTextTheme.getResponsiveFontSize(context, 26), // Increased from 24
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: verticalPadding * 2),

                  // Action Buttons
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            // Get.to(() => const ViewTripInfoScreen());
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.green,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(screenWidth * 0.08),
                            ),
                            padding: EdgeInsets.symmetric(vertical: screenHeight * 0.02),
                          ),
                          child: Text(
                            AppStrings.get('viewTripInfo'),
                            style: AppTextTheme.buttonLarge.copyWith(
                              color: Colors.white,
                              fontSize: AppTextTheme.getResponsiveFontSize(context, 18), // Increased from 16
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: screenWidth * 0.04),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            // Get.to(() => const PaymentTripWiseScreen());
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.green,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(screenWidth * 0.08),
                            ),
                            padding: EdgeInsets.symmetric(vertical: screenHeight * 0.02),
                          ),
                          child: Text(
                            AppStrings.get('paymentTripWise'),
                            style: AppTextTheme.buttonLarge.copyWith(
                              color: Colors.white,
                              fontSize: AppTextTheme.getResponsiveFontSize(context, 18), // Increased from 16
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
