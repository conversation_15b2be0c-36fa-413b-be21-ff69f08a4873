class CommonDataResponse {
  final String status;
  final String msg;
  final CombinedDetails? combinedDetails;

  CommonDataResponse({
    required this.status,
    required this.msg,
    this.combinedDetails,
  });

  factory CommonDataResponse.fromJson(Map<String, dynamic> json) {
    return CommonDataResponse(
      status: json['status'] ?? '',
      msg: json['msg'] ?? '',
      combinedDetails: json['fE_CombinedDetails'] != null
          ? CombinedDetails.fromJson(json['fE_CombinedDetails'])
          : null,
    );
  }
}

class CombinedDetails {
  final List<VehicleType> vehicleTypeList;
  final List<RiderType> riderTypeList;
  final List<BankInfo> bankList;
  final List<SlotName> slotNameList;
  final List<ShiftMaster> shiftMasterList;
  final List<SubShift> subShiftList;
  final List<KfhLocation> kfhList;

  CombinedDetails({
    required this.vehicleTypeList,
    required this.riderTypeList,
    required this.bankList,
    required this.slotNameList,
    required this.shiftMasterList,
    required this.subShiftList,
    required this.kfhList,
  });

  factory CombinedDetails.fromJson(Map<String, dynamic> json) {
    return CombinedDetails(
      vehicleTypeList: (json['vehicleTypeList'] as List<dynamic>?)
              ?.map((x) => VehicleType.fromJson(x))
              .toList() ??
          [],
      riderTypeList: (json['riderTypeList'] as List<dynamic>?)
              ?.map((x) => RiderType.fromJson(x))
              .toList() ??
          [],
      bankList: (json['bankList'] as List<dynamic>?)
              ?.map((x) => BankInfo.fromJson(x))
              .toList() ??
          [],
      slotNameList: (json['slotNameList'] as List<dynamic>?)
              ?.map((x) => SlotName.fromJson(x))
              .toList() ??
          [],
      shiftMasterList: (json['shiftMasterList'] as List<dynamic>?)
              ?.map((x) => ShiftMaster.fromJson(x))
              .toList() ??
          [],
      subShiftList: (json['subShiftList'] as List<dynamic>?)
              ?.map((x) => SubShift.fromJson(x))
              .toList() ??
          [],
      kfhList: (json['kfhList'] as List<dynamic>?)
              ?.map((x) => KfhLocation.fromJson(x))
              .toList() ??
          [],
    );
  }
}

class VehicleType {
  final int id;
  final String vehicleType;
  final String? imageUrl;

  VehicleType({
    required this.id,
    required this.vehicleType,
    this.imageUrl,
  });

  factory VehicleType.fromJson(Map<String, dynamic> json) {
    return VehicleType(
      id: json['id'] ?? 0,
      vehicleType: json['vehicleType'] ?? '',
      imageUrl: json['imageUrl'],
    );
  }
}

class RiderType {
  final int rid;
  final String riderType;

  RiderType({
    required this.rid,
    required this.riderType,
  });

  factory RiderType.fromJson(Map<String, dynamic> json) {
    return RiderType(
      rid: json['rid'] ?? 0,
      riderType: json['riderType'] ?? '',
    );
  }
}

class BankInfo {
  final int id;
  final String bankName;
  final int isActive;

  BankInfo({
    required this.id,
    required this.bankName,
    required this.isActive,
  });

  factory BankInfo.fromJson(Map<String, dynamic> json) {
    return BankInfo(
      id: json['id'] ?? 0,
      bankName: json['bankName'] ?? '',
      isActive: json['isActive'] ?? 0,
    );
  }
}

class SlotName {
  final int id;
  final String slotName;

  SlotName({
    required this.id,
    required this.slotName,
  });

  factory SlotName.fromJson(Map<String, dynamic> json) {
    return SlotName(
      id: json['id'] ?? 0,
      slotName: json['slotName'] ?? '',
    );
  }
}

class ShiftMaster {
  final int id;
  final String shiftName;
  final String startTime;
  final String endTime;
  final int srDeliverySlotID;
  final String noofDaysWeek;
  final String weeklyEarnings;

  ShiftMaster({
    required this.id,
    required this.shiftName,
    required this.startTime,
    required this.endTime,
    required this.srDeliverySlotID,
    required this.noofDaysWeek,
    required this.weeklyEarnings,
  });

  factory ShiftMaster.fromJson(Map<String, dynamic> json) {
    return ShiftMaster(
      id: json['id'] ?? 0,
      shiftName: json['shiftName'] ?? '',
      startTime: json['startTime'] ?? '',
      endTime: json['endTime'] ?? '',
      srDeliverySlotID: json['srDeliverySlotID'] ?? 0,
      noofDaysWeek: json['noofDaysWeek'] ?? '',
      weeklyEarnings: json['weeklyEarnings'] ?? '',
    );
  }
}

class SubShift {
  final int id;
  final int srShiftID;
  final String shiftName;
  final String startTime;
  final String endTime;
  final String weeklyEarnings;

  SubShift({
    required this.id,
    required this.srShiftID,
    required this.shiftName,
    required this.startTime,
    required this.endTime,
    required this.weeklyEarnings,
  });

  factory SubShift.fromJson(Map<String, dynamic> json) {
    return SubShift(
      id: json['id'] ?? 0,
      srShiftID: json['srShiftID'] ?? 0,
      shiftName: json['shiftName'] ?? '',
      startTime: json['startTime'] ?? '',
      endTime: json['endTime'] ?? '',
      weeklyEarnings: json['weeklyEarnings'] ?? '',
    );
  }
}

class KfhLocation {
  final int cdcID;
  final String distributorName;
  final String address;
  final double latitude;
  final double longitude;
  final String mobileNo;
  final int distributorTypeID;
  final String weeklyEarnings;
  final double distance;

  KfhLocation({
    required this.cdcID,
    required this.distributorName,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.mobileNo,
    required this.distributorTypeID,
    required this.weeklyEarnings,
    required this.distance,
  });

  factory KfhLocation.fromJson(Map<String, dynamic> json) {
    return KfhLocation(
      cdcID: json['cdcID'] ?? 0,
      distributorName: json['distributorName'] ?? '',
      address: json['address'] ?? '',
      latitude: (json['latitude'] ?? 0.0).toDouble(),
      longitude: (json['longitude'] ?? 0.0).toDouble(),
      mobileNo: json['mobileNo'] ?? '',
      distributorTypeID: json['distributorTypeID'] ?? 0,
      weeklyEarnings: json['weeklyEarnings'] ?? '',
      distance: (json['distance'] ?? 0.0).toDouble(),
    );
  }
}
