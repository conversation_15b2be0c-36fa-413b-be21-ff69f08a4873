import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

class CommonElevatedButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget child;
  final Color? color;
  final double borderRadius;
  final double width;
  final double height;

  const CommonElevatedButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.color,
    this.borderRadius = 32,
    this.width = 358,
    this.height = 56,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: color ?? AppColors.green,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          foregroundColor: Colors.white,
        ),
        child: child,
      ),
    );
  }
}
