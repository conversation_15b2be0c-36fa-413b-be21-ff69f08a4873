import 'package:flutter/foundation.dart';

/// Performance monitoring utility for tracking operation times
class PerformanceMonitor {
  static final Map<String, Stopwatch> _stopwatches = {};
  static final Map<String, List<int>> _measurements = {};

  /// Start timing an operation
  static void startTimer(String operationName) {
    final stopwatch = Stopwatch()..start();
    _stopwatches[operationName] = stopwatch;
    debugPrint('⏱️ Started timing: $operationName');
  }

  /// Stop timing an operation and record the result
  static int stopTimer(String operationName) {
    final stopwatch = _stopwatches[operationName];
    if (stopwatch == null) {
      debugPrint('❌ No timer found for: $operationName');
      return 0;
    }

    stopwatch.stop();
    final elapsedMs = stopwatch.elapsedMilliseconds;
    
    // Record measurement
    _measurements.putIfAbsent(operationName, () => []).add(elapsedMs);
    
    debugPrint('⏱️ $operationName completed in ${elapsedMs}ms');
    
    // Clean up
    _stopwatches.remove(operationName);
    
    return elapsedMs;
  }

  /// Time a future operation
  static Future<T> timeOperation<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    startTimer(operationName);
    try {
      final result = await operation();
      stopTimer(operationName);
      return result;
    } catch (e) {
      stopTimer(operationName);
      rethrow;
    }
  }

  /// Get performance statistics for an operation
  static Map<String, dynamic> getStats(String operationName) {
    final measurements = _measurements[operationName];
    if (measurements == null || measurements.isEmpty) {
      return {'error': 'No measurements found for $operationName'};
    }

    measurements.sort();
    final count = measurements.length;
    final sum = measurements.reduce((a, b) => a + b);
    final average = sum / count;
    final median = count % 2 == 0
        ? (measurements[count ~/ 2 - 1] + measurements[count ~/ 2]) / 2
        : measurements[count ~/ 2].toDouble();

    return {
      'operation': operationName,
      'count': count,
      'total_ms': sum,
      'average_ms': average.round(),
      'median_ms': median.round(),
      'min_ms': measurements.first,
      'max_ms': measurements.last,
      'measurements': measurements,
    };
  }

  /// Get all performance statistics
  static Map<String, Map<String, dynamic>> getAllStats() {
    final allStats = <String, Map<String, dynamic>>{};
    for (final operationName in _measurements.keys) {
      allStats[operationName] = getStats(operationName);
    }
    return allStats;
  }

  /// Print performance report
  static void printReport([String? operationName]) {
    if (operationName != null) {
      final stats = getStats(operationName);
      debugPrint('📊 Performance Report for $operationName:');
      debugPrint('   Count: ${stats['count']}');
      debugPrint('   Average: ${stats['average_ms']}ms');
      debugPrint('   Median: ${stats['median_ms']}ms');
      debugPrint('   Min: ${stats['min_ms']}ms');
      debugPrint('   Max: ${stats['max_ms']}ms');
    } else {
      debugPrint('📊 Performance Report (All Operations):');
      final allStats = getAllStats();
      for (final entry in allStats.entries) {
        final stats = entry.value;
        debugPrint('   ${entry.key}: avg=${stats['average_ms']}ms, count=${stats['count']}');
      }
    }
  }

  /// Clear all measurements
  static void clearMeasurements([String? operationName]) {
    if (operationName != null) {
      _measurements.remove(operationName);
      debugPrint('🗑️ Cleared measurements for: $operationName');
    } else {
      _measurements.clear();
      debugPrint('🗑️ Cleared all measurements');
    }
  }

  /// Check if logout is performing well (under 2 seconds)
  static bool isLogoutPerformanceGood() {
    final stats = getStats('logout');
    if (stats.containsKey('error')) return true; // No data yet
    
    final averageMs = stats['average_ms'] as int;
    return averageMs < 2000; // Under 2 seconds is good
  }

  /// Get logout performance status
  static String getLogoutPerformanceStatus() {
    final stats = getStats('logout');
    if (stats.containsKey('error')) return 'No data available';
    
    final averageMs = stats['average_ms'] as int;
    final count = stats['count'] as int;
    
    if (averageMs < 1000) {
      return 'Excellent (${averageMs}ms avg, $count samples)';
    } else if (averageMs < 2000) {
      return 'Good (${averageMs}ms avg, $count samples)';
    } else if (averageMs < 5000) {
      return 'Slow (${averageMs}ms avg, $count samples)';
    } else {
      return 'Very Slow (${averageMs}ms avg, $count samples)';
    }
  }
}
