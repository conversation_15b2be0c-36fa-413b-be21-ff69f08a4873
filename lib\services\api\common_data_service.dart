import 'package:flutter/foundation.dart';
import 'package:kisankonnect_rider/services/api/api_helper.dart';
import '../../models/common_data_models.dart';
import '../../constants/api_endpoints.dart';

class CommonDataService {
  static final ApiHelper _apiHelper = ApiHelper.instance;

  /// Fetch common data including vehicle types, rider types, banks, shifts, etc.
  static Future<CommonDataResponse?> fetchCommonData({
    required double latitude,
    required double longitude,
  }) async {
    try {
      debugPrint('🌐 Fetching common data with Lat: $latitude, Lng: $longitude');

      final response = await _apiHelper.get<dynamic>(
        ApiEndpoints.commonData,
        queryParameters: {
          'Lat': latitude,
          'Lng': longitude,
        },
      );

      if (response.isSuccess && response.data != null) {
        final Map<String, dynamic> jsonData = response.data as Map<String, dynamic>;

        debugPrint('📡 Common data response: ${jsonData['status']} - ${jsonData['msg']}');

        if (jsonData['status'] == '200') {
          return CommonDataResponse.fromJson(jsonData);
        } else {
          debugPrint('❌ API returned error: ${jsonData['msg']}');
          return null;
        }
      } else {
        debugPrint('❌ API call failed: ${response.error}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ Exception in fetchCommonData: $e');
      return null;
    }
  }

  /// Get default coordinates (Mumbai) if location is not available
  static Map<String, double> getDefaultCoordinates() {
    return {
      'latitude': 19.0760,
      'longitude': 72.8777,
    };
  }
}
