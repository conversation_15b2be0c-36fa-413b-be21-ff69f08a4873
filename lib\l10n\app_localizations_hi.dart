// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Hindi (`hi`).
class AppLocalizationsHi extends AppLocalizations {
  AppLocalizationsHi([String locale = 'hi']) : super(locale);

  @override
  String get appName => 'किसानकनेक्ट राइडर';

  @override
  String get profile => 'प्रोफ़ाइल';

  @override
  String get riderName => 'राइडर का नाम';

  @override
  String get phoneNumber => '+91- 98388 89898';

  @override
  String get idCard => 'आईडी कार्ड';

  @override
  String get cashBalance => 'नकद शेष';

  @override
  String get myShift => 'मेरी शिफ्ट';

  @override
  String get myKFHLocation => 'मेरा KFH स्थान';

  @override
  String get tripHistory => 'यात्रा इतिहास';

  @override
  String get kisanStore => 'किसान स्टोर';

  @override
  String get newTrends => 'नए ट्रेंड्स';

  @override
  String get referAndEarn => 'रेफर करें और कमाएं';

  @override
  String get referralBonusText => '10,000+ राइडर रेफरल बोनस कमा रहे हैं';

  @override
  String get helpAndSupport => 'सहायता और समर्थन';

  @override
  String get appVersion => 'ऐप संस्करण v0.1.10';

  @override
  String get featuredProducts => 'फीचर्ड उत्पाद';

  @override
  String get bigDiscount => 'बड़ी छूट';

  @override
  String get orderNow => 'अभी ऑर्डर करें';

  @override
  String get cartReview => 'कार्ट समीक्षा';

  @override
  String get cartDetails => 'कार्ट विवरण';

  @override
  String get wallet => 'वॉलेट';

  @override
  String get kisanKonnectWallet => 'किसानकनेक्ट वॉलेट';

  @override
  String get kisanKash => 'किसानकैश';

  @override
  String get balance => 'शेष';

  @override
  String get add => 'जोड़ें';

  @override
  String get redeem => 'रिडीम करें';

  @override
  String get selectPaymentMode => 'अपना भुगतान मोड चुनें';

  @override
  String get cardsUPINetbanking => 'कार्ड/UPI/नेटबैंकिंग';

  @override
  String get payWithUPI => 'UPI से भुगतान करें';

  @override
  String get useAnyUPIApp =>
      'भुगतान के लिए अपने फोन पर कोई भी UPI ऐप का उपयोग करें';

  @override
  String get payNow => 'अभी भुगतान करें';

  @override
  String get orderPlacedSuccessfully => 'आपका ऑर्डर सफलतापूर्वक\nदिया गया';

  @override
  String get continueShopping => 'खरीदारी जारी रखें';

  @override
  String get viewMyOrders => 'मेरे ऑर्डर देखें';

  @override
  String get selectAddress => 'पता चुनें';

  @override
  String get selectYourLocation => 'अपना स्थान चुनें';

  @override
  String get getUpdatedOnWhatsapp => 'व्हाट्सऐप पर अपडेट प्राप्त करें';

  @override
  String get continueButton => 'जारी रखें';

  @override
  String get selectPreferableFilter => 'अपना पसंदीदा फिल्टर चुनें';

  @override
  String get tripAscending => 'आरोही क्रम में यात्रा';

  @override
  String get tripDescending => 'अवरोही क्रम में यात्रा';

  @override
  String get onlySuccessTrip => 'केवल सफल यात्रा';

  @override
  String get onlyFailedTrip => 'केवल असफल यात्रा';

  @override
  String get okay => 'ठीक है';

  @override
  String get myEarnings => 'मेरी कमाई';

  @override
  String get today => 'आज';

  @override
  String get thisWeek => 'इस सप्ताह';

  @override
  String get thisMonth => 'इस महीने';

  @override
  String get totalEarningsOfTheDay => 'दिन की कुल कमाई';

  @override
  String get totalEarningsOfThePreviousDay => 'पिछले दिन की कुल कमाई';

  @override
  String get orderDelivered => 'ऑर्डर डिलीवर किया गया';

  @override
  String get orderEarning => 'ऑर्डर कमाई';

  @override
  String get rainSurgeEarning => 'बारिश सर्ज कमाई';

  @override
  String get totalIncentive => 'कुल प्रोत्साहन';

  @override
  String get incentiveSubtitle => 'शनिवार और रविवार को काम करने पर लागू';

  @override
  String get doMoreOrdersAndEarn => '15 और ऑर्डर करें और कमाएं';

  @override
  String get viewAllEarningsAndIncentives => 'सभी कमाई और प्रोत्साहन देखें';

  @override
  String get orders => 'ऑर्डर';

  @override
  String get referFriendAndEarn => 'दोस्त को रेफर करें और कमाएं';

  @override
  String get yourFriendGets => 'आपके दोस्त को मिलता है';

  @override
  String get onJoining => ' जॉइन करने पर!';

  @override
  String get totalReferralEarnings => 'कुल रेफरल कमाई: ₹5500';

  @override
  String get friendsReferred => '2 दोस्त रेफर किए गए';

  @override
  String get shareYourReferralCode => 'अपना रेफरल कोड शेयर करें';

  @override
  String get howItWorks => 'यह कैसे काम करता है';

  @override
  String get referInSimpleSteps => '3 आसान चरणों में रेफर करें';

  @override
  String get copyCodeOrShareViaWhatsapp =>
      'कोड कॉपी करें या व्हाट्सऐप से शेयर करें';

  @override
  String get completeTheTarget => 'लक्ष्य पूरा करें';

  @override
  String get enjoyTheBonus => 'बोनस का आनंद लें';

  @override
  String get yourReferrals => 'आपके रेफरल';

  @override
  String get pending => 'लंबित';

  @override
  String get success => 'सफल';

  @override
  String get failed => 'असफल';

  @override
  String get inviteViaWhatsApp => 'व्हाट्सऐप से आमंत्रित करें';

  @override
  String get referralCodeCopied => 'रेफरल कोड क्लिपबोर्ड पर कॉपी हो गया!';

  @override
  String joinKisanKonnectMessage(String code) {
    return '🎉 किसानकनेक्ट में शामिल हों और जॉइन करने पर ₹10,000 कमाएं!\n\nमेरा रेफरल कोड उपयोग करें: $code\n\nऐप डाउनलोड करें और आज ही कमाना शुरू करें!';
  }
}
