import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../routes/app_pages.dart';
import '../../../../models/rider_details_model.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import '../../../../constants/storage_keys.dart';
import '../../../../config/flavor_config.dart';

class ProfileSummaryScreen extends StatefulWidget {
  final VoidCallback? onConfirm;

  const ProfileSummaryScreen({
    super.key,
    this.onConfirm,
  });

  @override
  State<ProfileSummaryScreen> createState() => _ProfileSummaryScreenState();
}

class _ProfileSummaryScreenState extends State<ProfileSummaryScreen> {
  bool _isTermsAccepted = false;
  bool _isLoading = true;
  RiderDetail? _riderDetail;
  String _errorMessage = '';

  final SecureStorageService _storage = SecureStorageService.instance;

  @override
  void initState() {
    super.initState();
    _loadRiderDetails();
  }

  Future<void> _loadRiderDetails() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });

      // Get mobile number from storage
      final mobileNumber = await _storage.read(StorageKeys.mobileNumber) ?? '';

      if (mobileNumber.isEmpty) {
        setState(() {
          _errorMessage = 'Mobile number not found';
          _isLoading = false;
        });
        return;
      }

      debugPrint('📱 Loading rider details for: $mobileNumber');

      final response = await ApiService.instance.profile.getRiderDetails(
        mobileNo: mobileNumber,
      );

      if (response.isSuccess && response.data != null) {
        final riderResponse = RiderDetailsResponse.fromJson(response.data);

        if (riderResponse.isSuccess && riderResponse.riderDetail != null) {
          setState(() {
            _riderDetail = riderResponse.riderDetail;
            _isLoading = false;
          });
          debugPrint('✅ Rider details loaded successfully');
        } else {
          setState(() {
            _errorMessage = riderResponse.msg;
            _isLoading = false;
          });
        }
      } else {
        setState(() {
          _errorMessage = response.error ?? 'Failed to load profile details';
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('🚨 Error loading rider details: $e');
      setState(() {
        _errorMessage = 'An error occurred while loading profile';
        _isLoading = false;
      });
    }
  }

  void _confirmAndProceed() {
    debugPrint('✅ Profile summary confirmed, proceeding to next step...');

    // Call the onConfirm callback if provided
    if (widget.onConfirm != null) {
      widget.onConfirm!();
    } else {
      // Navigate to dashboard if no callback provided
      Get.offAllNamed(AppRoutes.dashboard);
    }
  }

  void _openTermsAndConditions() async {
    final url = Environment.termsAndConditionsUrl;
    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
        debugPrint('✅ Terms & Conditions opened successfully');
      } else {
        debugPrint('❌ Cannot launch URL: $url');
        _showFallbackDialog(url);
      }
    } catch (e) {
      debugPrint('🚨 Error opening Terms & Conditions: $e');
      _showFallbackDialog(url);
    }
  }

  void _showFallbackDialog(String url) {
    Get.dialog(
      AlertDialog(
        title: const Text('Terms & Conditions'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Please visit the following URL to review our Terms & Conditions:'),
            const SizedBox(height: 16),
            SelectableText(
              url,
              style: TextStyle(
                color: AppColors.green,
                decoration: TextDecoration.underline,
              ),
            ),
            const SizedBox(height: 16),
            const Text('After reviewing, you can close this dialog and check the agreement checkbox.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: AppColors.green),
            )
          : _errorMessage.isNotEmpty
              ? _buildErrorState()
              : _riderDetail != null
                  ? _buildProfileSummary()
                  : _buildNoDataState(),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            const Text(
              'Error Loading Profile',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.red,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadRiderDetails,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.green,
                foregroundColor: Colors.white,
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoDataState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.person_outline,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            const Text(
              'No Profile Data',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'No profile information found for your account.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileSummary() {
    return Column(
      children: [
        // Content
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Personal details header (outside card)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Personal details',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        color: Colors.black,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: AppColors.greenLight,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.edit, size: 14, color: AppColors.green),
                          const SizedBox(width: 4),
                          Text(
                            'Edit',
                            style: TextStyle(
                              color: AppColors.green,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                _buildPersonalCard(),
                const SizedBox(height: 12),
                // Work details header (outside card)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Work details',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        color: Colors.black,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: AppColors.greenLight,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.edit, size: 14, color: AppColors.green),
                          const SizedBox(width: 4),
                          Text(
                            'Edit',
                            style: TextStyle(
                              color: AppColors.green,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                _buildWorkCard(),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),

        // Bottom section with checkbox and button
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                spreadRadius: 1,
                blurRadius: 5,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: Column(
            children: [
              // Terms & Conditions checkbox
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        _isTermsAccepted = !_isTermsAccepted;
                      });
                    },
                    child: Container(
                      width: 20,
                      height: 20,
                      margin: const EdgeInsets.only(top: 2),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: _isTermsAccepted ? AppColors.green : Colors.grey[400]!,
                          width: _isTermsAccepted ? 2 : 1,
                        ),
                        borderRadius: BorderRadius.circular(4),
                        color: _isTermsAccepted ? AppColors.green : Colors.white,
                      ),
                      child: Icon(
                        Icons.check,
                        size: 14,
                        color: _isTermsAccepted ? Colors.white : Colors.transparent,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: GestureDetector(
                      onTap: _openTermsAndConditions,
                      child: RichText(
                        text: TextSpan(
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[700],
                          ),
                          children: [
                            const TextSpan(text: 'By check confirm, you agree to our '),
                            TextSpan(
                              text: 'Terms & Conditions',
                              style: TextStyle(
                                color: AppColors.green,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Confirm button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: !_isTermsAccepted ? null : _confirmAndProceed,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _isTermsAccepted ? AppColors.green : Colors.grey[400],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                  child: Text(
                    _isTermsAccepted ? 'Confirm' : 'Please accept Terms & Conditions',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPersonalCard() {
    if (_riderDetail == null) return const SizedBox.shrink();

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile section
            Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    image: _riderDetail!.selfImg != null
                        ? DecorationImage(
                            image: NetworkImage(_riderDetail!.selfImg!),
                            fit: BoxFit.cover,
                          )
                        : const DecorationImage(
                            image: AssetImage('assets/images/rider_image.png'),
                            fit: BoxFit.cover,
                          ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Rider name',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        _riderDetail!.mobileNo,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.greenLight,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _riderDetail!.genderText,
                    style: TextStyle(
                      color: AppColors.green,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Contact info with borders
            Wrap(
              spacing: 6,
              runSpacing: 6,
              children: [
                _buildBorderedChip(_riderDetail!.emailID),
                _buildBorderedChip('Status - ${_riderDetail!.maritalStatusText}'),
                _buildBorderedChip(_riderDetail!.formattedDob, icon: Icons.calendar_today),
              ],
            ),
            const SizedBox(height: 12),

            // Bank and document details
            _buildDetailRow('Bank name', _riderDetail!.bankName, isRed: _riderDetail!.bankName == '1'),
            _buildDetailRow('Account number', _riderDetail!.accountNumber, isRed: _riderDetail!.accountNumber == '1'),
            _buildDetailRow('IFSC code', _riderDetail!.ifscCode, isRed: _riderDetail!.ifscCode == '1'),
            _buildDetailRow('Aadhar card', '**************'),
            _buildDetailRow('Pan card', _riderDetail!.panNo),
            _buildDetailRow('Driving license', _riderDetail!.dlNo),
          ],
        ),
      ),
    );
  }

  Widget _buildWorkCard() {
    if (_riderDetail == null) return const SizedBox.shrink();

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Vehicle section
            Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.motorcycle,
                    color: Colors.grey,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _riderDetail!.vehicleTypeText,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                        ),
                      ),
                      Text(
                        'MH67-9898-6837-48',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Depot names with borders
            Wrap(
              spacing: 6,
              runSpacing: 6,
              children: [
                _buildBorderedChip('Nerul (Depot name)'),
                _buildBorderedChip('Vashi (Depot name)'),
              ],
            ),
            const SizedBox(height: 12),

            // Work details
            _buildDetailRow('Delivery', 'Slot wise delivery'),
            _buildDetailRow('Shift', _riderDetail!.shiftID),
            _buildDetailRow('Shift time', '7 am - 3 pm'),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {bool isRed = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2.0),
      child: Row(
        children: [
          Text(
            '$label\t\t\t\t- ',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 12,
                color: isRed ? Colors.red : Colors.black,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBorderedChip(String text, {IconData? icon}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(6),
        color: Colors.white,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            Icon(icon, size: 12, color: Colors.grey[600]),
            const SizedBox(width: 4),
          ],
          Text(
            text,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }
}
