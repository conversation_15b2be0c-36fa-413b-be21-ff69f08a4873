import 'package:flutter/material.dart';
import 'package:get/get.dart';

class LocalizationService extends GetxController {
  static LocalizationService get to => Get.find();

  // Current locale
  Locale _locale = const Locale('en', 'US');
  Locale get locale => _locale;

  // Supported locales
  static const supportedLocales = [
    Locale('en', 'US'), // English
    Locale('hi', 'IN'), // Hindi
    Locale('mr', 'IN'), // Marathi
  ];

  // Fallback locale
  static const fallbackLocale = Locale('en', 'US');

  @override
  void onInit() {
    super.onInit();
    _loadSavedLocale();
  }

  void _loadSavedLocale() {
    // Load saved locale from storage (you can implement SharedPreferences here)
    // For now, we'll use English as default
    _locale = const Locale('en', 'US');
  }

  void changeLocale(Locale newLocale) {
    if (supportedLocales.contains(newLocale)) {
      _locale = newLocale;
      Get.updateLocale(newLocale);
      update(); // Notify GetBuilder widgets to rebuild
      // Save to storage (implement SharedPreferences here)
    }
  }

  void toggleLanguage() {
    if (_locale.languageCode == 'en') {
      changeLocale(const Locale('hi', 'IN'));
    } else if (_locale.languageCode == 'hi') {
      changeLocale(const Locale('mr', 'IN'));
    } else {
      changeLocale(const Locale('en', 'US'));
    }
  }

  void setLanguage(String languageCode) {
    switch (languageCode) {
      case 'en':
        changeLocale(const Locale('en', 'US'));
        break;
      case 'hi':
        changeLocale(const Locale('hi', 'IN'));
        break;
      case 'mr':
        changeLocale(const Locale('mr', 'IN'));
        break;
      default:
        changeLocale(const Locale('en', 'US'));
    }
  }

  bool get isHindi => _locale.languageCode == 'hi';
  bool get isEnglish => _locale.languageCode == 'en';
  bool get isMarathi => _locale.languageCode == 'mr';

  String get currentLanguageName {
    switch (_locale.languageCode) {
      case 'hi':
        return 'हिंदी';
      case 'mr':
        return 'मराठी';
      case 'en':
      default:
        return 'English';
    }
  }
}
