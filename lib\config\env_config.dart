import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'flavor_config.dart';

/// Environment configuration service using .env files
/// Loads environment-specific configuration from .env files
class EnvConfig {
  static bool _initialized = false;

  /// Initialize environment configuration
  /// Must be called before accessing any environment variables
  static Future<void> init({required Flavor flavor}) async {
    if (_initialized) return;

    try {
      // Load environment-specific .env file
      String envFile = _getEnvFileName(flavor);
      await dotenv.load(fileName: envFile);

      _initialized = true;

      if (kDebugMode) {
        debugPrint('🌍 Environment loaded: $envFile');
        debugPrint('📱 App Name: $appName');
        debugPrint('🌐 API Base URL: $apiBaseUrl');
        debugPrint('🔧 Log Level: $logLevel');
      }
    } catch (e) {
      debugPrint('❌ Error loading environment: $e');
      // Fallback to default .env if environment-specific file fails
      try {
        await dotenv.load(fileName: '.env');
        _initialized = true;
        debugPrint('✅ Fallback to default .env loaded');
      } catch (fallbackError) {
        debugPrint('❌ Error loading fallback .env: $fallbackError');
        throw Exception('Failed to load environment configuration');
      }
    }
  }

  /// Get environment file name based on flavor
  static String _getEnvFileName(Flavor flavor) {
    switch (flavor) {
      case Flavor.development:
        return '.env.development';
      case Flavor.production:
        return '.env.production';
    }
  }

  /// Check if environment is initialized
  static bool get isInitialized => _initialized;

  // =============================================================================
  // APP CONFIGURATION
  // =============================================================================

  static String get appName => dotenv.env['APP_NAME'] ?? 'KisanKonnect Rider';
  static String get appVersion => dotenv.env['APP_VERSION'] ?? '1.0.0';
  static String get bundleId => dotenv.env['BUNDLE_ID'] ?? 'com.kisankonnect.rider';

  // =============================================================================
  // API CONFIGURATION
  // =============================================================================

  static String get apiBaseUrl => dotenv.env['API_BASE_URL'] ?? 'http://knet.kisankonnect.com/SRIT3O/api';
  static String get apiVersion => dotenv.env['API_VERSION'] ?? '';
  static String get socketUrl => dotenv.env['SOCKET_URL'] ?? 'wss://socket.kisankonnect.com';
  static int get requestTimeout => int.tryParse(dotenv.env['REQUEST_TIMEOUT'] ?? '30') ?? 30;
  static int get maxRetries => int.tryParse(dotenv.env['MAX_RETRIES'] ?? '3') ?? 3;

  // =============================================================================
  // FEATURE FLAGS
  // =============================================================================

  static bool get enableLogging => _getBool('ENABLE_LOGGING', true);
  static bool get enableCrashlytics => _getBool('ENABLE_CRASHLYTICS', false);
  static bool get enableAnalytics => _getBool('ENABLE_ANALYTICS', false);
  static bool get enablePerformanceMonitoring => _getBool('ENABLE_PERFORMANCE_MONITORING', false);
  static bool get enableDevicePreview => _getBool('ENABLE_DEVICE_PREVIEW', false);
  static bool get enableFlavorBanner => _getBool('ENABLE_FLAVOR_BANNER', true);

  // =============================================================================
  // CACHE & STORAGE CONFIGURATION
  // =============================================================================

  static int get cacheTimeoutMinutes => int.tryParse(dotenv.env['CACHE_TIMEOUT_MINUTES'] ?? '15') ?? 15;
  static Duration get cacheTimeout => Duration(minutes: cacheTimeoutMinutes);
  static String get databaseName => dotenv.env['DATABASE_NAME'] ?? 'kisankonnect_rider.db';
  static String get logLevel => dotenv.env['LOG_LEVEL'] ?? 'info';

  // =============================================================================
  // EXTERNAL SERVICES
  // =============================================================================

  static String get ifscApiUrl => dotenv.env['IFSC_API_URL'] ?? 'https://ifsc.razorpay.com';
  static String get twilioApiUrl => dotenv.env['TWILIO_API_URL'] ?? 'https://api.twilio.com/2010-04-01';
  static String get messageBirdApiUrl =>
      dotenv.env['MESSAGEBIRD_API_URL'] ?? 'https://conversations.messagebird.com/v1';
  static String get gupshupApiUrl => dotenv.env['GUPSHUP_API_URL'] ?? 'https://api.gupshup.io/sm/api/v1';

  // =============================================================================
  // DOCUMENT URLS
  // =============================================================================

  static String get documentsBaseUrl => dotenv.env['DOCUMENTS_BASE_URL'] ?? 'http://knet.kisankonnect.com';
  static String get termsConditionsPath =>
      dotenv.env['TERMS_CONDITIONS_PATH'] ?? '/KisanKonnect/RiderDocument/Terms&Condition.html';
  static String get privacyPolicyPath =>
      dotenv.env['PRIVACY_POLICY_PATH'] ?? '/KisanKonnect/RiderDocument/PrivacyPolicy.html';

  static String get termsConditionsUrl => '$documentsBaseUrl$termsConditionsPath';
  static String get privacyPolicyUrl => '$documentsBaseUrl$privacyPolicyPath';

  // =============================================================================
  // SECURITY CONFIGURATION
  // =============================================================================

  static bool get enableSslPinning => _getBool('ENABLE_SSL_PINNING', false);
  static bool get enableCertificateValidation => _getBool('ENABLE_CERTIFICATE_VALIDATION', true);
  static bool get enableNetworkSecurity => _getBool('ENABLE_NETWORK_SECURITY', true);

  // =============================================================================
  // DEVELOPMENT TOOLS
  // =============================================================================

  static bool get enableDebugMenu => _getBool('ENABLE_DEBUG_MENU', false);
  static bool get enableNetworkInspector => _getBool('ENABLE_NETWORK_INSPECTOR', false);
  static bool get enablePerformanceOverlay => _getBool('ENABLE_PERFORMANCE_OVERLAY', false);

  // =============================================================================
  // THIRD PARTY INTEGRATIONS
  // =============================================================================

  static String get fcmSenderId => dotenv.env['FCM_SENDER_ID'] ?? '';
  static String get fcmProjectId => dotenv.env['FCM_PROJECT_ID'] ?? '';
  static String get googleMapsApiKey => dotenv.env['GOOGLE_MAPS_API_KEY'] ?? '';
  static String get razorpayKeyId => dotenv.env['RAZORPAY_KEY_ID'] ?? '';

  // =============================================================================
  // COMPUTED VALUES
  // =============================================================================

  /// Get full API base URL with version
  static String get fullApiBaseUrl => apiVersion.isEmpty ? apiBaseUrl : '$apiBaseUrl/$apiVersion';

  /// Get rider-specific API base URL
  static String get riderBaseUrl => '$fullApiBaseUrl/Rider';

  /// Get request timeout as Duration
  static Duration get requestTimeoutDuration => Duration(seconds: requestTimeout);

  // =============================================================================
  // HELPER METHODS
  // =============================================================================

  /// Parse boolean from environment variable
  static bool _getBool(String key, bool defaultValue) {
    final value = dotenv.env[key]?.toLowerCase();
    if (value == null) return defaultValue;
    return value == 'true' || value == '1' || value == 'yes';
  }

  /// Get all environment variables for debugging
  static Map<String, String> get allEnvVars => Map.from(dotenv.env);

  /// Get environment summary for debugging
  static Map<String, dynamic> get environmentSummary => {
        'appName': appName,
        'appVersion': appVersion,
        'bundleId': bundleId,
        'apiBaseUrl': apiBaseUrl,
        'enableLogging': enableLogging,
        'enableCrashlytics': enableCrashlytics,
        'enableAnalytics': enableAnalytics,
        'logLevel': logLevel,
        'cacheTimeout': cacheTimeoutMinutes,
        'requestTimeout': requestTimeout,
        'maxRetries': maxRetries,
      };

  /// Validate required environment variables
  static bool validateEnvironment() {
    final required = ['APP_NAME', 'API_BASE_URL'];
    for (final key in required) {
      if (dotenv.env[key] == null || dotenv.env[key]!.isEmpty) {
        debugPrint('❌ Missing required environment variable: $key');
        return false;
      }
    }
    return true;
  }
}
