import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

import '../controllers/auth_controller.dart';
import '../routes/app_pages.dart';

/// Simple Registration Controller
class ProfileController extends GetxController {
  final ApiService _apiService = ApiService.instance;
  final AuthController _authController = Get.find<AuthController>();
  final SecureStorageService _storage = SecureStorageService.instance;

  // Storage keys
  static const String _profileDataKey = 'profile_data';
  static const String _workDataKey = 'work_data';
  static const String _imagePathsKey = 'image_paths';
  static const String _currentStepKey = 'current_step';

  // Loading state
  final RxBool _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  // Registration data
  final Map<String, dynamic> _profileData = {};
  final Map<String, dynamic> _workData = {};
  final Map<String, String> _imagePaths = {};

  // Getters
  Map<String, dynamic> get profileData => _profileData;
  Map<String, dynamic> get workData => _workData;
  Map<String, String> get imagePaths => _imagePaths;

  @override
  void onInit() {
    super.onInit();
    _loadLocalData();
  }

  /// Load local data from storage
  Future<void> _loadLocalData() async {
    try {
      final mobileNumber = this.mobileNumber;
      if (mobileNumber.isEmpty) return;

      // Load profile data
      final storedProfileData = await _storage.readMap('${_profileDataKey}_$mobileNumber');
      if (storedProfileData != null) {
        _profileData.addAll(storedProfileData);
        debugPrint('📝 Loaded profile data: ${_profileData.keys}');
      }

      // Load work data
      final storedWorkData = await _storage.readMap('${_workDataKey}_$mobileNumber');
      if (storedWorkData != null) {
        _workData.addAll(storedWorkData);
        debugPrint('💼 Loaded work data: ${_workData.keys}');
      }

      // Load image paths
      final storedImagePaths = await _storage.readMap('${_imagePathsKey}_$mobileNumber');
      if (storedImagePaths != null) {
        _imagePaths.addAll(Map<String, String>.from(storedImagePaths));
        debugPrint('📷 Loaded image paths: ${_imagePaths.keys}');
      }
    } catch (e) {
      debugPrint('🚨 Error loading local data: $e');
    }
  }

  /// Save data to local storage
  Future<void> _saveData() async {
    try {
      final mobileNumber = this.mobileNumber;
      if (mobileNumber.isEmpty) return;

      await _storage.writeMap('${_profileDataKey}_$mobileNumber', _profileData);
      await _storage.writeMap('${_workDataKey}_$mobileNumber', _workData);
      await _storage.writeMap('${_imagePathsKey}_$mobileNumber', Map<String, dynamic>.from(_imagePaths));
      debugPrint('💾 Data saved for: $mobileNumber');
    } catch (e) {
      debugPrint('🚨 Error saving data: $e');
    }
  }

  /// Get mobile number from auth controller
  String get mobileNumber {
    return _authController.currentUser?.mobileNumber ?? '';
  }

  /// Update profile data
  void updateProfileData(String key, dynamic value) {
    _profileData[key] = value;
    _saveData(); // Auto-save on every update (fire and forget)
    debugPrint('📝 Profile data updated: $key = $value');
  }

  /// Update work data
  void updateWorkData(String key, dynamic value) {
    _workData[key] = value;
    _saveData(); // Auto-save on every update (fire and forget)
    debugPrint('💼 Work data updated: $key = $value');
  }

  /// Update image path
  void updateImagePath(String key, String path) {
    _imagePaths[key] = path;
    _saveData(); // Auto-save on every update (fire and forget)
    debugPrint('📷 Image path updated: $key = $path');
  }

  /// Save current step
  void saveCurrentStep(int step) {
    try {
      final mobileNumber = this.mobileNumber;
      if (mobileNumber.isEmpty) return;

      _storage.writeInt('${_currentStepKey}_$mobileNumber', step);
      debugPrint('📍 Step saved: $step');
    } catch (e) {
      debugPrint('🚨 Error saving step: $e');
    }
  }

  /// Get saved current step
  Future<int> getSavedCurrentStep() async {
    try {
      final mobileNumber = this.mobileNumber;
      if (mobileNumber.isEmpty) return 0;

      final savedStep = await _storage.readInt('${_currentStepKey}_$mobileNumber');
      return savedStep ?? 0;
    } catch (e) {
      debugPrint('🚨 Error getting saved step: $e');
      return 0;
    }
  }

  /// Submit profile registration
  Future<bool> submitProfileRegistration() async {
    try {
      _isLoading.value = true;

      // Submit to API
      final response = await _apiService.profile.registerProfile(
        request: {
          'firstName': _profileData['firstName'] ?? '',
          'lastName': _profileData['lastName'] ?? '',
          'mobileNo': mobileNumber,
          'emailID': _profileData['email'] ?? '',
          'dob': _profileData['dob'] ?? '',
          'genderType': _profileData['genderType'] ?? 1,
          'maritalStatus': _profileData['maritalStatus'] ?? 1,
          'spouseName': _profileData['spouseName'],
          'sDob': _profileData['spouseDob'],
          'childName': _profileData['childName'],
          'cDob': _profileData['childDob'],
          'societyName': _profileData['societyName'] ?? '',
          'roomFlatNo': _profileData['roomFlatNo'] ?? '',
          'fullAddress': _profileData['fullAddress'] ?? '',
          'landmark': _profileData['landmark'] ?? '',
          'bankName': _profileData['bankName'] ?? '',
          'accountNumber': _profileData['accountNumber'] ?? '',
          'ifscCode': _profileData['ifscCode'] ?? '',
        },
        passbookImagePath: _imagePaths['passbook'],
      );

      if (response.isSuccess) {
        debugPrint('✅ Profile registered successfully');

        Get.snackbar(
          'Success',
          'Profile registered successfully!',
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        return true;
      } else {
        Get.snackbar(
          'Error',
          response.error ?? 'Failed to register profile',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }
    } catch (e) {
      debugPrint('🚨 Profile registration error: $e');
      Get.snackbar(
        'Error',
        'Registration failed. Please try again.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Submit document upload
  Future<bool> submitDocumentUpload() async {
    try {
      _isLoading.value = true;

      final response = await _apiService.profile.uploadDocuments(
        mobileNo: mobileNumber,
        panCardPhotoPath: _imagePaths['panCard'],
        dlPhotoPath: _imagePaths['drivingLicense'],
        selfImagePath: _imagePaths['selfie'],
      );

      if (response.isSuccess) {
        debugPrint('✅ Documents uploaded successfully');

        Get.snackbar(
          'Success',
          'Documents uploaded successfully!',
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        return true;
      } else {
        Get.snackbar(
          'Error',
          response.error ?? 'Failed to upload documents',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }
    } catch (e) {
      debugPrint('🚨 Document upload error: $e');
      Get.snackbar(
        'Error',
        'Upload failed. Please try again.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Submit work details
  Future<bool> submitWorkDetails() async {
    try {
      _isLoading.value = true;

      final response = await _apiService.profile.registerWorkDetails(request: {
        'mobileNo': mobileNumber,
        'referBy': _workData['referBy'],
        'shiftID': _workData['shiftID'] ?? 0,
        'vehicleType': _workData['vehicleType'] ?? 0,
        'slotName': _workData['slotName'] ?? 0,
        'subSlotName': _workData['subSlotName'],
        'weekoffDays': _workData['weekoffDays'],
        'dcid': _workData['dcid'],
      });

      if (response.isSuccess) {
        debugPrint('✅ Work details registered successfully');

        Get.snackbar(
          'Success',
          'Work details registered successfully!',
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        return true;
      } else {
        Get.snackbar(
          'Error',
          response.error ?? 'Failed to register work details',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }
    } catch (e) {
      debugPrint('🚨 Work details registration error: $e');
      Get.snackbar(
        'Error',
        'Registration failed. Please try again.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Complete registration
  Future<void> completeRegistration() async {
    try {
      _isLoading.value = true;

      Get.snackbar(
        'Success',
        'Registration completed successfully!',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );

      // Clear data and navigate
      clearData();
      Get.offAllNamed(AppRoutes.dashboard);
    } catch (e) {
      debugPrint('🚨 Registration completion error: $e');
      Get.offAllNamed(AppRoutes.dashboard);
    } finally {
      _isLoading.value = false;
    }
  }

  /// Submit data based on current step
  Future<bool> submitStepData(int currentStep) async {
    debugPrint('📤 Submitting data for step: $currentStep');

    switch (currentStep) {
      case 5:
        // After bank details step - submit profile registration
        return await submitProfileRegistration();
      case 6:
        // After document upload step - submit documents
        return await submitDocumentUpload();
      case 7:
        // After work details step - submit work details
        return await submitWorkDetails();
      case 8:
        // Summary step - complete profile creation
        await completeRegistration();
        return true;
      default:
        // No API call needed for this step
        debugPrint('📝 No API submission required for step $currentStep');
        return true;
    }
  }

  /// Check if step requires API submission
  bool stepRequiresApiSubmission(int step) {
    return [5, 6, 7, 8].contains(step);
  }

  /// Get next step after successful API submission
  int getNextStepAfterApiSubmission(int currentStep) {
    switch (currentStep) {
      case 5:
        return 6; // Profile registered → Go to documents
      case 6:
        return 7; // Documents uploaded → Go to work details
      case 7:
        return 8; // Work details submitted → Go to summary
      case 8:
        return -1; // Summary completed → Navigate to dashboard
      default:
        return currentStep + 1;
    }
  }

  /// Get current registration status description
  String getCurrentStatusDescription() {
    final currentUser = _authController.currentUser;
    if (currentUser != null) {
      return currentUser.regStatusDescription;
    }
    return 'Profile not created';
  }

  /// Get prefilled value for a field
  String getPrefilledValue(String key) {
    final value = _profileData[key] ?? _workData[key];
    return value?.toString() ?? '';
  }

  /// Get prefilled image path
  String getPrefilledImagePath(String key) {
    return _imagePaths[key] ?? '';
  }

  /// Check if field has saved data
  bool hasPrefilledData(String key) {
    return _profileData.containsKey(key) || _workData.containsKey(key);
  }

  /// Get all prefilled data for debugging
  Map<String, dynamic> getAllPrefilledData() {
    return {
      'profileData': _profileData,
      'workData': _workData,
      'imagePaths': _imagePaths,
    };
  }

  /// Clear data for specific user (called after successful profile completion)
  Future<void> clearUserData() async {
    try {
      final mobileNumber = this.mobileNumber;
      if (mobileNumber.isEmpty) return;

      // Clear from memory
      _profileData.clear();
      _workData.clear();
      _imagePaths.clear();

      // Clear from storage
      await _storage.delete('${_profileDataKey}_$mobileNumber');
      await _storage.delete('${_workDataKey}_$mobileNumber');
      await _storage.delete('${_imagePathsKey}_$mobileNumber');
      await _storage.delete('${_currentStepKey}_$mobileNumber');

      debugPrint('🗑️ Profile data cleared for user: $mobileNumber');
    } catch (e) {
      debugPrint('🚨 Error clearing user data: $e');
    }
  }

  /// Clear all data (for logout or reset)
  void clearData() {
    _profileData.clear();
    _workData.clear();
    _imagePaths.clear();
    debugPrint('🗑️ Profile controller data cleared');
  }
}
