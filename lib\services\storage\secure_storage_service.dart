import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:kisankonnect_rider/services/all_services.dart';

/// Common Rider Storage Service using SharedPreferences
/// Centralized storage service for all rider data, preferences, and app state
/// Provides a single point of access for all storage operations
class SecureStorageService {
  static SecureStorageService? _instance;
  static SecureStorageService get instance => _instance ??= SecureStorageService._();

  SharedPreferences? _prefs;

  SecureStorageService._() {
    _initializeStorage();
  }

  Future<void> _initializeStorage() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      debugPrint('📱 Common Rider Storage initialized successfully');
    } catch (e) {
      debugPrint('🚨 Common Rider Storage initialization error: $e');
    }
  }

  /// Ensure SharedPreferences is initialized
  Future<SharedPreferences> _getPrefs() async {
    if (_prefs == null) {
      await _initializeStorage();
    }
    return _prefs!;
  }

  /// Write a string value to storage
  Future<void> write(String key, String value) async {
    try {
      final prefs = await _getPrefs();
      await prefs.setString(key, value);
      debugPrint('� SharedPreferences write: $key');
    } catch (e) {
      debugPrint('🚨 SharedPreferences write error: $e');
      rethrow;
    }
  }

  /// Read a string value from storage
  Future<String?> read(String key) async {
    try {
      final prefs = await _getPrefs();
      final value = prefs.getString(key);
      debugPrint('� SharedPreferences read: $key ${value != null ? '(found)' : '(not found)'}');
      return value;
    } catch (e) {
      debugPrint('🚨 SharedPreferences read error: $e');
      return null;
    }
  }

  /// Write a Map/Object to storage as JSON
  Future<void> writeMap(String key, Map<String, dynamic> value) async {
    try {
      final jsonString = json.encode(value);
      await write(key, jsonString);
    } catch (e) {
      debugPrint('🚨 SharedPreferences writeMap error: $e');
      rethrow;
    }
  }

  /// Read a Map/Object from storage
  Future<Map<String, dynamic>?> readMap(String key) async {
    try {
      final jsonString = await read(key);
      if (jsonString == null) return null;

      final decoded = json.decode(jsonString);
      if (decoded is Map<String, dynamic>) {
        return decoded;
      }
      return null;
    } catch (e) {
      debugPrint('🚨 SharedPreferences readMap error: $e');
      return null;
    }
  }

  /// Write an integer value to storage
  Future<void> writeInt(String key, int value) async {
    try {
      final prefs = await _getPrefs();
      await prefs.setInt(key, value);
      debugPrint('📱 SharedPreferences writeInt: $key = $value');
    } catch (e) {
      debugPrint('🚨 SharedPreferences writeInt error: $e');
      rethrow;
    }
  }

  /// Read an integer value from storage
  Future<int?> readInt(String key) async {
    try {
      final prefs = await _getPrefs();
      if (!prefs.containsKey(key)) return null;
      return prefs.getInt(key);
    } catch (e) {
      debugPrint('🚨 SharedPreferences readInt error: $e');
      return null;
    }
  }

  /// Write a boolean value to storage
  Future<void> writeBool(String key, bool value) async {
    try {
      final prefs = await _getPrefs();
      await prefs.setBool(key, value);
      debugPrint('📱 SharedPreferences writeBool: $key = $value');
    } catch (e) {
      debugPrint('🚨 SharedPreferences writeBool error: $e');
      rethrow;
    }
  }

  /// Read a boolean value from storage
  Future<bool?> readBool(String key) async {
    try {
      final prefs = await _getPrefs();
      if (!prefs.containsKey(key)) return null;
      return prefs.getBool(key);
    } catch (e) {
      debugPrint('🚨 SharedPreferences readBool error: $e');
      return null;
    }
  }

  /// Delete a specific key from storage
  Future<void> delete(String key) async {
    try {
      final prefs = await _getPrefs();
      await prefs.remove(key);
      debugPrint('� SharedPreferences delete: $key');
    } catch (e) {
      debugPrint('🚨 SharedPreferences delete error: $e');
    }
  }

  /// Delete all data from storage
  Future<void> deleteAll() async {
    try {
      final prefs = await _getPrefs();
      await prefs.clear();
      debugPrint('� SharedPreferences: All data cleared');
    } catch (e) {
      debugPrint('🚨 SharedPreferences deleteAll error: $e');
    }
  }

  /// Check if a key exists in storage
  Future<bool> containsKey(String key) async {
    try {
      final prefs = await _getPrefs();
      return prefs.containsKey(key);
    } catch (e) {
      debugPrint('🚨 SharedPreferences containsKey error: $e');
      return false;
    }
  }

  /// Get all keys from storage
  Future<Set<String>> getAllKeys() async {
    try {
      final prefs = await _getPrefs();
      return prefs.getKeys();
    } catch (e) {
      debugPrint('🚨 SharedPreferences getAllKeys error: $e');
      return <String>{};
    }
  }

  /// Clear user-specific data (for logout)
  Future<void> clearUserData(String mobileNumber) async {
    try {
      final allKeys = await getAllKeys();
      final userKeys = allKeys.where((key) => key.contains(mobileNumber));

      for (final key in userKeys) {
        await delete(key);
      }

      debugPrint('🔐 Cleared user data for: $mobileNumber');
    } catch (e) {
      debugPrint('🚨 Error clearing user data: $e');
    }
  }

  /// Batch delete multiple keys for better performance
  Future<void> batchDelete(List<String> keys) async {
    try {
      debugPrint('🔐 Batch deleting ${keys.length} keys...');

      // Use Future.wait for parallel deletion
      await Future.wait(
        keys.map((key) => delete(key)),
        eagerError: false, // Continue even if some deletions fail
      );

      debugPrint('🔐 Batch delete completed');
    } catch (e) {
      debugPrint('🚨 Batch delete error: $e');
    }
  }

  /// Get storage info for debugging
  Map<String, dynamic> getStorageInfo() {
    return {
      'platform': defaultTargetPlatform.name,
      'storageType': 'SharedPreferences',
      'encrypted': false,
      'version': '2.3.3',
    };
  }

  // =============================================================================
  // RIDER-SPECIFIC STORAGE METHODS
  // =============================================================================

  /// Save rider authentication data
  Future<void> saveRiderAuth({
    required String mobileNumber,
    required String riderId,
    required String riderName,
    String? authToken,
  }) async {
    try {
      await write(StorageKeys.mobileNumber, mobileNumber);
      await write(StorageKeys.riderId, riderId);
      await write(StorageKeys.riderName, riderName);
      if (authToken != null) {
        await write(StorageKeys.authToken, authToken);
      }
      debugPrint('✅ Rider auth data saved: $mobileNumber');
    } catch (e) {
      debugPrint('❌ Error saving rider auth data: $e');
    }
  }

  /// Get rider authentication data
  Future<Map<String, String?>> getRiderAuth() async {
    try {
      return {
        'mobileNumber': await read(StorageKeys.mobileNumber),
        'riderId': await read(StorageKeys.riderId),
        'riderName': await read(StorageKeys.riderName),
        'authToken': await read(StorageKeys.authToken),
      };
    } catch (e) {
      debugPrint('❌ Error getting rider auth data: $e');
      return {};
    }
  }

  /// Save rider profile data
  Future<void> saveRiderProfile(Map<String, dynamic> profileData) async {
    try {
      await writeMap(StorageKeys.riderProfile, profileData);
      debugPrint('✅ Rider profile saved');
    } catch (e) {
      debugPrint('❌ Error saving rider profile: $e');
    }
  }

  /// Get rider profile data
  Future<Map<String, dynamic>?> getRiderProfile() async {
    try {
      return await readMap(StorageKeys.riderProfile);
    } catch (e) {
      debugPrint('❌ Error getting rider profile: $e');
      return null;
    }
  }

  /// Save rider location data
  Future<void> saveRiderLocation({
    required double latitude,
    required double longitude,
    String? address,
  }) async {
    try {
      await write(StorageKeys.latitude, latitude.toString());
      await write(StorageKeys.longitude, longitude.toString());
      if (address != null) {
        await write(StorageKeys.currentAddress, address);
      }
      debugPrint('✅ Rider location saved: $latitude, $longitude');
    } catch (e) {
      debugPrint('❌ Error saving rider location: $e');
    }
  }

  /// Get rider location data
  Future<Map<String, dynamic>?> getRiderLocation() async {
    try {
      final lat = await read(StorageKeys.latitude);
      final lng = await read(StorageKeys.longitude);
      final address = await read(StorageKeys.currentAddress);

      if (lat != null && lng != null) {
        return {
          'latitude': double.tryParse(lat),
          'longitude': double.tryParse(lng),
          'address': address,
        };
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error getting rider location: $e');
      return null;
    }
  }

  /// Save rider status (online/offline)
  Future<void> saveRiderStatus(bool isOnline) async {
    try {
      await writeBool(StorageKeys.riderOnlineStatus, isOnline);
      debugPrint('✅ Rider status saved: ${isOnline ? 'Online' : 'Offline'}');
    } catch (e) {
      debugPrint('❌ Error saving rider status: $e');
    }
  }

  /// Get rider status
  Future<bool> getRiderStatus() async {
    try {
      return await readBool(StorageKeys.riderOnlineStatus) ?? false;
    } catch (e) {
      debugPrint('❌ Error getting rider status: $e');
      return false;
    }
  }

  /// Save app preferences
  Future<void> saveAppPreferences({
    bool? biometricEnabled,
    String? language,
    bool? notificationsEnabled,
  }) async {
    try {
      if (biometricEnabled != null) {
        await writeBool(StorageKeys.biometricEnabled, biometricEnabled);
      }
      if (language != null) {
        await write(StorageKeys.selectedLanguage, language);
      }
      if (notificationsEnabled != null) {
        await writeBool(StorageKeys.notificationsEnabled, notificationsEnabled);
      }
      debugPrint('✅ App preferences saved');
    } catch (e) {
      debugPrint('❌ Error saving app preferences: $e');
    }
  }

  /// Get app preferences
  Future<Map<String, dynamic>> getAppPreferences() async {
    try {
      return {
        'biometricEnabled': await readBool(StorageKeys.biometricEnabled) ?? false,
        'language': await read(StorageKeys.selectedLanguage) ?? 'en',
        'notificationsEnabled': await readBool(StorageKeys.notificationsEnabled) ?? true,
      };
    } catch (e) {
      debugPrint('❌ Error getting app preferences: $e');
      return {
        'biometricEnabled': false,
        'language': 'en',
        'notificationsEnabled': true,
      };
    }
  }

  /// Save registration step progress
  Future<void> saveRegistrationStep(String mobileNumber, int step) async {
    try {
      final stepKey = StorageKeys.getCurrentStepKey(mobileNumber);
      await writeInt(stepKey, step);
      debugPrint('✅ Registration step saved: $step for $mobileNumber');
    } catch (e) {
      debugPrint('❌ Error saving registration step: $e');
    }
  }

  /// Get registration step progress
  Future<int> getRegistrationStep(String mobileNumber) async {
    try {
      final stepKey = StorageKeys.getCurrentStepKey(mobileNumber);
      return await readInt(stepKey) ?? 0;
    } catch (e) {
      debugPrint('❌ Error getting registration step: $e');
      return 0;
    }
  }

  /// Clear all rider data (for logout)
  Future<void> clearRiderData() async {
    try {
      final mobileNumber = await read(StorageKeys.mobileNumber);
      if (mobileNumber != null) {
        await clearUserData(mobileNumber);
      }

      // Clear main rider keys
      final riderKeys = [
        StorageKeys.mobileNumber,
        StorageKeys.riderId,
        StorageKeys.riderName,
        StorageKeys.authToken,
        StorageKeys.riderProfile,
        StorageKeys.latitude,
        StorageKeys.longitude,
        StorageKeys.currentAddress,
        StorageKeys.riderOnlineStatus,
      ];

      await batchDelete(riderKeys);
      debugPrint('✅ All rider data cleared');
    } catch (e) {
      debugPrint('❌ Error clearing rider data: $e');
    }
  }

  /// Check if rider is logged in
  Future<bool> isRiderLoggedIn() async {
    try {
      final mobileNumber = await read(StorageKeys.mobileNumber);
      final riderId = await read(StorageKeys.riderId);
      return mobileNumber != null && riderId != null;
    } catch (e) {
      debugPrint('❌ Error checking rider login status: $e');
      return false;
    }
  }

  /// Get current rider mobile number
  Future<String?> getCurrentRiderMobile() async {
    try {
      return await read(StorageKeys.mobileNumber);
    } catch (e) {
      debugPrint('❌ Error getting current rider mobile: $e');
      return null;
    }
  }

  /// Get current rider ID
  Future<String?> getCurrentRiderId() async {
    try {
      return await read(StorageKeys.riderId);
    } catch (e) {
      debugPrint('❌ Error getting current rider ID: $e');
      return null;
    }
  }

  /// Save temporary data (for form persistence)
  Future<void> saveTempData(String key, Map<String, dynamic> data) async {
    try {
      await writeMap('temp_$key', data);
      debugPrint('✅ Temporary data saved: $key');
    } catch (e) {
      debugPrint('❌ Error saving temporary data: $e');
    }
  }

  /// Get temporary data
  Future<Map<String, dynamic>?> getTempData(String key) async {
    try {
      return await readMap('temp_$key');
    } catch (e) {
      debugPrint('❌ Error getting temporary data: $e');
      return null;
    }
  }

  /// Clear temporary data
  Future<void> clearTempData(String key) async {
    try {
      await delete('temp_$key');
      debugPrint('✅ Temporary data cleared: $key');
    } catch (e) {
      debugPrint('❌ Error clearing temporary data: $e');
    }
  }
}
