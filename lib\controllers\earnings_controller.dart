import 'package:get/get.dart';
import 'package:kisankonnect_rider/models/earnings_models.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/constants/storage_keys.dart';

class EarningsController extends GetxController {
  final ApiService _apiService = ApiService.instance;
  late final SecureStorageService _storage;

  // Observable data
  final Rx<WeeklyEarningsResponse?> _weeklyEarnings = Rx<WeeklyEarningsResponse?>(null);
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;
  final Rx<EarningsPeriod> _selectedPeriod = EarningsPeriod.thisWeek.obs;

  // Getters
  WeeklyEarningsResponse? get weeklyEarnings => _weeklyEarnings.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  EarningsPeriod get selectedPeriod => _selectedPeriod.value;

  // Computed values
  double get totalEarnings => weeklyEarnings?.totalWeeklyEarnings ?? 0.0;
  double get totalIncentives => weeklyEarnings?.totalWeeklyIncentives ?? 0.0;
  int get totalOrders => weeklyEarnings?.totalWeeklyOrders ?? 0;
  double get orderEarnings => totalEarnings - totalIncentives;
  double get rainSurgeEarnings => weeklyEarnings?.getIncentiveByType('rain') ?? 0.0;
  double get attendanceIncentive => weeklyEarnings?.getIncentiveByType('attendance') ?? 0.0;
  double get peakHourIncentive => weeklyEarnings?.getIncentiveByType('peak') ?? 0.0;
  double get areaWiseIncentive => weeklyEarnings?.getIncentiveByType('area') ?? 0.0;
  double get festivalIncentive => weeklyEarnings?.getIncentiveByType('festival') ?? 0.0;
  double get slabDeliveryIncentive => weeklyEarnings?.getIncentiveByType('slab') ?? 0.0;

  @override
  void onInit() {
    super.onInit();
    _storage = Get.find<SecureStorageService>();
    _loadEarningsForPeriod(_selectedPeriod.value);
  }

  /// Change selected period and reload data
  void changePeriod(EarningsPeriod period) {
    if (_selectedPeriod.value != period) {
      _selectedPeriod.value = period;
      _loadEarningsForPeriod(period);
    }
  }

  /// Load earnings data based on selected period
  Future<void> _loadEarningsForPeriod(EarningsPeriod period) async {
    switch (period) {
      case EarningsPeriod.today:
        await _loadTodayEarnings();
        break;
      case EarningsPeriod.thisWeek:
        await _loadWeeklyEarnings();
        break;
      case EarningsPeriod.thisMonth:
        await _loadMonthlyEarnings();
        break;
    }
  }

  /// Load weekly earnings data
  Future<void> _loadWeeklyEarnings() async {
    if (_isLoading.value) return;

    _isLoading.value = true;
    _error.value = '';

    try {
      // Get user data from storage
      final userData = await _getUserData();

      // Get current date for this week
      final currentDate = DateTime.now().toIso8601String().split('T')[0];

      print('📊 Loading weekly earnings for SRID: ${userData['srid']}, Date: $currentDate');

      final response = await _apiService.earnings.getRiderWeeklyEarnings(
        date: currentDate,
        srid: userData['srid'],
        kfhId: userData['kfhid'],
        cdcType: userData['cdcType'],
      );

      if (response.isSuccess && response.data != null) {
        final earningsResponse = WeeklyEarningsResponse.fromJson(response.data);

        if (earningsResponse.isSuccess) {
          _weeklyEarnings.value = earningsResponse;
          print('✅ Successfully loaded weekly earnings: ${earningsResponse.totalWeeklyEarnings}');
          print('📋 Total orders: ${earningsResponse.totalWeeklyOrders}');
          print('🎁 Total incentives: ${earningsResponse.totalWeeklyIncentives}');
        } else {
          _error.value = 'Failed to load earnings data';
          print('❌ Earnings API returned error: ${earningsResponse.msg}');
        }
      } else {
        _error.value = response.error ?? 'Failed to load earnings data';
        print('❌ API call failed: ${response.error}');
      }
    } catch (e) {
      _error.value = 'Error loading earnings: $e';
      print('❌ Exception loading earnings: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Load today's earnings data
  Future<void> _loadTodayEarnings() async {
    if (_isLoading.value) return;

    _isLoading.value = true;
    _error.value = '';

    try {
      // Get user data from storage
      final userData = await _getUserData();

      // Get today's date
      final todayDate = DateTime.now().toIso8601String().split('T')[0];

      print('📊 Loading today earnings for SRID: ${userData['srid']}, Date: $todayDate');

      final response = await _apiService.earnings.getRiderWeeklyEarnings(
        date: todayDate,
        srid: userData['srid'],
        kfhId: userData['kfhid'],
        cdcType: userData['cdcType'],
      );

      if (response.isSuccess && response.data != null) {
        final earningsResponse = WeeklyEarningsResponse.fromJson(response.data);

        if (earningsResponse.isSuccess) {
          _weeklyEarnings.value = earningsResponse;
          print('✅ Successfully loaded today earnings: ${earningsResponse.totalWeeklyEarnings}');
          print('📋 Total orders: ${earningsResponse.totalWeeklyOrders}');
          print('🎁 Total incentives: ${earningsResponse.totalWeeklyIncentives}');
        } else {
          _error.value = 'Failed to load today earnings data';
          print('❌ Today earnings API returned error: ${earningsResponse.msg}');
        }
      } else {
        _error.value = response.error ?? 'Failed to load today earnings data';
        print('❌ Today earnings API call failed: ${response.error}');
      }
    } catch (e) {
      _error.value = 'Error loading today earnings: $e';
      print('❌ Exception loading today earnings: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Load monthly earnings data
  Future<void> _loadMonthlyEarnings() async {
    if (_isLoading.value) return;

    _isLoading.value = true;
    _error.value = '';

    try {
      // Get user data from storage
      final userData = await _getUserData();

      // Get first day of current month
      final now = DateTime.now();
      final firstDayOfMonth = DateTime(now.year, now.month, 1);
      final monthDate = firstDayOfMonth.toIso8601String().split('T')[0];

      print('📊 Loading monthly earnings for SRID: ${userData['srid']}, Date: $monthDate');

      final response = await _apiService.earnings.getRiderWeeklyEarnings(
        date: monthDate,
        srid: userData['srid'],
        kfhId: userData['kfhid'],
        cdcType: userData['cdcType'],
      );

      if (response.isSuccess && response.data != null) {
        final earningsResponse = WeeklyEarningsResponse.fromJson(response.data);

        if (earningsResponse.isSuccess) {
          _weeklyEarnings.value = earningsResponse;
          print('✅ Successfully loaded monthly earnings: ${earningsResponse.totalWeeklyEarnings}');
          print('📋 Total orders: ${earningsResponse.totalWeeklyOrders}');
          print('🎁 Total incentives: ${earningsResponse.totalWeeklyIncentives}');
        } else {
          _error.value = 'Failed to load monthly earnings data';
          print('❌ Monthly earnings API returned error: ${earningsResponse.msg}');
        }
      } else {
        _error.value = response.error ?? 'Failed to load monthly earnings data';
        print('❌ Monthly earnings API call failed: ${response.error}');
      }
    } catch (e) {
      _error.value = 'Error loading monthly earnings: $e';
      print('❌ Exception loading monthly earnings: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Get user data from storage for API calls
  Future<Map<String, dynamic>> _getUserData() async {
    try {
      // Get SRID from stored login info
      final sridStr = await _storage.read(StorageKeys.riderId) ?? '0';
      final kfhidStr = await _storage.read(StorageKeys.kfhStatus) ?? '0';
      final cdcTypeStr = await _storage.read(StorageKeys.dcId) ?? '0';

      final srid = int.tryParse(sridStr) ?? 0;
      final kfhid = int.tryParse(kfhidStr) ?? 0;
      final cdcType = int.tryParse(cdcTypeStr) ?? 0;

      print('📱 Retrieved earnings user data: SRID=$srid, KFHID=$kfhid, CDCType=$cdcType');

      if (srid == 0 || kfhid == 0 || cdcType == 0) {
        throw Exception('Missing required user data for earnings API');
      }

      return {
        'srid': srid.toString(),
        'kfhid': kfhid.toString(),
        'cdcType': cdcType.toString(),
      };
    } catch (e) {
      print('❌ Error getting user data for earnings: $e');
      throw Exception('Failed to get user data: $e');
    }
  }

  /// Refresh earnings data
  Future<void> refreshEarnings() async {
    await _loadEarningsForPeriod(_selectedPeriod.value);
  }

  /// Format currency for display
  String formatCurrency(double amount) {
    return '₹${amount.toStringAsFixed(2)}';
  }

  /// Format orders count for display
  String formatOrdersCount(int count) {
    return '$count ${count == 1 ? 'order' : 'orders'}';
  }

  /// Get earnings summary text based on selected period
  String get earningsSummaryText {
    switch (_selectedPeriod.value) {
      case EarningsPeriod.today:
        return 'Total earnings of the day';
      case EarningsPeriod.thisWeek:
        return 'Total earnings of the week';
      case EarningsPeriod.thisMonth:
        return 'Total earnings of the month';
    }
  }

  /// Get previous period earnings text
  String get previousEarningsText {
    switch (_selectedPeriod.value) {
      case EarningsPeriod.today:
        return 'Total earnings of the previous day';
      case EarningsPeriod.thisWeek:
        return 'Total earnings of the previous week';
      case EarningsPeriod.thisMonth:
        return 'Total earnings of the previous month';
    }
  }

  /// Get previous period earnings amount (placeholder for now)
  /// TODO: Implement API call for previous period data
  double get previousPeriodEarnings {
    // For now, return 0 since we don't have previous period API
    // This can be implemented later when the API is available
    return 0.0;
  }

  /// Get potential earnings text for incentive banner
  String get potentialEarningsText {
    // Calculate potential earnings based on current performance
    // This is a rough estimate - can be made more sophisticated
    final currentEarnings = totalEarnings;
    final potentialBonus = currentEarnings > 0 ? (currentEarnings * 0.5) : 1200.0;
    return '₹${potentialBonus.toStringAsFixed(0)}+';
  }
}
