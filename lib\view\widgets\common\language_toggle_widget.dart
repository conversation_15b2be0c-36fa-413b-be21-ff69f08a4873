import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:get/get.dart';

class LanguageToggleWidget extends StatelessWidget {
  const LanguageToggleWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<LocalizationService>(
      builder: (localizationService) {
        return Container(
          margin: EdgeInsets.symmetric(
            horizontal: ResponsiveUtils.spacingM(context),
            vertical: ResponsiveUtils.spacingS(context),
          ),
          child: Material(
            color: AppColors.cardBackground,
            borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
            elevation: 2,
            shadowColor: AppColors.shadowLight,
            child: InkWell(
              onTap: () => _showLanguageSelector(context, localizationService),
              borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
              child: Padding(
                padding: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
                child: Row(
                  children: [
                    // Language Icon with flag-like design
                    Container(
                      width: ResponsiveUtils.width(context, 12),
                      height: ResponsiveUtils.width(context, 12),
                      decoration: BoxDecoration(
                        color: AppColors.green.withAlpha(25),
                        borderRadius:
                            BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.small)),
                      ),
                      child: Icon(
                        Icons.language,
                        color: AppColors.green,
                        size: ResponsiveUtils.iconSize(context, IconSizeType.medium),
                      ),
                    ),

                    SizedBox(width: ResponsiveUtils.spacingM(context)),

                    // Language Text
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Language / भाषा / भाषा',
                            style: TextStyle(
                              fontSize: ResponsiveUtils.fontSize(context, 16),
                              fontWeight: FontWeight.w600,
                              color: AppColors.textPrimary,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          SizedBox(height: ResponsiveUtils.spacingXS(context) / 2),
                          Wrap(
                            spacing: ResponsiveUtils.spacingXS(context),
                            children: [
                              _buildLanguageIndicator(
                                context,
                                'EN',
                                localizationService.isEnglish,
                              ),
                              _buildLanguageIndicator(
                                context,
                                'हि',
                                localizationService.isHindi,
                              ),
                              _buildLanguageIndicator(
                                context,
                                'मर',
                                localizationService.isMarathi,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    // Arrow indicator
                    Icon(
                      Icons.keyboard_arrow_right,
                      color: AppColors.textSecondary,
                      size: ResponsiveUtils.iconSize(context, IconSizeType.medium),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildLanguageIndicator(BuildContext context, String code, bool isSelected) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: ResponsiveUtils.spacingS(context),
        vertical: ResponsiveUtils.spacingXS(context) / 2,
      ),
      decoration: BoxDecoration(
        color: isSelected ? AppColors.green : AppColors.borderLight,
        borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.small)),
      ),
      child: Text(
        code,
        style: TextStyle(
          fontSize: ResponsiveUtils.fontSize(context, 12),
          fontWeight: FontWeight.w600,
          color: isSelected ? Colors.white : AppColors.textSecondary,
        ),
      ),
    );
  }

  void _showLanguageSelector(BuildContext context, LocalizationService localizationService) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.large)),
        ),
      ),
      builder: (context) {
        return Container(
          padding: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Expanded(
                    child: Text(
                      'Select Language / भाषा चुनें / भाषा निवडा',
                      style: TextStyle(
                        fontSize: ResponsiveUtils.fontSize(context, 18),
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  SizedBox(width: ResponsiveUtils.spacingS(context)),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(
                      Icons.close,
                      color: AppColors.textSecondary,
                    ),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),

              SizedBox(height: ResponsiveUtils.spacingM(context)),

              // Language Options
              _buildLanguageOption(
                context,
                'English',
                'EN',
                'English',
                localizationService.isEnglish,
                () {
                  localizationService.setLanguage('en');
                  Navigator.pop(context);
                },
              ),

              _buildLanguageOption(
                context,
                'हिंदी',
                'हि',
                'Hindi',
                localizationService.isHindi,
                () {
                  localizationService.setLanguage('hi');
                  Navigator.pop(context);
                },
              ),

              _buildLanguageOption(
                context,
                'मराठी',
                'मर',
                'Marathi',
                localizationService.isMarathi,
                () {
                  localizationService.setLanguage('mr');
                  Navigator.pop(context);
                },
              ),

              SizedBox(height: ResponsiveUtils.spacingM(context)),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLanguageOption(
    BuildContext context,
    String languageName,
    String shortCode,
    String englishName,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
        margin: EdgeInsets.only(bottom: ResponsiveUtils.spacingS(context)),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.green.withAlpha(25) : Colors.transparent,
          borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
          border: Border.all(
            color: isSelected ? AppColors.green : AppColors.borderLight,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            // Language code indicator
            Container(
              width: ResponsiveUtils.width(context, 12),
              height: ResponsiveUtils.width(context, 12),
              decoration: BoxDecoration(
                color: isSelected ? AppColors.green : AppColors.borderLight,
                borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.small)),
              ),
              child: Center(
                child: Text(
                  shortCode,
                  style: TextStyle(
                    fontSize: ResponsiveUtils.fontSize(context, 12),
                    fontWeight: FontWeight.w600,
                    color: isSelected ? Colors.white : AppColors.textSecondary,
                  ),
                ),
              ),
            ),

            SizedBox(width: ResponsiveUtils.spacingM(context)),

            // Language names
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    languageName,
                    style: TextStyle(
                      fontSize: ResponsiveUtils.fontSize(context, 16),
                      fontWeight: FontWeight.w600,
                      color: isSelected ? AppColors.green : AppColors.textPrimary,
                    ),
                  ),
                  if (englishName != languageName) ...[
                    SizedBox(height: ResponsiveUtils.spacingXS(context) / 2),
                    Text(
                      englishName,
                      style: TextStyle(
                        fontSize: ResponsiveUtils.fontSize(context, 12),
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ],
              ),
            ),

            // Selection indicator
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: AppColors.green,
                size: ResponsiveUtils.iconSize(context, IconSizeType.medium),
              ),
          ],
        ),
      ),
    );
  }
}
