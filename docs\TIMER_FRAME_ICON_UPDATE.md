# Timer Frame Icon Update

## Overview

The work timer row in the shift info section now uses a custom timer frame icon instead of the previous green circular background with white timer icon.

## Changes Made

### 1. Code Changes
- **File**: `lib/view/screens/home/<USER>/shift_info_section.dart`
- **Lines**: 221-226
- **Change**: Replaced `Container` with green background and `Icons.access_time` with `Image.asset('assets/icons/timer-frame.png')`

### 2. Asset Configuration
- **File**: `pubspec.yaml`
- **Addition**: Added `- assets/icons/timer-frame.png` to the assets section

## Implementation Details

### Before (Green Circle with Icon)
```dart
Container(
  width: 24,
  height: 24,
  decoration: BoxDecoration(
    color: AppColors.green,
    shape: BoxShape.circle,
  ),
  child: Icon(
    Icons.access_time,
    color: Colors.white,
    size: 14,
  ),
),
```

### After (Custom Frame Image)
```dart
Image.asset(
  'assets/icons/timer-frame.png',
  width: 24,
  height: 24,
),
```

## Required Action

### Step 1: Add Your Timer Frame Image
1. Save your timer frame icon as `timer-frame.png`
2. Place it in the `assets/icons/` directory
3. Ensure the image has a transparent background

### Step 2: Image Specifications
- **Format**: PNG (recommended for transparency)
- **Size**: 24x24px (exact size for optimal display)
- **Background**: Transparent
- **Design**: Should include the green circular frame with timer icon
- **Color**: Pre-colored (no programmatic tinting applied)

### Step 3: Test the Implementation
```bash
flutter pub get
flutter run
```

## Current Timer Row Layout

```
🟢 04:30 hr                    [Take break]
   out of 8h
```

Where 🟢 represents your custom timer frame icon.

## Features

### No Color Tinting
Unlike other icons in the app, this timer frame icon:
- **No dynamic coloring**: Uses the original image colors
- **Pre-designed**: Should include all colors and styling in the image
- **Static appearance**: Maintains consistent look regardless of app state

### Responsive Sizing
- **Fixed size**: 24x24px for consistent appearance
- **Proper alignment**: Aligned with text baseline
- **Spacing**: Proper spacing with adjacent text elements

## File Structure
```
assets/
└── icons/
    ├── working-time.png     (Clock icon for shift header)
    ├── timer-frame.png      (Timer frame icon - ADD THIS FILE)
    └── README.md            (Documentation)
```

## Benefits

1. **Custom Design**: Use your exact timer frame design
2. **Brand Consistency**: Matches your app's visual identity
3. **High Quality**: Vector-based or high-resolution image
4. **No Dependencies**: Simple image asset, no complex styling

## Troubleshooting

### Image Not Showing
1. Verify the image is saved as `assets/icons/timer-frame.png`
2. Run `flutter pub get` after adding the image
3. Check that the image has the correct format (PNG)
4. Ensure the image path in code matches the actual file location

### Image Quality Issues
1. Use a 24x24px image for pixel-perfect display
2. Ensure the image has a transparent background
3. Use PNG format for best quality with transparency
4. Avoid compression that might reduce quality

### Alignment Issues
1. Ensure the image is 24x24px (not larger or smaller)
2. Check that the icon design is centered within the 24x24px canvas
3. Verify transparent padding around the icon if needed

## Design Guidelines

### Timer Frame Icon Design
- **Size**: 24x24px canvas
- **Icon area**: Approximately 20x20px with 2px padding
- **Colors**: Green frame with appropriate timer icon inside
- **Style**: Should match the design you provided
- **Format**: PNG with transparency

### Visual Consistency
- Should complement the "Take break" button styling
- Should work well with the "04:30 hr" text
- Should maintain readability at 24px size

## Next Steps

1. **Add the Image**: Place your timer frame icon as `assets/icons/timer-frame.png`
2. **Test**: Run the app to see the custom timer frame in action
3. **Adjust**: If needed, modify the image design for optimal appearance at 24px

The implementation is ready - you just need to add your custom timer frame icon image to complete the setup!
