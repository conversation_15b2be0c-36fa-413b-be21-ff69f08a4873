/// Model for Rider Document Registration Insert API request
class DocumentRegistrationRequest {
  final String mobileNo;
  final String panNo;
  final String dlNo;
  final String? panCardPhotoPath;
  final String? dlPhotoPath;
  final String? selfImgPath;

  const DocumentRegistrationRequest({
    required this.mobileNo,
    required this.panNo,
    required this.dlNo,
    this.panCardPhotoPath,
    this.dlPhotoPath,
    this.selfImgPath,
  });

  /// Create from JSON
  factory DocumentRegistrationRequest.fromJson(Map<String, dynamic> json) {
    return DocumentRegistrationRequest(
      mobileNo: json['mobileNo'] ?? '',
      panNo: json['panNo'] ?? '',
      dlNo: json['dlNo'] ?? '',
      panCardPhotoPath: json['panCardPhotoPath'],
      dlPhotoPath: json['dlPhotoPath'],
      selfImgPath: json['selfImgPath'],
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'mobileNo': mobileNo,
      'panNo': panNo,
      'dlNo': dlNo,
    };

    // Add optional fields if they exist
    if (panCardPhotoPath != null && panCardPhotoPath!.isNotEmpty) {
      json['panCardPhotoPath'] = panCardPhotoPath;
    }
    if (dlPhotoPath != null && dlPhotoPath!.isNotEmpty) {
      json['dlPhotoPath'] = dlPhotoPath;
    }
    if (selfImgPath != null && selfImgPath!.isNotEmpty) {
      json['selfImgPath'] = selfImgPath;
    }

    return json;
  }

  /// Create a copy with updated fields
  DocumentRegistrationRequest copyWith({
    String? mobileNo,
    String? panNo,
    String? dlNo,
    String? panCardPhotoPath,
    String? dlPhotoPath,
    String? selfImgPath,
  }) {
    return DocumentRegistrationRequest(
      mobileNo: mobileNo ?? this.mobileNo,
      panNo: panNo ?? this.panNo,
      dlNo: dlNo ?? this.dlNo,
      panCardPhotoPath: panCardPhotoPath ?? this.panCardPhotoPath,
      dlPhotoPath: dlPhotoPath ?? this.dlPhotoPath,
      selfImgPath: selfImgPath ?? this.selfImgPath,
    );
  }

  /// Validate required fields
  bool isValid() {
    return mobileNo.isNotEmpty && panNo.isNotEmpty && dlNo.isNotEmpty;
  }

  /// Get validation errors
  List<String> getValidationErrors() {
    final errors = <String>[];

    if (mobileNo.isEmpty) errors.add('Mobile number is required');
    if (panNo.isEmpty) errors.add('PAN number is required');
    if (dlNo.isEmpty) errors.add('Driving license number is required');

    return errors;
  }

  /// Check if any document photos are provided
  bool hasDocumentPhotos() {
    return (panCardPhotoPath != null && panCardPhotoPath!.isNotEmpty) ||
        (dlPhotoPath != null && dlPhotoPath!.isNotEmpty) ||
        (selfImgPath != null && selfImgPath!.isNotEmpty);
  }

  /// Get list of provided document types
  List<String> getProvidedDocuments() {
    final documents = <String>[];

    if (panCardPhotoPath != null && panCardPhotoPath!.isNotEmpty) {
      documents.add('PAN Card');
    }
    if (dlPhotoPath != null && dlPhotoPath!.isNotEmpty) {
      documents.add('Driving License');
    }
    if (selfImgPath != null && selfImgPath!.isNotEmpty) {
      documents.add('Selfie');
    }

    return documents;
  }

  @override
  String toString() {
    return 'DocumentRegistrationRequest(mobileNo: $mobileNo, panNo: $panNo, dlNo: $dlNo)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is DocumentRegistrationRequest &&
        other.mobileNo == mobileNo &&
        other.panNo == panNo &&
        other.dlNo == dlNo &&
        other.panCardPhotoPath == panCardPhotoPath &&
        other.dlPhotoPath == dlPhotoPath &&
        other.selfImgPath == selfImgPath;
  }

  @override
  int get hashCode {
    return mobileNo.hashCode ^
        panNo.hashCode ^
        dlNo.hashCode ^
        panCardPhotoPath.hashCode ^
        dlPhotoPath.hashCode ^
        selfImgPath.hashCode;
  }
}

/// Response model for document registration
class DocumentRegistrationResponse {
  final int status;
  final String message;
  final dynamic data;

  const DocumentRegistrationResponse({
    required this.status,
    required this.message,
    this.data,
  });

  factory DocumentRegistrationResponse.fromJson(Map<String, dynamic> json) {
    return DocumentRegistrationResponse(
      status: json['status'] ?? 0,
      message: json['msg'] ?? '',
      data: json['data'],
    );
  }

  bool get isSuccess => status == 200;

  @override
  String toString() {
    return 'DocumentRegistrationResponse(status: $status, message: $message)';
  }
}

/// Document types enum
enum DocumentType {
  panCard('PAN Card'),
  drivingLicense('Driving License'),
  selfie('Selfie');

  const DocumentType(this.displayName);
  final String displayName;
}

/// Document validation helper
class DocumentValidator {
  /// Validate PAN number format
  static bool isValidPanNumber(String panNo) {
    final regex = RegExp(r'^[A-Z]{5}[0-9]{4}[A-Z]{1}$');
    return regex.hasMatch(panNo);
  }

  /// Validate driving license number format (basic validation)
  static bool isValidDLNumber(String dlNo) {
    // Basic validation - should be alphanumeric and at least 8 characters
    final regex = RegExp(r'^[A-Z0-9]{8,}$');
    return regex.hasMatch(dlNo.toUpperCase());
  }

  /// Validate mobile number format
  static bool isValidMobileNumber(String mobileNo) {
    final regex = RegExp(r'^[6-9]\d{9}$');
    return regex.hasMatch(mobileNo);
  }

  /// Get PAN number validation error message
  static String? getPanValidationError(String panNo) {
    if (panNo.isEmpty) return 'PAN number is required';
    if (!isValidPanNumber(panNo)) {
      return 'Invalid PAN format. Should be like **********';
    }
    return null;
  }

  /// Get DL number validation error message
  static String? getDLValidationError(String dlNo) {
    if (dlNo.isEmpty) return 'Driving license number is required';
    if (!isValidDLNumber(dlNo)) {
      return 'Invalid DL format. Should be alphanumeric and at least 8 characters';
    }
    return null;
  }

  /// Get mobile number validation error message
  static String? getMobileValidationError(String mobileNo) {
    if (mobileNo.isEmpty) return 'Mobile number is required';
    if (!isValidMobileNumber(mobileNo)) {
      return 'Invalid mobile number format';
    }
    return null;
  }
}
