import 'package:get/get.dart';
import 'package:kisankonnect_rider/models/refer_earn_models.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/constants/storage_keys.dart';

class ReferEarnController extends GetxController {
  final ReferEarnService _apiService = ReferEarnService.instance;
  late SecureStorageService _storage;

  // Observable variables
  final _isLoading = true.obs;
  final _error = ''.obs;
  final _referEarnData = Rxn<ReferEarnResponse>();

  // Getters
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  ReferEarnResponse? get referEarnData => _referEarnData.value;

  // Get referral code with fallback
  String get referralCode {
    return _referEarnData.value?.referEarnData.referralCode ?? 'PTHJTYR&656444';
  }

  // Get referral amount (default 5000)
  String get referralAmount {
    return '5000'; // Fixed amount for referrer
  }

  // Get friend amount with fallback
  String get friendAmount {
    return _referEarnData.value?.referEarnData.friendAmount ?? '10000';
  }

  // Get total earnings with fallback
  String get totalEarnings {
    return _referEarnData.value?.referEarnData.totalEarnings ?? '0';
  }

  // Get total referrals with fallback
  String get totalReferrals {
    return _referEarnData.value?.referEarnData.totalReferrals ?? '0';
  }

  // Get referral list with fallback
  List<ReferralItem> get referralList {
    return _referEarnData.value?.referEarnData.yourReferrals ?? [];
  }

  // Get banner image URL
  String get bannerImageUrl {
    return _referEarnData.value?.referEarnData.bannerImage ?? '';
  }

  // Get share message
  String get shareMessage {
    return _referEarnData.value?.referEarnData.msg1 ?? '';
  }

  @override
  void onInit() {
    super.onInit();
    _storage = Get.find<SecureStorageService>();

    // Start with loading false to show UI immediately
    _isLoading.value = false;

    // Load data in background
    loadReferEarnData();
  }

  /// Load refer and earn data from API
  Future<void> loadReferEarnData() async {
    // Don't show loading initially - let UI show with fallback data
    _error.value = '';

    try {
      // Get user data from storage
      final mobileNo = await _storage.read(StorageKeys.mobileNumber) ?? '';
      final riderId = await _storage.read(StorageKeys.userName) ?? '';

      print('📊 Loading refer earn data for Mobile: $mobileNo, RiderID: $riderId');

      if (mobileNo.isEmpty || riderId.isEmpty) {
        _error.value = 'User data not found. Please login again.';
        print('❌ Missing user data - Mobile: $mobileNo, RiderID: $riderId');
        return;
      }

      // Add timeout to prevent long loading
      final response = await _apiService
          .getReferEarnBanner(
        mobileNo: mobileNo,
        riderId: riderId,
      )
          .timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw Exception('Request timeout - please check your internet connection');
        },
      );

      if (response.isSuccess && response.data != null) {
        final referEarnResponse = ReferEarnResponse.fromJson(response.data);

        if (referEarnResponse.isSuccess) {
          _referEarnData.value = referEarnResponse;
          print('✅ Successfully loaded refer earn data');
          print('📋 Referral Code: $referralCode');
          print('💰 Referral Amount: ₹$referralAmount');
          print('👥 Total Referrals: $totalReferrals');
        } else {
          _error.value = 'Failed to load refer earn data';
          print('❌ Refer earn API returned error: ${referEarnResponse.msg}');
        }
      } else {
        _error.value = response.error ?? 'Failed to load refer earn data';
        print('❌ Refer earn API call failed: ${response.error}');
      }
    } catch (e) {
      _error.value = 'Error loading refer earn data: $e';
      print('❌ Exception loading refer earn data: $e');
    } finally {
      // Don't set loading to false since we started with false
      // This allows UI to remain responsive
    }
  }

  /// Refresh refer and earn data (for pull-to-refresh)
  Future<void> refreshReferEarnData() async {
    // Show subtle loading for refresh
    _isLoading.value = true;
    await loadReferEarnData();
    _isLoading.value = false;
  }
}
