import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

import 'package:kisankonnect_rider/utils/responsive_utils.dart';
import 'package:kisankonnect_rider/routes/app_pages.dart';
import 'package:kisankonnect_rider/controllers/reports_controller.dart';
import 'package:get/get.dart';
import '../../../widgets/loading/elegant_shimmer.dart';

class ReportsCarousel extends StatefulWidget {
  const ReportsCarousel({super.key});

  @override
  State<ReportsCarousel> createState() => _ReportsCarouselState();
}

class _ReportsCarouselState extends State<ReportsCarousel> {
  final CarouselSliderController _carouselController = CarouselSliderController();
  late final ReportsController _controller;
  int _currentIndex = 0;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _controller = Get.find<ReportsController>();
    _listenToController();
  }

  void _listenToController() {
    // Listen to controller loading state for dynamic shimmer
    ever(_controller.isLoading, (bool isLoading) {
      if (mounted) {
        setState(() {
          _isLoading = isLoading;
        });
      }
    });

    // Set initial loading state
    _isLoading = _controller.isLoading.value;
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final reportsData = _controller.reportsDataForUI;
      final itemCount = _isLoading ? 4 : reportsData.length; // Show 4 skeleton items

      // Debug info
      print(
          '📊 Reports Debug - Loading: $_isLoading, Data Length: ${reportsData.length}, Current Index: $_currentIndex');

      return ElegantShimmer(
        enabled: _isLoading,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header outside the container
            Padding(
              padding: EdgeInsets.symmetric(horizontal: ResponsiveUtils.spacingM(context)),
              child: Text(
                'Reports',
                style: AppTextTheme.cardTitle,
              ),
            ),

            SizedBox(height: ResponsiveUtils.height(context, 1.5)),

            // Carousel Section
            CarouselSlider.builder(
              carouselController: _carouselController,
              itemCount: itemCount,
              itemBuilder: (context, index, realIndex) {
                final data = _isLoading
                    ? {
                        'riderName': 'Loading Rider Name - 000000',
                        'onTime': 80,
                        'early': 10,
                        'delay': 80,
                      }
                    : reportsData[index];
                return ReportsSection(data: data);
              },
              options: CarouselOptions(
                height: ResponsiveUtils.height(context, 18), // Reduced from 25% to 18%
                autoPlay: false,
                enlargeCenterPage: false,
                viewportFraction: 0.95,
                initialPage: 0,
                enableInfiniteScroll: !_isLoading && reportsData.length > 1,
                reverse: false,
                scrollDirection: Axis.horizontal,
                onPageChanged: (index, reason) {
                  if (!_isLoading) {
                    setState(() {
                      _currentIndex = index;
                    });
                  }
                },
              ),
            ),

            SizedBox(height: ResponsiveUtils.height(context, 1)),

            // Page Indicator
            if (!_isLoading && reportsData.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(bottom: 20.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (reportsData.length > 1)
                      Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: _currentIndex == 0 ? AppColors.green : AppColors.green.withValues(alpha: 0.3),
                        ),
                      ),
                    if (reportsData.length > 1) const SizedBox(width: 8),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: ResponsiveUtils.spacingS(context),
                        vertical: ResponsiveUtils.spacingXS(context),
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.green,
                        borderRadius:
                            BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.large)),
                      ),
                      child: Text(
                        '${_currentIndex + 1}/${reportsData.length}',
                        style: AppTextTheme.cardCaption.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'Figtree',
                        ),
                      ),
                    ),
                    if (reportsData.length > 1) const SizedBox(width: 8),
                    if (reportsData.length > 1)
                      Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: _currentIndex == reportsData.length - 1
                              ? AppColors.green
                              : AppColors.green.withValues(alpha: 0.3),
                        ),
                      ),
                  ],
                ),
              ),
          ],
        ),
      );
    });
  }
}

class ReportsSection extends StatelessWidget {
  final Map<String, dynamic> data;

  const ReportsSection({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: ResponsiveUtils.spacingS(context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: ResponsiveUtils.width(context, 2),
            offset: Offset(0, ResponsiveUtils.height(context, 0.5)),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(
              horizontal: ResponsiveUtils.spacingM(context),
              vertical: ResponsiveUtils.spacingXS(context) * 0.8, // Slightly reduced padding
            ),
            decoration: BoxDecoration(
              color: const Color(0xFFF2F7FF), // Light blue background
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
                topRight: Radius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
              ),
            ),
            child: Text(
              data['riderName'],
              style: AppTextTheme.cardSubtitle.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          // Report Rows
          _ReportRow(
            label: 'On Time orders',
            percent: data['onTime'],
            color: AppColors.green,
          ),
          _ReportRow(
            label: 'Early orders',
            percent: data['early'],
            color: Colors.red,
          ),
          _ReportRow(
            label: 'Delay orders',
            percent: data['delay'],
            color: Colors.blue,
          ),

          // Footer
          GestureDetector(
            onTap: () {
              Get.toNamed(AppRoutes.allEarnings);
            },
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(
                horizontal: ResponsiveUtils.spacingM(context),
                vertical: ResponsiveUtils.spacingXS(context) * 0.8, // Slightly reduced padding
              ),
              decoration: BoxDecoration(
                color: const Color(0xFFE8FDF3), // Light green background
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
                  bottomRight: Radius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    'View all earnings',
                    style: AppTextTheme.cardSubtitle.copyWith(
                      color: AppColors.green,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(width: ResponsiveUtils.spacingXS(context)),
                  Icon(
                    Icons.arrow_forward,
                    color: AppColors.green,
                    size: ResponsiveUtils.iconSize(context, IconSizeType.small),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _ReportRow extends StatelessWidget {
  final String label;
  final int percent;
  final Color color;

  const _ReportRow({
    required this.label,
    required this.percent,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: ResponsiveUtils.spacingM(context),
        vertical: ResponsiveUtils.spacingXS(context) * 0.7, // Reduced vertical padding
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              label,
              style: AppTextTheme.cardSubtitle.copyWith(
                color: color,
                fontWeight: FontWeight.w500,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Text(
            '$percent%',
            style: AppTextTheme.cardSubtitle.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}
