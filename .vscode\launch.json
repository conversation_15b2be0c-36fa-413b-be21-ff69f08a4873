{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Development",
            "request": "launch",
            "type": "dart",
            "program": "lib/main_dev.dart",
            "args": [
                "--flavor",
                "dev",
                "--target",
                "lib/main_dev.dart"
            ],
            "console": "debugConsole"
        },
        {
            "name": "Production",
            "request": "launch",
            "type": "dart",
            "program": "lib/main_prod.dart",
            "args": [
                "--flavor",
                "prod",
                "--target",
                "lib/main_prod.dart"
            ],
            "console": "debugConsole"
        },
        {
            "name": "Development (Release)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release",
            "program": "lib/main_dev.dart",
            "args": [
                "--flavor",
                "dev",
                "--target",
                "lib/main_dev.dart"
            ],
            "console": "debugConsole"
        },
        {
            "name": "Production (Release)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release",
            "program": "lib/main_prod.dart",
            "args": [
                "--flavor",
                "prod",
                "--target",
                "lib/main_prod.dart"
            ],
            "console": "debugConsole"
        },
        {
            "name": "Legacy Main (Deprecated)",
            "program": "lib/main.dart",
            "request": "launch",
            "type": "dart",
            "args": [
                "-t",
                "lib/main.dart"
            ]
        }
    ]
}