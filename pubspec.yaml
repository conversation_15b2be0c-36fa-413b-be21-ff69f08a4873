name: kisankonnect_rider
description: "Kisankonnect rider app"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  intl: ^0.20.2
  change_app_package_name: ^1.5.0
  get: ^4.6.6
  skeletonizer: ^2.0.1
  google_fonts: ^6.1.0

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  carousel_slider: ^5.0.0
  badges: ^3.1.2
  video_player: ^2.10.0
  camera: ^0.10.6
  path_provider: ^2.1.4
  path: ^1.9.0
  image_picker: ^1.1.2
  geolocator: ^13.0.1
  device_preview: ^1.2.0
  geocoding: ^3.0.0
  google_maps_flutter: ^2.5.0
  share_plus: ^10.0.2
  url_launcher: ^6.3.1
  dio: ^5.8.0+1
  device_info_plus: ^10.1.2
  country_code_picker: ^3.0.0
  flutter_dotenv: ^5.1.0
  otp_autofill: ^4.1.0
  local_auth: 2.3.0
  shared_preferences: ^2.3.3
  pinput: ^5.0.0
  chewie: ^1.12.1
  vibration: ^2.0.0
  story_view: ^0.16.5
dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.4 

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Enable generation of localization files
  generate: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/banner/
    - assets/images/pending_assets/
    - assets/images/orders/
    - assets/images/badge/
    - assets/icons/working-time.png
    - assets/icons/timer-frame.png
    - assets/icons/Group.png
    - assets/icons/family-protection.png
    - assets/icons/paper_bag.png
    - assets/icons/
    # Environment configuration files
    - .env
    - .env.development
    - .env.production

  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/images/kk_rider_logo.png"
  min_sdk_android: 21
  adaptive_icon_background: "#4CAF50"
  adaptive_icon_foreground: "assets/images/kk_rider_logo.png"
  remove_alpha_ios: true
