/// Centralized storage keys for the KisanKonnect Rider app
/// This file contains all local storage keys used throughout the application
/// for better organization and to avoid key conflicts.
library;

class StorageKeys {
  // Private constructor to prevent instantiation
  StorageKeys._();

  // ============================================================================
  // AUTHENTICATION KEYS
  // ============================================================================

  /// User profile data (JSON)
  static const String userProfile = 'user_profile';

  /// Authentication token
  static const String authToken = 'auth_token';

  /// Mobile number
  static const String mobileNumber = 'mobile_number';

  /// Stored OTP for verification
  static const String storedOtp = 'stored_otp';

  /// Registration status
  static const String regStatus = 'reg_status';

  /// Approval status
  static const String approvalStatus = 'approval_status';

  // ============================================================================
  // RIDER LOGIN INFO KEYS
  // ============================================================================

  /// Rider ID
  static const String riderId = 'rider_id';

  /// Rider name
  static const String riderName = 'rider_name';

  /// Username
  static const String userName = 'user_name';

  /// Password
  static const String password = 'password';

  /// User type
  static const String userType = 'user_type';

  /// DC ID
  static const String dcId = 'dc_id';

  /// DCID (alternative key)
  static const String dcid = 'dcid';

  /// CDC Type
  static const String cdcType = 'cdctype';

  /// Latitude
  static const String latitude = 'latitude';

  /// Longitude
  static const String longitude = 'longitude';

  /// Application version for SR
  static const String applicationVersionSr = 'application_version_sr';

  /// Area in meters
  static const String areaInMeter = 'area_in_meter';

  /// Download date
  static const String downloadDate = 'download_date';

  /// SR Shift information
  static const String srShift = 'sr_shift';

  // ============================================================================
  // TOKEN KEYS
  // ============================================================================

  /// Dunzo key
  static const String dunzoKey = 'dunzo_key';

  /// Dunzo token
  static const String dunzoToken = 'dunzo_token';

  /// Tata token
  static const String tataToken = 'tata_token';

  /// API token
  static const String apiToken = 'api_token';

  // ============================================================================
  // LOCUS CREDENTIALS
  // ============================================================================

  /// Locus client
  static const String locusClient = 'locus_client';

  /// Locus user
  static const String locusUser = 'locus_user';

  /// Locus password
  static const String locusPassword = 'locus_password';

  // ============================================================================
  // STATUS KEYS
  // ============================================================================

  /// Login status
  static const String loginStatus = 'login_status';

  /// Login status DC
  static const String loginStatusDc = 'login_status_dc';

  /// Image status
  static const String imageStatus = 'image_status';

  /// Store status
  static const String storeStatus = 'store_status';

  /// Order delivery status
  static const String orderDelivery = 'order_delivery';

  /// Order assign status
  static const String orderAssign = 'order_assign';

  /// KFH status
  static const String kfhStatus = 'kfh_status';

  /// KFHID (alternative key)
  static const String kfhid = 'kfhid';

  /// DC status
  static const String dcStatus = 'dc_status';

  /// Document status
  static const String docStatus = 'doc_status';

  // ============================================================================
  // RIDER STATUS KEYS
  // ============================================================================

  /// Rider online/offline status
  static const String riderOnlineStatus = 'rider_online_status';

  /// Rider break status
  static const String riderBreakStatus = 'rider_break_status';

  /// Rider break start time
  static const String riderBreakStartTime = 'rider_break_start_time';

  // ============================================================================
  // PROFILE REGISTRATION KEYS
  // ============================================================================

  /// Profile data (with mobile number suffix)
  static const String profileData = 'profile_data';

  /// Work data (with mobile number suffix)
  static const String workData = 'work_data';

  /// Image paths (with mobile number suffix)
  static const String imagePaths = 'image_paths';

  /// Current step (with mobile number suffix)
  static const String currentStep = 'current_step';

  // ============================================================================
  // CASH BALANCE KEYS
  // ============================================================================

  /// Cash balance data
  static const String cashBalance = 'cash_balance';

  /// Cash balance last updated
  static const String cashBalanceLastUpdated = 'cash_balance_last_updated';

  // ============================================================================
  // EARNINGS KEYS
  // ============================================================================

  /// Weekly earnings data
  static const String weeklyEarnings = 'weekly_earnings';

  /// Daily earnings data
  static const String dailyEarnings = 'daily_earnings';

  /// Monthly earnings data
  static const String monthlyEarnings = 'monthly_earnings';

  // ============================================================================
  // RIDER DETAILS KEYS
  // ============================================================================

  /// Complete rider details from FE_GetRiderDetails API
  static const String riderDetails = 'rider_details';

  /// Rider details last updated timestamp
  static const String riderDetailsLastUpdated = 'rider_details_last_updated';

  // ============================================================================
  // DASHBOARD KEYS
  // ============================================================================

  /// Dashboard story banner data
  static const String dashboardStoryBanner = 'dashboard_story_banner';

  /// Dashboard last refresh time
  static const String dashboardLastRefresh = 'dashboard_last_refresh';

  // ============================================================================
  // REPORTS KEYS
  // ============================================================================

  /// Reports data
  static const String reportsData = 'reports_data';

  /// Reports last updated
  static const String reportsLastUpdated = 'reports_last_updated';

  // ============================================================================
  // APP PREFERENCES KEYS
  // ============================================================================

  /// Biometric authentication enabled
  static const String biometricEnabled = 'biometric_enabled';

  /// Selected language
  static const String selectedLanguage = 'selected_language';

  /// Notifications enabled
  static const String notificationsEnabled = 'notifications_enabled';

  /// Current address
  static const String currentAddress = 'current_address';

  /// Rider profile data
  static const String riderProfile = 'rider_profile';

  // ============================================================================
  // HELPER METHODS
  // ============================================================================

  /// Get profile data key with mobile number suffix
  static String getProfileDataKey(String mobileNumber) => '${profileData}_$mobileNumber';

  /// Get work data key with mobile number suffix
  static String getWorkDataKey(String mobileNumber) => '${workData}_$mobileNumber';

  /// Get image paths key with mobile number suffix
  static String getImagePathsKey(String mobileNumber) => '${imagePaths}_$mobileNumber';

  /// Get current step key with mobile number suffix
  static String getCurrentStepKey(String mobileNumber) => '${currentStep}_$mobileNumber';

  // ============================================================================
  // ALL KEYS LIST (for cleanup operations)
  // ============================================================================

  /// List of all authentication related keys
  static const List<String> authKeys = [
    userProfile,
    authToken,
    mobileNumber,
    storedOtp,
    regStatus,
    approvalStatus,
  ];

  /// List of all rider login info keys
  static const List<String> riderLoginKeys = [
    riderId,
    riderName,
    userName,
    password,
    userType,
    dcId,
    dcid,
    cdcType,
    latitude,
    longitude,
    applicationVersionSr,
    areaInMeter,
    downloadDate,
  ];

  /// List of all token keys
  static const List<String> tokenKeys = [
    dunzoKey,
    dunzoToken,
    tataToken,
    apiToken,
  ];

  /// List of all locus credential keys
  static const List<String> locusKeys = [
    locusClient,
    locusUser,
    locusPassword,
  ];

  /// List of all status keys
  static const List<String> statusKeys = [
    loginStatus,
    loginStatusDc,
    imageStatus,
    storeStatus,
    orderDelivery,
    orderAssign,
    kfhStatus,
    kfhid,
    dcStatus,
    docStatus,
  ];

  /// List of all rider status keys
  static const List<String> riderStatusKeys = [
    riderOnlineStatus,
    riderBreakStatus,
    riderBreakStartTime,
  ];

  /// List of all keys that should be cleared on logout
  static const List<String> logoutClearKeys = [
    ...authKeys,
    ...riderLoginKeys,
    ...tokenKeys,
    ...locusKeys,
    ...statusKeys,
    ...riderStatusKeys,
    riderDetails,
    riderDetailsLastUpdated,
    cashBalance,
    cashBalanceLastUpdated,
    weeklyEarnings,
    dailyEarnings,
    monthlyEarnings,
    dashboardStoryBanner,
    dashboardLastRefresh,
    reportsData,
    reportsLastUpdated,
  ];
}
