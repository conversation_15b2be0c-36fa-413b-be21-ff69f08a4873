import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import '../../../config/env_config.dart';
import 'address_details_dialog.dart';
import '../loading/elegant_shimmer.dart';

class GoogleMapsLocationPicker extends StatefulWidget {
  final Function(Map<String, dynamic>)? onLocationSelected;

  const GoogleMapsLocationPicker({
    super.key,
    this.onLocationSelected,
  });

  static Future<Map<String, dynamic>?> show({
    required BuildContext context,
    Function(Map<String, dynamic>)? onLocationSelected,
  }) async {
    return await showModalBottomSheet<Map<String, dynamic>>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => GoogleMapsLocationPicker(
        onLocationSelected: onLocationSelected,
      ),
    );
  }

  @override
  State<GoogleMapsLocationPicker> createState() => _GoogleMapsLocationPickerState();
}

class _GoogleMapsLocationPickerState extends State<GoogleMapsLocationPicker> {
  GoogleMapController? _mapController;
  LatLng _currentPosition = const LatLng(19.0760, 72.8777); // Default Mumbai
  LatLng _selectedPosition = const LatLng(19.0760, 72.8777);
  String _selectedAddress = '';
  bool _isLoading = true;
  bool _isLoadingAddress = false;
  final Set<Marker> _markers = {};

  @override
  void initState() {
    super.initState();
    debugPrint('🗺️ GoogleMapsLocationPicker initialized');
    debugPrint('🗺️ Google Maps API Key available: ${MapsService.instance.isApiKeyValid}');
    _getCurrentLocation();
  }

  Future<void> _getCurrentLocation() async {
    try {
      setState(() => _isLoading = true);

      // Check location permission
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          _showPermissionDialog();
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _showPermissionDialog();
        return;
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
        ),
      );

      _currentPosition = LatLng(position.latitude, position.longitude);
      _selectedPosition = _currentPosition;

      // Get address for current location
      await _getAddressFromCoordinates(_currentPosition);

      // Update map and marker
      _updateMarker(_selectedPosition);

      if (_mapController != null) {
        _mapController!.animateCamera(
          CameraUpdate.newLatLngZoom(_currentPosition, 16.0),
        );
      }
    } catch (e) {
      debugPrint('Error getting current location: $e');
      // Use default location (Mumbai)
      await _getAddressFromCoordinates(_currentPosition);
      _updateMarker(_selectedPosition);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _getAddressFromCoordinates(LatLng position) async {
    try {
      setState(() => _isLoadingAddress = true);

      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        _selectedAddress = [
          place.name,
          place.subLocality,
          place.locality,
          place.administrativeArea,
          place.postalCode,
        ].where((element) => element != null && element.isNotEmpty).join(', ');
      }
    } catch (e) {
      debugPrint('Error getting address: $e');
      _selectedAddress = 'Selected Location';
    } finally {
      setState(() => _isLoadingAddress = false);
    }
  }

  void _updateMarker(LatLng position) {
    setState(() {
      _markers.clear();
      _markers.add(
        Marker(
          markerId: const MarkerId('selected_location'),
          position: position,
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
          infoWindow: InfoWindow(
            title: 'Selected Location',
            snippet: _selectedAddress,
          ),
        ),
      );
    });
  }

  void _onMapTap(LatLng position) {
    _selectedPosition = position;
    _updateMarker(position);
    _getAddressFromCoordinates(position);
  }

  void _showPermissionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Location Permission Required'),
        content: const Text(
          'This app needs location permission to show your current location on the map. Please enable location permission in settings.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Geolocator.openAppSettings();
            },
            child: const Text('Settings'),
          ),
        ],
      ),
    );
  }

  Future<void> _confirmLocation() async {
    if (_selectedAddress.isNotEmpty) {
      // Show address details dialog
      final addressDetails = await AddressDetailsDialog.show(
        context: context,
        selectedLocation: _selectedAddress.split(',').first,
        selectedAddress: _selectedAddress,
      );

      if (addressDetails != null) {
        // Combine location data with address details
        final completeLocationData = {
          'address': _selectedAddress,
          'latitude': _selectedPosition.latitude.toString(),
          'longitude': _selectedPosition.longitude.toString(),
          'societyName': addressDetails['societyName'] ?? '',
          'flatDetails': addressDetails['flatDetails'] ?? '',
          'nearestArea': addressDetails['nearestArea'] ?? '',
          'landmark': addressDetails['landmark'] ?? '',
        };

        if (mounted) {
          Navigator.of(context).pop(completeLocationData);
          widget.onLocationSelected?.call(completeLocationData);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.green,
              borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Row(
              children: [
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close, color: Colors.white),
                ),
                const Expanded(
                  child: Text(
                    'Select Location',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                IconButton(
                  onPressed: _getCurrentLocation,
                  icon: const Icon(Icons.my_location, color: Colors.white),
                ),
              ],
            ),
          ),

          // Map
          Expanded(
            child: _isLoading
                ? Center(
                    child: ElegantShimmer(
                      child: Container(
                        margin: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade200,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            ElegantShimmerBox(
                              width: 80,
                              height: 80,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            const SizedBox(height: 16),
                            ElegantShimmerLine(width: 150, height: 16),
                            const SizedBox(height: 8),
                            ElegantShimmerLine(width: 100, height: 12),
                          ],
                        ),
                      ),
                    ),
                  )
                : GoogleMap(
                    onMapCreated: (GoogleMapController controller) {
                      _mapController = controller;
                      debugPrint('🗺️ Google Map created successfully');
                    },
                    initialCameraPosition: CameraPosition(
                      target: _currentPosition,
                      zoom: 16.0,
                    ),
                    onTap: _onMapTap,
                    markers: _markers,
                    myLocationEnabled: true,
                    myLocationButtonEnabled: false,
                    zoomControlsEnabled: false,
                    mapToolbarEnabled: false,
                    onCameraMove: (position) {
                      debugPrint('🗺️ Camera moved to: ${position.target}');
                    },
                  ),
          ),

          // Address display and confirm button
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Selected Address:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 8),
                _isLoadingAddress
                    ? const Row(
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: AppColors.green,
                            ),
                          ),
                          SizedBox(width: 8),
                          Text('Getting address...'),
                        ],
                      )
                    : Text(
                        _selectedAddress.isNotEmpty ? _selectedAddress : 'Tap on map to select location',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                const SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _selectedAddress.isNotEmpty ? _confirmLocation : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Confirm Location',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
