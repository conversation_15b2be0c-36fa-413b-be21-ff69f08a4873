import 'package:flutter/material.dart';

import 'package:get/get.dart';
import '../../../controllers/auth_controller.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import '../../../constants/storage_keys.dart';

class DeliveryPartnerWelcomeScreen extends StatefulWidget {
  const DeliveryPartnerWelcomeScreen({super.key});

  @override
  State<DeliveryPartnerWelcomeScreen> createState() => _DeliveryPartnerWelcomeScreenState();
}

class _DeliveryPartnerWelcomeScreenState extends State<DeliveryPartnerWelcomeScreen> {
  final PageController _pageController = PageController();
  final SecureStorageService _storage = SecureStorageService.instance;
  int _currentPage = 0;
  int _regStatus = 0;
  int _approvalStatus = 0;

  final List<Map<String, String>> _testimonials = [
    {
      'quote':
          '"Great service by <PERSON><PERSON> Konnect rider! Prompt, courteous, and careful with my delivery. Highly recommend!"',
      'author': 'Rider name - Location name',
      'image': 'assets/images/rider_image.png',
    },
    {
      'quote': '"Excellent delivery experience! The rider was professional and delivered on time. Very satisfied!"',
      'author': 'Customer name - Location name',
      'image': 'assets/images/rider_image.png',
    },
    {
      'quote': '"Amazing service quality! Fast delivery and great communication. Will definitely use again!"',
      'author': 'User name - Location name',
      'image': 'assets/images/rider_image.png',
    },
  ];

  @override
  void initState() {
    super.initState();
    _loadRegistrationStatus();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  /// Load registration status from storage
  Future<void> _loadRegistrationStatus() async {
    try {
      // Get regStatus and approvalStatus from direct storage
      final regStatusStr = await _storage.read(StorageKeys.regStatus);
      final approvalStatusStr = await _storage.read(StorageKeys.approvalStatus);

      setState(() {
        _regStatus = regStatusStr != null ? int.tryParse(regStatusStr) ?? 0 : 0;
        _approvalStatus = approvalStatusStr != null ? int.tryParse(approvalStatusStr) ?? 0 : 0;
      });

      debugPrint('📋 Loaded registration status: regStatus=$_regStatus, approvalStatus=$_approvalStatus');
    } catch (e) {
      debugPrint('🚨 Error loading registration status: $e');
    }
  }

  /// Handle back to login action
  void _handleBackToLogin() {
    // Show confirmation dialog
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Back to Login'),
          content: const Text(
              'Are you sure you want to go back to login? You will need to enter your mobile number and OTP again.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Logout and navigate to login
                final authController = Get.find<AuthController>();
                authController.logout();
              },
              child: const Text('Confirm'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      body: SafeArea(
        child: Column(
          children: [
            // Header Section with Testimonials
            Container(
              height: 260,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Color(0xFF4CAF50),
                    Color(0xFF2E7D32),
                  ],
                ),
              ),
              child: Column(
                children: [
                  // Title
                  Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Welcome to KisanKonnect',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 4),
                        const Text(
                          'delivery partner',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.w500,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Testimonial Carousel
                  Expanded(
                    child: Container(
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                      child: PageView.builder(
                        controller: _pageController,
                        onPageChanged: (index) {
                          setState(() {
                            _currentPage = index;
                          });
                        },
                        itemCount: _testimonials.length,
                        itemBuilder: (context, index) {
                          return _buildTestimonialCard(_testimonials[index]);
                        },
                      ),
                    ),
                  ),

                  // Page Indicators
                  Padding(
                    padding: const EdgeInsets.only(bottom: 20.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: _currentPage == 0 ? Colors.white : Colors.white.withValues(alpha: 0.5),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '${_currentPage + 1}/${_testimonials.length}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            fontFamily: 'Figtree',
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: _currentPage == _testimonials.length - 1
                                ? Colors.white
                                : Colors.white.withValues(alpha: 0.5),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Steps Section
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Become a delivery partner in 3 easy steps',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Step 1: Profile
                    _buildStepCard(
                      stepNumber: 1,
                      title: 'Profile',
                      status: _getStep1Status(),
                      statusColor: _getStep1StatusColor(),
                      icon: Icons.person_outline,
                      iconColor: _getStep1IconColor(),
                    ),
                    const SizedBox(height: 16),

                    // Step 2: Document upload
                    _buildStepCard(
                      stepNumber: 2,
                      title: 'Document upload',
                      status: _getStep2Status(),
                      statusColor: _getStep2StatusColor(),
                      icon: Icons.description_outlined,
                      iconColor: _getStep2IconColor(),
                    ),
                    const SizedBox(height: 16),

                    // Step 3: Work details
                    _buildStepCard(
                      stepNumber: 3,
                      title: 'Work details',
                      status: _getStep3Status(),
                      statusColor: _getStep3StatusColor(),
                      icon: Icons.work_outline,
                      iconColor: _getStep3IconColor(),
                    ),
                    const SizedBox(height: 16),

                    // Step 4: Back to Login
                    _buildStepCard(
                      stepNumber: 4,
                      title: 'Back to Login',
                      status: 'Available',
                      statusColor: Colors.blue,
                      icon: Icons.login,
                      iconColor: Colors.blue,
                      onTap: _handleBackToLogin,
                    ),

                    const SizedBox(height: 40),

                    // Bottom Section
                    Row(
                      children: [
                        const Text(
                          'This',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                        const Spacer(),
                        Row(
                          children: [
                            const Icon(
                              Icons.phone,
                              size: 16,
                              color: Colors.grey,
                            ),
                            const SizedBox(width: 4),
                            const Text(
                              'Customer care',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 24), // Extra bottom padding
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Get Step 1 (Profile) status based on regStatus
  String _getStep1Status() {
    if (_regStatus >= 1) {
      return 'Completed';
    }
    return 'Pending';
  }

  Color _getStep1StatusColor() {
    if (_regStatus >= 1) {
      return AppColors.green;
    }
    return Colors.grey;
  }

  Color _getStep1IconColor() {
    if (_regStatus >= 1) {
      return Colors.blue;
    }
    return Colors.grey;
  }

  /// Get Step 2 (Document upload) status based on regStatus and approvalStatus
  String _getStep2Status() {
    if (_regStatus >= 2) {
      // Documents uploaded, check approval status
      switch (_approvalStatus) {
        case 0:
          return 'Not verified';
        case 1:
          return 'Verified';
        case 2:
          return 'Rejected';
        default:
          return 'Verifying';
      }
    } else if (_regStatus == 1) {
      return 'Verifying';
    }
    return 'Pending';
  }

  Color _getStep2StatusColor() {
    if (_regStatus >= 2) {
      // Documents uploaded, check approval status
      switch (_approvalStatus) {
        case 0:
          return Colors.orange;
        case 1:
          return AppColors.green;
        case 2:
          return Colors.red;
        default:
          return Colors.orange;
      }
    } else if (_regStatus == 1) {
      return Colors.orange;
    }
    return Colors.grey;
  }

  Color _getStep2IconColor() {
    if (_regStatus >= 2) {
      // Documents uploaded, check approval status
      switch (_approvalStatus) {
        case 0:
          return Colors.orange;
        case 1:
          return Colors.blue;
        case 2:
          return Colors.red;
        default:
          return Colors.orange;
      }
    } else if (_regStatus == 1) {
      return Colors.orange;
    }
    return Colors.grey;
  }

  String _getStep3Status() {
    if (_regStatus == 3) {
      // Work details completed, check approval status
      switch (_approvalStatus) {
        case 0:
          return 'Not verified';
        case 1:
          return 'Verified';
        case 2:
          return 'Rejected';
        default:
          return 'Pending';
      }
    } else if (_regStatus == 2) {
      return 'In progress';
    }
    return 'Pending';
  }

  Color _getStep3StatusColor() {
    if (_regStatus == 3) {
      switch (_approvalStatus) {
        case 0:
          return Colors.orange;
        case 1:
          return AppColors.green;
        case 2:
          return Colors.red;
        default:
          return Colors.grey;
      }
    } else if (_regStatus == 2) {
      return Colors.blue;
    }
    return Colors.grey;
  }

  Color _getStep3IconColor() {
    if (_regStatus == 3) {
      switch (_approvalStatus) {
        case 0:
          return Colors.orange;
        case 1:
          return Colors.blue;
        case 2:
          return Colors.red;
        default:
          return Colors.grey;
      }
    } else if (_regStatus == 2) {
      return Colors.blue;
    }
    return Colors.grey;
  }

  Widget _buildTestimonialCard(Map<String, String> testimonial) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8),
      padding: const EdgeInsets.all(16),
      constraints: const BoxConstraints(
        maxHeight: 120, // Limit the height of testimonial cards
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Profile Image
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              image: DecorationImage(
                image: AssetImage(testimonial['image']!),
                fit: BoxFit.cover,
                onError: (exception, stackTrace) {
                  // Fallback to default avatar
                },
              ),
            ),
            child: testimonial['image']!.isEmpty ? const Icon(Icons.person, size: 30, color: Colors.grey) : null,
          ),
          const SizedBox(width: 12),

          // Quote and Author
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Flexible(
                  child: Text(
                    testimonial['quote']!,
                    style: AppTextTheme.cardSubtitle.copyWith(
                      height: 1.3,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  testimonial['author']!,
                  style: AppTextTheme.cardCaption.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepCard({
    required int stepNumber,
    required String title,
    required String status,
    required Color statusColor,
    required IconData icon,
    required Color iconColor,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Step Number Circle
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: status == 'Completed' ? AppColors.green : Colors.grey[300],
                shape: BoxShape.circle,
              ),
              child: Center(
                child: status == 'Completed'
                    ? const Icon(Icons.check, color: Colors.white, size: 18)
                    : Text(
                        stepNumber.toString(),
                        style: const TextStyle(
                          color: Colors.grey,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
              ),
            ),
            const SizedBox(width: 16),

            // Title and Status
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Step $stepNumber',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      status,
                      style: TextStyle(
                        fontSize: 12,
                        color: statusColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Icon
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: iconColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: iconColor,
                size: 24,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
