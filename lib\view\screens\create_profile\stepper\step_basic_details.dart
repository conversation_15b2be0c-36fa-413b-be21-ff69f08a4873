import 'package:flutter/material.dart';

import 'package:kisankonnect_rider/services/all_services.dart';
import '../../../widgets/forms/common_text_field.dart';

class StepBasicDetails extends StatefulWidget {
  final Function(Map<String, dynamic>)? onContinue;
  final VoidCallback? onBack;
  const StepBasicDetails({super.key, this.onContinue, this.onBack});

  @override
  State<StepBasicDetails> createState() => _StepBasicDetailsState();
}

class _StepBasicDetailsState extends State<StepBasicDetails> {
  final _formKey = GlobalKey<FormState>();
  final _scrollController = ScrollController();
  final _storage = SecureStorageService.instance;

  // Form controllers
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _mobileController = TextEditingController();
  final _dobController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadMobileNumber();
  }

  /// Load mobile number from storage and populate the field
  Future<void> _loadMobileNumber() async {
    try {
      final mobileNumber = await _storage.read(StorageKeys.mobileNumber);
      if (mobileNumber != null && mobileNumber.isNotEmpty) {
        setState(() {
          _mobileController.text = mobileNumber;
        });
        debugPrint('📱 Mobile number loaded from storage: $mobileNumber');
      } else {
        debugPrint('📱 No mobile number found in storage');
      }
    } catch (e) {
      debugPrint('🚨 Error loading mobile number: $e');
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _mobileController.dispose();
    _dobController.dispose();
    super.dispose();
  }

  Future<void> _pickDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: DateTime(2000, 1, 1),
      firstDate: DateTime(1950),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      final formattedDate =
          "${picked.day.toString().padLeft(2, '0')}-${picked.month.toString().padLeft(2, '0')}-${picked.year}";
      _dobController.text = formattedDate;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xFFF7F8FA),
      child: Column(
        children: [
          Expanded(
            child: SafeArea(
              child: SingleChildScrollView(
                controller: _scrollController,
                padding: const EdgeInsets.all(16),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 16),
                      Text('Enter your basic details', style: AppTextTheme.cardTitle.copyWith(fontSize: 18)),
                      const SizedBox(height: 4),
                      Text('Enter your info has mention on Aadhar card', style: AppTextTheme.cardSubtitle),
                      const SizedBox(height: 24),
                      CommonTextField(
                        controller: _firstNameController,
                        hint: 'First Name*',
                        keyboardType: TextInputType.name,
                        validator: (value) => value?.isEmpty == true ? 'First name is required' : null,
                      ),
                      const SizedBox(height: 16),
                      CommonTextField(
                        controller: _lastNameController,
                        hint: 'Last Name*',
                        keyboardType: TextInputType.name,
                        validator: (value) => value?.isEmpty == true ? 'Last name is required' : null,
                      ),
                      const SizedBox(height: 16),
                      CommonTextField(
                        controller: _mobileController,
                        hint: 'Mobile Number*',
                        keyboardType: TextInputType.phone,
                        readOnly: true,
                        validator: (value) => value?.isEmpty == true ? 'Mobile number is required' : null,
                      ),
                      const SizedBox(height: 16),
                      CommonTextField(
                        controller: _emailController,
                        hint: 'Email ID',
                        keyboardType: TextInputType.emailAddress,
                        validator: (value) {
                          if (value != null && value.isNotEmpty) {
                            if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                              return 'Enter a valid email address';
                            }
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      GestureDetector(
                        onTap: _pickDate,
                        child: AbsorbPointer(
                          child: CommonTextField(
                            controller: _dobController,
                            hint: 'Date of Birth* (DD-MM-YYYY)',
                            validator: (value) => value?.isEmpty == true ? 'Date of birth is required' : null,
                          ),
                        ),
                      ),
                      const SizedBox(height: 32),
                    ],
                  ),
                ),
              ),
            ),
          ),
          // Continue button
          Container(
            padding: EdgeInsets.only(
              left: 16,
              right: 16,
              bottom: MediaQuery.of(context).viewInsets.bottom + 16,
              top: 16,
            ),
            decoration: const BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black12,
                  blurRadius: 4,
                  offset: Offset(0, -2),
                ),
              ],
            ),
            child: SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: () {
                  if (_formKey.currentState!.validate()) {
                    // Collect form data
                    final data = {
                      'firstName': _firstNameController.text,
                      'lastName': _lastNameController.text,
                      'mobileNumber': _mobileController.text,
                      'email': _emailController.text,
                      'dob': _dobController.text,
                    };

                    widget.onContinue?.call(data);
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.green,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(32),
                  ),
                ),
                child: const Text(
                  'Continue',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
