class EarningsTrip {
  final String tripId;
  final String tripName;
  final int orderCount;
  final double amount;
  final DateTime date;
  final bool isSuccess;

  EarningsTrip({
    required this.tripId,
    required this.tripName,
    required this.orderCount,
    required this.amount,
    required this.date,
    this.isSuccess = true,
  });

  factory EarningsTrip.fromJson(Map<String, dynamic> json) {
    return EarningsTrip(
      tripId: json['tripId'] ?? '',
      tripName: json['tripName'] ?? '',
      orderCount: json['orderCount'] ?? 0,
      amount: (json['amount'] ?? 0).toDouble(),
      date: DateTime.parse(json['date']),
      isSuccess: json['isSuccess'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'tripId': tripId,
      'tripName': tripName,
      'orderCount': orderCount,
      'amount': amount,
      'date': date.toIso8601String(),
      'isSuccess': isSuccess,
    };
  }
}

class EarningsDeduction {
  final String id;
  final String title;
  final String remark;
  final double amount;
  final DateTime date;

  EarningsDeduction({
    required this.id,
    required this.title,
    required this.remark,
    required this.amount,
    required this.date,
  });

  factory EarningsDeduction.fromJson(Map<String, dynamic> json) {
    return EarningsDeduction(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      remark: json['remark'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      date: DateTime.parse(json['date']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'remark': remark,
      'amount': amount,
      'date': date.toIso8601String(),
    };
  }
}

class DailyEarnings {
  final DateTime date;
  final double amount;
  final bool hasEarnings;

  DailyEarnings({
    required this.date,
    required this.amount,
    this.hasEarnings = false,
  });

  factory DailyEarnings.fromJson(Map<String, dynamic> json) {
    return DailyEarnings(
      date: DateTime.parse(json['date']),
      amount: (json['amount'] ?? 0).toDouble(),
      hasEarnings: json['hasEarnings'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'amount': amount,
      'hasEarnings': hasEarnings,
    };
  }
}

class WeeklyEarnings {
  final int weekNumber;
  final DateTime startDate;
  final DateTime endDate;
  final List<DailyEarnings> dailyEarnings;
  final List<EarningsTrip> trips;
  final List<EarningsDeduction> deductions;
  final double totalEarnings;
  final double todaysEarn;
  final int ordersCompleted;

  WeeklyEarnings({
    required this.weekNumber,
    required this.startDate,
    required this.endDate,
    required this.dailyEarnings,
    required this.trips,
    required this.deductions,
    required this.totalEarnings,
    required this.todaysEarn,
    required this.ordersCompleted,
  });

  factory WeeklyEarnings.fromJson(Map<String, dynamic> json) {
    return WeeklyEarnings(
      weekNumber: json['weekNumber'] ?? 0,
      startDate: DateTime.parse(json['startDate']),
      endDate: DateTime.parse(json['endDate']),
      dailyEarnings: (json['dailyEarnings'] as List<dynamic>?)?.map((e) => DailyEarnings.fromJson(e)).toList() ?? [],
      trips: (json['trips'] as List<dynamic>?)?.map((e) => EarningsTrip.fromJson(e)).toList() ?? [],
      deductions: (json['deductions'] as List<dynamic>?)?.map((e) => EarningsDeduction.fromJson(e)).toList() ?? [],
      totalEarnings: (json['totalEarnings'] ?? 0).toDouble(),
      todaysEarn: (json['todaysEarn'] ?? 0).toDouble(),
      ordersCompleted: json['ordersCompleted'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'weekNumber': weekNumber,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'dailyEarnings': dailyEarnings.map((e) => e.toJson()).toList(),
      'trips': trips.map((e) => e.toJson()).toList(),
      'deductions': deductions.map((e) => e.toJson()).toList(),
      'totalEarnings': totalEarnings,
      'todaysEarn': todaysEarn,
      'ordersCompleted': ordersCompleted,
    };
  }
}

enum EarningsFilterType {
  tripAscending,
  tripDescending,
  onlySuccess,
  onlyFailed,
}

class EarningsFilter {
  final EarningsFilterType type;
  final String displayName;

  const EarningsFilter({
    required this.type,
    required this.displayName,
  });

  static const List<EarningsFilter> allFilters = [
    EarningsFilter(
      type: EarningsFilterType.tripAscending,
      displayName: 'Trip in Ascending order',
    ),
    EarningsFilter(
      type: EarningsFilterType.tripDescending,
      displayName: 'Trip in Descending order',
    ),
    EarningsFilter(
      type: EarningsFilterType.onlySuccess,
      displayName: 'Only Success Trip',
    ),
    EarningsFilter(
      type: EarningsFilterType.onlyFailed,
      displayName: 'Only Failed Trip',
    ),
  ];
}
