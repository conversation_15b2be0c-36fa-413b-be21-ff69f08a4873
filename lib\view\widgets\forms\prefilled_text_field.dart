import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../controllers/profile_controller.dart';
import '../../../utils/responsive_utils.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

class PrefilledTextField extends StatefulWidget {
  final String dataKey;
  final String label;
  final String? hint;
  final TextInputType? keyboardType;
  final bool isRequired;
  final int? maxLines;
  final Function(String)? onChanged;
  final String? Function(String?)? validator;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final bool enabled;

  const PrefilledTextField({
    super.key,
    required this.dataKey,
    required this.label,
    this.hint,
    this.keyboardType,
    this.isRequired = false,
    this.maxLines = 1,
    this.onChanged,
    this.validator,
    this.prefixIcon,
    this.suffixIcon,
    this.enabled = true,
  });

  @override
  State<PrefilledTextField> createState() => _PrefilledTextFieldState();
}

class _PrefilledTextFieldState extends State<PrefilledTextField> {
  late TextEditingController _controller;
  late ProfileController _profileController;

  @override
  void initState() {
    super.initState();

    // Initialize ProfileController
    try {
      _profileController = Get.find<ProfileController>();
    } catch (e) {
      debugPrint('🚨 ProfileController not found in PrefilledTextField, creating new instance: $e');
      _profileController = Get.put(ProfileController());
    }

    // Initialize controller with prefilled data
    final prefilledValue = _profileController.getPrefilledValue(widget.dataKey);
    _controller = TextEditingController(text: prefilledValue);

    // Add listener to save data on change
    _controller.addListener(() {
      final value = _controller.text;
      _profileController.updateProfileData(widget.dataKey, value);
      widget.onChanged?.call(value);
    });

    debugPrint('🔄 PrefilledTextField initialized for ${widget.dataKey} with value: $prefilledValue');
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final borderRadius = ResponsiveUtils.borderRadius(context, BorderRadiusType.small);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        RichText(
          text: TextSpan(
            text: widget.label,
            style: AppTextTheme.cardSubtitle.copyWith(
              fontSize: AppTextTheme.getResponsiveFontSize(context, 14),
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
            children: widget.isRequired
                ? [
                    TextSpan(
                      text: ' *',
                      style: AppTextTheme.cardSubtitle.copyWith(
                        color: AppColors.error,
                        fontSize: AppTextTheme.getResponsiveFontSize(context, 14),
                      ),
                    ),
                  ]
                : null,
          ),
        ),

        SizedBox(height: ResponsiveUtils.spacingS(context)),

        // Text Field
        TextFormField(
          controller: _controller,
          keyboardType: widget.keyboardType,
          maxLines: widget.maxLines,
          enabled: widget.enabled,
          style: AppTextTheme.cardSubtitle.copyWith(
            fontSize: AppTextTheme.getResponsiveFontSize(context, 14),
            color: widget.enabled ? AppColors.textPrimary : AppColors.textSecondary,
          ),
          decoration: InputDecoration(
            hintText: widget.hint ?? 'Enter ${widget.label.toLowerCase()}',
            hintStyle: AppTextTheme.cardSubtitle.copyWith(
              fontSize: AppTextTheme.getResponsiveFontSize(context, 14),
              color: AppColors.textSecondary,
            ),
            prefixIcon: widget.prefixIcon,
            suffixIcon: widget.suffixIcon,
            filled: true,
            fillColor: widget.enabled ? AppColors.cardBackground : AppColors.cardBackground.withValues(alpha: 0.5),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius),
              borderSide: BorderSide(color: AppColors.borderLight, width: 1),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius),
              borderSide: BorderSide(color: AppColors.borderLight, width: 1),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius),
              borderSide: BorderSide(color: AppColors.green, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius),
              borderSide: BorderSide(color: AppColors.error, width: 1),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius),
              borderSide: BorderSide(color: AppColors.error, width: 2),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: ResponsiveUtils.spacingM(context),
              vertical: ResponsiveUtils.spacingS(context),
            ),
          ),
          validator: widget.validator ??
              (widget.isRequired
                  ? (value) {
                      if (value == null || value.trim().isEmpty) {
                        return '${widget.label} is required';
                      }
                      return null;
                    }
                  : null),
        ),
      ],
    );
  }
}

/// Prefilled dropdown field for selections
class PrefilledDropdownField<T> extends StatefulWidget {
  final String dataKey;
  final String label;
  final List<DropdownMenuItem<T>> items;
  final bool isRequired;
  final Function(T?)? onChanged;
  final String? Function(T?)? validator;

  const PrefilledDropdownField({
    super.key,
    required this.dataKey,
    required this.label,
    required this.items,
    this.isRequired = false,
    this.onChanged,
    this.validator,
  });

  @override
  State<PrefilledDropdownField<T>> createState() => _PrefilledDropdownFieldState<T>();
}

class _PrefilledDropdownFieldState<T> extends State<PrefilledDropdownField<T>> {
  late ProfileController _profileController;
  T? _selectedValue;

  @override
  void initState() {
    super.initState();

    // Initialize ProfileController
    try {
      _profileController = Get.find<ProfileController>();
    } catch (e) {
      debugPrint('🚨 ProfileController not found in PrefilledDropdownField, creating new instance: $e');
      _profileController = Get.put(ProfileController());
    }

    // Initialize with prefilled data
    final prefilledValue = _profileController.getPrefilledValue(widget.dataKey);
    if (prefilledValue.isNotEmpty) {
      // Try to find matching value in items
      for (final item in widget.items) {
        if (item.value.toString() == prefilledValue) {
          _selectedValue = item.value;
          break;
        }
      }
    }

    debugPrint('🔄 PrefilledDropdownField initialized for ${widget.dataKey} with value: $_selectedValue');
  }

  @override
  Widget build(BuildContext context) {
    final borderRadius = ResponsiveUtils.borderRadius(context, BorderRadiusType.small);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        RichText(
          text: TextSpan(
            text: widget.label,
            style: AppTextTheme.cardSubtitle.copyWith(
              fontSize: AppTextTheme.getResponsiveFontSize(context, 14),
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
            children: widget.isRequired
                ? [
                    TextSpan(
                      text: ' *',
                      style: AppTextTheme.cardSubtitle.copyWith(
                        color: AppColors.error,
                        fontSize: AppTextTheme.getResponsiveFontSize(context, 14),
                      ),
                    ),
                  ]
                : null,
          ),
        ),

        SizedBox(height: ResponsiveUtils.spacingS(context)),

        // Dropdown Field
        DropdownButtonFormField<T>(
          value: _selectedValue,
          items: widget.items,
          onChanged: (value) {
            setState(() => _selectedValue = value);
            _profileController.updateProfileData(widget.dataKey, value);
            widget.onChanged?.call(value);
          },
          style: AppTextTheme.cardSubtitle.copyWith(
            fontSize: AppTextTheme.getResponsiveFontSize(context, 14),
            color: AppColors.textPrimary,
          ),
          decoration: InputDecoration(
            filled: true,
            fillColor: AppColors.cardBackground,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius),
              borderSide: BorderSide(color: AppColors.borderLight, width: 1),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius),
              borderSide: BorderSide(color: AppColors.borderLight, width: 1),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius),
              borderSide: BorderSide(color: AppColors.green, width: 2),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: ResponsiveUtils.spacingM(context),
              vertical: ResponsiveUtils.spacingS(context),
            ),
          ),
          validator: widget.validator ??
              (widget.isRequired
                  ? (value) {
                      if (value == null) {
                        return '${widget.label} is required';
                      }
                      return null;
                    }
                  : null),
        ),
      ],
    );
  }
}
