import 'package:flutter/material.dart';
import '../../widgets/common/common_elevated_button.dart';
import '../../widgets/forms/country_code_picker_widget.dart';
import 'package:get/get.dart';
import '../../../controllers/auth_controller.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:country_code_picker/country_code_picker.dart';

class EnterNumberScreen extends StatefulWidget {
  const EnterNumberScreen({super.key});

  @override
  State<EnterNumberScreen> createState() => _EnterNumberScreenState();
}

class _EnterNumberScreenState extends State<EnterNumberScreen> {
  final TextEditingController _phoneController = TextEditingController();
  final AuthController _authController = Get.find<AuthController>();
  bool isLoading = false;

  // Country code picker variables
  CountryCode selectedCountryCode = CountryCode.fromCountryCode('IN');
  bool isAutoDetecting = true;

  @override
  void initState() {
    super.initState();
    // Add listener for phone number changes to auto-detect country
    _phoneController.addListener(_onPhoneNumberChanged);
  }

  @override
  void dispose() {
    _phoneController.dispose();
    super.dispose();
  }

  void _onPhoneNumberChanged() {
    // Disable aggressive auto-detection to prevent interference with user input
    // Only detect when user has entered a substantial number
    if (!isAutoDetecting) return;

    final phoneNumber = _phoneController.text;
    if (phoneNumber.isEmpty || phoneNumber.length < 3) return;

    // Only auto-detect for longer numbers to avoid premature formatting
    if (phoneNumber.length >= 10) {
      if (phoneNumber.startsWith('91') || phoneNumber.startsWith('0')) {
        _updateCountryCode('IN');
      } else if (phoneNumber.startsWith('1')) {
        _updateCountryCode('US');
      } else if (phoneNumber.startsWith('44')) {
        _updateCountryCode('GB');
      } else if (phoneNumber.startsWith('86')) {
        _updateCountryCode('CN');
      }
    }
  }

  void _updateCountryCode(String countryCode) {
    final newCountryCode = CountryCode.fromCountryCode(countryCode);
    if (newCountryCode.dialCode != selectedCountryCode.dialCode) {
      setState(() {
        selectedCountryCode = newCountryCode;
      });
    }
  }

  void _onCountryChanged(CountryCode countryCode) {
    setState(() {
      selectedCountryCode = countryCode;
      isAutoDetecting = false; // Disable auto-detection when manually changed
    });
  }

  String _formatPhoneNumber(String phoneNumber) {
    // Remove any non-digit characters
    String cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');

    debugPrint('🧹 Formatting phone number: $cleanNumber (Country: ${selectedCountryCode.code})');

    // Handle country-specific formatting
    if (selectedCountryCode.code == 'IN') {
      // For Indian numbers, intelligently handle 91 prefix
      if (cleanNumber.startsWith('91')) {
        if (cleanNumber.length == 12) {
          // Perfect case: ************ -> 9876543210
          cleanNumber = cleanNumber.substring(2);
          debugPrint('🧹 Removed 91 prefix (12->10 digits): $phoneNumber -> $cleanNumber');
        } else if (cleanNumber.length > 12) {
          // Too many digits, take 10 digits after removing 91
          cleanNumber = cleanNumber.substring(2, 12);
          debugPrint('🧹 Removed 91 prefix (>12->10 digits): $phoneNumber -> $cleanNumber');
        } else if (cleanNumber.length == 11) {
          // Incomplete number: 91987654321 -> 987654321
          cleanNumber = cleanNumber.substring(2);
          debugPrint('🧹 Removed 91 prefix (11->9 digits): $phoneNumber -> $cleanNumber');
        }
        // If 10 digits or less, treat as normal number
      } else if (cleanNumber.startsWith('0')) {
        // Remove leading zero for Indian numbers
        cleanNumber = cleanNumber.replaceFirst(RegExp(r'^0+'), '');
        debugPrint('🧹 Removed leading zero: $phoneNumber -> $cleanNumber');
      }
    } else {
      // For other countries, remove leading zeros
      cleanNumber = cleanNumber.replaceFirst(RegExp(r'^0+'), '');

      // Remove country code if present
      String dialCode = selectedCountryCode.dialCode?.replaceAll('+', '') ?? '';
      if (dialCode.isNotEmpty && cleanNumber.startsWith(dialCode)) {
        cleanNumber = cleanNumber.substring(dialCode.length);
        debugPrint('🧹 Removed country code $dialCode: $phoneNumber -> $cleanNumber');
      }
    }

    debugPrint('🧹 Final formatted number: $cleanNumber');
    return cleanNumber;
  }

  bool _isValidPhoneNumber(String phoneNumber) {
    final formattedNumber = _formatPhoneNumber(phoneNumber);

    debugPrint('🔍 Validating phone number: $formattedNumber (Country: ${selectedCountryCode.code})');

    // Basic validation - should be 7-15 digits
    if (formattedNumber.length < 7 || formattedNumber.length > 15) {
      debugPrint('❌ Validation failed: Length ${formattedNumber.length} not in range 7-15');
      return false;
    }

    // Country-specific validation
    bool isValid = false;
    switch (selectedCountryCode.code) {
      case 'IN':
        isValid = formattedNumber.length == 10 && formattedNumber.startsWith(RegExp(r'[6-9]'));
        debugPrint(
            '🔍 Indian validation: Length=${formattedNumber.length}, StartsWithValidDigit=${formattedNumber.startsWith(RegExp(r'[6-9]'))}, Valid=$isValid');
        break;
      case 'US':
      case 'CA':
        isValid = formattedNumber.length == 10;
        debugPrint('🔍 US/CA validation: Length=${formattedNumber.length}, Valid=$isValid');
        break;
      default:
        isValid = formattedNumber.length >= 7;
        debugPrint('🔍 Other country validation: Length=${formattedNumber.length}, Valid=$isValid');
        break;
    }

    return isValid;
  }

  void _sendOtp() async {
    final phoneNumber = _phoneController.text.trim();

    if (phoneNumber.isEmpty) {
      Get.snackbar(
        'Error',
        'Please enter your phone number',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    if (!_isValidPhoneNumber(phoneNumber)) {
      Get.snackbar(
        'Error',
        'Please enter a valid phone number',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    setState(() {
      isLoading = true;
    });

    try {
      final formattedNumber = _formatPhoneNumber(phoneNumber);
      final fullNumber = '${selectedCountryCode.dialCode}$formattedNumber';

      debugPrint('📱 Sending OTP for number: $fullNumber');
      debugPrint('📱 Sending OTP to: $formattedNumber');

      final success = await _authController.sendOtp(formattedNumber);

      if (success) {
        debugPrint('📱 OTP sent successfully, moving to login screen');

        // Navigate to login screen with OTP step
        Get.toNamed('/login', arguments: {
          'phoneNumber': formattedNumber,
          'fullNumber': fullNumber,
          'countryCode': selectedCountryCode,
          'startWithOtp': true,
        });
      } else {
        Get.snackbar(
          'Error',
          'Failed to send OTP',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      debugPrint('❌ Error sending OTP: $e');
      Get.snackbar(
        'Error',
        'Failed to send OTP. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      resizeToAvoidBottomInset: true,
      floatingActionButton: Container(
        margin: const EdgeInsets.symmetric(horizontal: 24.0),
        child: SizedBox(
          width: double.infinity,
          height: 50,
          child: CommonElevatedButton(
            onPressed: isLoading ? null : _sendOtp,
            child: isLoading
                ? Row(
                    mainAxisSize: MainAxisSize.min,
                    children: const [
                      SizedBox(
                        height: 16,
                        width: 16,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      ),
                      SizedBox(width: 8),
                      Text(
                        'Sending...',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  )
                : const Text(
                    'Continue',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      body: SafeArea(
        child: SingleChildScrollView(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height -
                  MediaQuery.of(context).padding.top -
                  MediaQuery.of(context).padding.bottom,
            ),
            child: IntrinsicHeight(
              child: Column(
                children: [
                  // Hero Image Container - Responsive with bottom border radius
                  Container(
                    width: double.infinity,
                    height: MediaQuery.of(context).size.height * 0.65, // 65% of screen height
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(16),
                        bottomRight: Radius.circular(16),
                      ),
                      image: DecorationImage(
                        image: AssetImage('assets/images/rider_image.png'),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),

                  // Content section
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(24.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Start delivering orders with\nKisanKonnect',
                            textAlign: TextAlign.center,
                            style: AppTextTheme.responsive(context, Theme.of(context).textTheme.headlineMedium!),
                          ),
                          const SizedBox(height: 24),

                          // Phone Number Input Container
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.grey.shade300),
                              color: Colors.white,
                            ),
                            child: Row(
                              children: [
                                // Country Code Picker
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 12),
                                  child: CountryCodePickerWidget(
                                    onChanged: _onCountryChanged,
                                    initialSelection: selectedCountryCode,
                                  ),
                                ),

                                // Divider
                                Container(
                                  height: 30,
                                  width: 1,
                                  color: Colors.grey.shade300,
                                ),

                                // Phone Number Field
                                Expanded(
                                  child: TextField(
                                    controller: _phoneController,
                                    keyboardType: TextInputType.phone,
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.black87,
                                    ),
                                    decoration: const InputDecoration(
                                      hintText: 'Enter your 10 digit mobile number',
                                      hintStyle: TextStyle(
                                        color: Colors.grey,
                                        fontSize: 16,
                                        fontWeight: FontWeight.normal,
                                      ),
                                      border: InputBorder.none,
                                      contentPadding: EdgeInsets.symmetric(
                                        horizontal: 16,
                                        vertical: 16,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // Helper text
                          const Padding(
                            padding: EdgeInsets.only(top: 8, left: 4),
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: Text(
                                'We\'ll send you a verification code',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
