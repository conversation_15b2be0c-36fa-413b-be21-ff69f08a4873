/// Model for Rider Work Details Registration API request
class WorkDetailsRequest {
  final String mobileNo;
  final String referBy;
  final int shiftID;
  final int vehicleType;
  final int slotName;
  final String? subSlotName;
  final String? weekoffDays;
  final int? dcid;

  const WorkDetailsRequest({
    required this.mobileNo,
    required this.referBy,
    required this.shiftID,
    required this.vehicleType,
    required this.slotName,
    this.subSlotName,
    this.weekoffDays,
    this.dcid,
  });

  /// Create from JSON
  factory WorkDetailsRequest.fromJson(Map<String, dynamic> json) {
    return WorkDetailsRequest(
      mobileNo: json['mobileNo'] ?? '',
      referBy: json['referBy'] ?? '',
      shiftID: json['shiftID'] ?? 0,
      vehicleType: json['vehicleType'] ?? 0,
      slotName: json['slotName'] ?? 0,
      subSlotName: json['subSlotName'],
      weekoffDays: json['weekoffDays'],
      dcid: json['dcid'],
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'mobileNo': mobileNo,
      'referBy': referBy,
      'shiftID': shiftID,
      'vehicleType': vehicleType,
      'slotName': slotName,
    };

    // Add optional fields if they exist
    if (subSlotName != null && subSlotName!.isNotEmpty) {
      json['subSlotName'] = subSlotName;
    }
    if (weekoffDays != null && weekoffDays!.isNotEmpty) {
      json['weekoffDays'] = weekoffDays;
    }
    if (dcid != null) {
      json['dcid'] = dcid;
    }

    return json;
  }

  /// Create a copy with updated fields
  WorkDetailsRequest copyWith({
    String? mobileNo,
    String? referBy,
    int? shiftID,
    int? vehicleType,
    int? slotName,
    String? subSlotName,
    String? weekoffDays,
    int? dcid,
  }) {
    return WorkDetailsRequest(
      mobileNo: mobileNo ?? this.mobileNo,
      referBy: referBy ?? this.referBy,
      shiftID: shiftID ?? this.shiftID,
      vehicleType: vehicleType ?? this.vehicleType,
      slotName: slotName ?? this.slotName,
      subSlotName: subSlotName ?? this.subSlotName,
      weekoffDays: weekoffDays ?? this.weekoffDays,
      dcid: dcid ?? this.dcid,
    );
  }

  /// Validate required fields
  bool isValid() {
    return mobileNo.isNotEmpty && referBy.isNotEmpty && shiftID > 0 && vehicleType > 0 && slotName > 0;
  }

  /// Get validation errors
  List<String> getValidationErrors() {
    final errors = <String>[];

    if (mobileNo.isEmpty) errors.add('Mobile number is required');
    if (referBy.isEmpty) errors.add('Reference is required');
    if (shiftID <= 0) errors.add('Valid shift ID is required');
    if (vehicleType <= 0) errors.add('Vehicle type is required');
    if (slotName <= 0) errors.add('Slot name is required');

    return errors;
  }

  @override
  String toString() {
    return 'WorkDetailsRequest(mobileNo: $mobileNo, referBy: $referBy, shiftID: $shiftID, vehicleType: $vehicleType, slotName: $slotName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is WorkDetailsRequest &&
        other.mobileNo == mobileNo &&
        other.referBy == referBy &&
        other.shiftID == shiftID &&
        other.vehicleType == vehicleType &&
        other.slotName == slotName &&
        other.subSlotName == subSlotName &&
        other.weekoffDays == weekoffDays &&
        other.dcid == dcid;
  }

  @override
  int get hashCode {
    return mobileNo.hashCode ^
        referBy.hashCode ^
        shiftID.hashCode ^
        vehicleType.hashCode ^
        slotName.hashCode ^
        subSlotName.hashCode ^
        weekoffDays.hashCode ^
        dcid.hashCode;
  }
}

/// Response model for work details registration
class WorkDetailsResponse {
  final int status;
  final String message;
  final dynamic data;

  const WorkDetailsResponse({
    required this.status,
    required this.message,
    this.data,
  });

  factory WorkDetailsResponse.fromJson(Map<String, dynamic> json) {
    return WorkDetailsResponse(
      status: json['status'] ?? 0,
      message: json['msg'] ?? '',
      data: json['data'],
    );
  }

  bool get isSuccess => status == 200;

  @override
  String toString() {
    return 'WorkDetailsResponse(status: $status, message: $message)';
  }
}

/// Vehicle types enum
enum VehicleType {
  bike(1, 'Bike'),
  scooter(2, 'Scooter'),
  bicycle(3, 'Bicycle'),
  car(4, 'Car'),
  van(5, 'Van');

  const VehicleType(this.id, this.displayName);
  final int id;
  final String displayName;

  static VehicleType? fromId(int id) {
    for (final type in VehicleType.values) {
      if (type.id == id) return type;
    }
    return null;
  }
}

/// Shift types enum
enum ShiftType {
  morning(1, 'Morning Shift'),
  afternoon(2, 'Afternoon Shift'),
  evening(3, 'Evening Shift'),
  night(4, 'Night Shift'),
  fullDay(5, 'Full Day');

  const ShiftType(this.id, this.displayName);
  final int id;
  final String displayName;

  static ShiftType? fromId(int id) {
    for (final shift in ShiftType.values) {
      if (shift.id == id) return shift;
    }
    return null;
  }
}

/// Slot names enum
enum SlotName {
  slot1(1, 'Slot 1'),
  slot2(2, 'Slot 2'),
  slot3(3, 'Slot 3'),
  slot4(4, 'Slot 4');

  const SlotName(this.id, this.displayName);
  final int id;
  final String displayName;

  static SlotName? fromId(int id) {
    for (final slot in SlotName.values) {
      if (slot.id == id) return slot;
    }
    return null;
  }
}

/// Week off days enum
enum WeekOffDay {
  sunday('Sunday'),
  monday('Monday'),
  tuesday('Tuesday'),
  wednesday('Wednesday'),
  thursday('Thursday'),
  friday('Friday'),
  saturday('Saturday');

  const WeekOffDay(this.displayName);
  final String displayName;
}

/// Work details validation helper
class WorkDetailsValidator {
  /// Validate mobile number format
  static bool isValidMobileNumber(String mobileNo) {
    final regex = RegExp(r'^[6-9]\d{9}$');
    return regex.hasMatch(mobileNo);
  }

  /// Validate reference code format
  static bool isValidReferenceCode(String referBy) {
    // Basic validation - should be alphanumeric and at least 3 characters
    final regex = RegExp(r'^[A-Z0-9]{3,}$');
    return regex.hasMatch(referBy.toUpperCase());
  }

  /// Get mobile number validation error message
  static String? getMobileValidationError(String mobileNo) {
    if (mobileNo.isEmpty) return 'Mobile number is required';
    if (!isValidMobileNumber(mobileNo)) {
      return 'Invalid mobile number format';
    }
    return null;
  }

  /// Get reference validation error message
  static String? getReferenceValidationError(String referBy) {
    if (referBy.isEmpty) return 'Reference is required';
    if (!isValidReferenceCode(referBy)) {
      return 'Invalid reference format. Should be alphanumeric and at least 3 characters';
    }
    return null;
  }

  /// Get shift ID validation error message
  static String? getShiftValidationError(int shiftID) {
    if (shiftID <= 0) return 'Please select a valid shift';
    return null;
  }

  /// Get vehicle type validation error message
  static String? getVehicleTypeValidationError(int vehicleType) {
    if (vehicleType <= 0) return 'Please select a vehicle type';
    return null;
  }

  /// Get slot name validation error message
  static String? getSlotValidationError(int slotName) {
    if (slotName <= 0) return 'Please select a time slot';
    return null;
  }
}
