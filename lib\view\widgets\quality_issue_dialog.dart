import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

class QualityIssueDialog extends StatefulWidget {
  const QualityIssueDialog({super.key});

  @override
  State<QualityIssueDialog> createState() => _QualityIssueDialogState();
}

class _QualityIssueDialogState extends State<QualityIssueDialog> {
  String selectedCategory = 'All';
  Map<String, int> itemQuantities = {
    'Fresh Cow Milk (Min. Fat 4.0% )': 0,
    'Ghar Jaisa Dahi Tub': 0,
    'Dragonfruit White-Imported': 0,
    'Papaya-Small': 0,
    'Economy Royal Gala Apple': 0,
    'Pear Green Imported': 0,
  };

  final List<String> categories = ['All', 'Saddle bag', 'Silver bag'];
  
  final List<Map<String, dynamic>> items = [
    {
      'name': 'Fresh Cow Milk (Min. Fat 4.0% )',
      'description': '500ml . 2 Nos',
      'image': 'assets/images/milk.png',
      'category': 'Saddle bag',
    },
    {
      'name': 'Ghar Jai<PERSON> Tu<PERSON>',
      'description': '1kg . 1 Nos',
      'image': 'assets/images/dahi.png',
      'category': 'Saddle bag',
    },
    {
      'name': 'Dragonfruit White-Imported',
      'description': '250gm . 2 Nos',
      'image': 'assets/images/dragonfruit.png',
      'category': 'Silver bag',
    },
    {
      'name': 'Papaya-Small',
      'description': 'App.(520g to 600g) . 1 Nos .',
      'image': 'assets/images/papaya.png',
      'category': 'Silver bag',
    },
    {
      'name': 'Economy Royal Gala Apple',
      'description': 'App.(520g to 600g) . 4 Nos',
      'image': 'assets/images/apple.png',
      'category': 'Silver bag',
    },
    {
      'name': 'Pear Green Imported',
      'description': 'App.(520g to 600g) . 1 Nos',
      'image': 'assets/images/pear.png',
      'category': 'Silver bag',
    },
  ];

  List<Map<String, dynamic>> get filteredItems {
    if (selectedCategory == 'All') {
      return items;
    }
    return items.where((item) => item['category'] == selectedCategory).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: double.infinity,
        height: MediaQuery.of(context).size.height * 0.8,
        margin: EdgeInsets.symmetric(horizontal: 20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          children: [
            // Header with close button
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Text(
                    'Mark which items having quality issue',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ),
                  ),
                  Spacer(),
                  GestureDetector(
                    onTap: () => Get.back(),
                    child: Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade400,
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // Category filter tabs
            Container(
              padding: EdgeInsets.all(16),
              child: Row(
                children: categories.map((category) {
                  final isSelected = selectedCategory == category;
                  return Expanded(
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          selectedCategory = category;
                        });
                      },
                      child: Container(
                        margin: EdgeInsets.symmetric(horizontal: 4),
                        padding: EdgeInsets.symmetric(vertical: 8),
                        decoration: BoxDecoration(
                          color: isSelected ? Colors.black : Colors.white,
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: isSelected ? Colors.black : Colors.grey.shade300,
                          ),
                        ),
                        child: Text(
                          category,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: isSelected ? Colors.white : Colors.black,
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
            
            // Items list
            Expanded(
              child: ListView.builder(
                padding: EdgeInsets.symmetric(horizontal: 16),
                itemCount: filteredItems.length,
                itemBuilder: (context, index) {
                  final item = filteredItems[index];
                  final itemName = item['name'];
                  final quantity = itemQuantities[itemName] ?? 0;
                  
                  return Container(
                    margin: EdgeInsets.only(bottom: 12),
                    padding: EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade200),
                    ),
                    child: Row(
                      children: [
                        // Item image
                        Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            color: Colors.grey.shade100,
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: _buildItemImage(item['image']),
                          ),
                        ),
                        SizedBox(width: 12),
                        
                        // Item details
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                itemName,
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black,
                                ),
                              ),
                              SizedBox(height: 2),
                              Text(
                                item['description'],
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ],
                          ),
                        ),
                        
                        // Quantity controls
                        Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColors.green),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              GestureDetector(
                                onTap: () {
                                  if (quantity > 0) {
                                    setState(() {
                                      itemQuantities[itemName] = quantity - 1;
                                    });
                                  }
                                },
                                child: Container(
                                  width: 32,
                                  height: 32,
                                  decoration: BoxDecoration(
                                    color: quantity > 0 ? AppColors.green : Colors.transparent,
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: Icon(
                                    Icons.remove,
                                    color: quantity > 0 ? Colors.white : AppColors.green,
                                    size: 16,
                                  ),
                                ),
                              ),
                              Container(
                                width: 40,
                                child: Text(
                                  quantity.toString().padLeft(2, '0'),
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.green,
                                  ),
                                ),
                              ),
                              GestureDetector(
                                onTap: () {
                                  setState(() {
                                    itemQuantities[itemName] = quantity + 1;
                                  });
                                },
                                child: Container(
                                  width: 32,
                                  height: 32,
                                  decoration: BoxDecoration(
                                    color: AppColors.green,
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: Icon(
                                    Icons.add,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
            
            // Quality issue button
            Container(
              padding: EdgeInsets.all(16),
              child: SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton(
                  onPressed: () {
                    final selectedItems = itemQuantities.entries
                        .where((entry) => entry.value > 0)
                        .toList();
                    
                    if (selectedItems.isNotEmpty) {
                      Get.back();
                      Get.snackbar(
                        'Quality Issues Marked',
                        '${selectedItems.length} items marked with quality issues',
                        backgroundColor: AppColors.green,
                        colorText: Colors.white,
                      );
                    } else {
                      Get.snackbar(
                        'No Items Selected',
                        'Please select items with quality issues',
                        backgroundColor: Colors.orange,
                        colorText: Colors.white,
                      );
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey.shade400,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(24),
                    ),
                  ),
                  child: Text(
                    'Quality issue',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemImage(String imagePath) {
    // Create simple colored containers as placeholders for different items
    final colors = {
      'milk.png': Colors.blue.shade100,
      'dahi.png': Colors.yellow.shade100,
      'dragonfruit.png': Colors.pink.shade100,
      'papaya.png': Colors.orange.shade100,
      'apple.png': Colors.red.shade100,
      'pear.png': Colors.green.shade100,
    };
    
    final icons = {
      'milk.png': Icons.local_drink,
      'dahi.png': Icons.icecream,
      'dragonfruit.png': Icons.local_florist,
      'papaya.png': Icons.local_florist,
      'apple.png': Icons.apple,
      'pear.png': Icons.local_florist,
    };
    
    final fileName = imagePath.split('/').last;
    
    return Container(
      color: colors[fileName] ?? Colors.grey.shade100,
      child: Center(
        child: Icon(
          icons[fileName] ?? Icons.shopping_basket,
          color: Colors.grey.shade600,
          size: 24,
        ),
      ),
    );
  }
}

// Helper function to show the dialog
void showQualityIssueDialog() {
  Get.dialog(
    QualityIssueDialog(),
    barrierDismissible: true,
  );
}
