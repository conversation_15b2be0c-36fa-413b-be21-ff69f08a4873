name: C<PERSON>EV
on:
  pull_request:
    branches:
      - '**'
  push:
    branches:
      - '**'
jobs:
  build:
    name: Build APK
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v1
      - uses: actions/setup-java@v1
        with:
          java-version: '17'
      - uses: subosito/flutter-action@v1
        with:
          flutter-version: '3.32.0'
          channel: 'stable'
      - run: flutter doctor
      - run: flutter pub get
      ## - run: flutter test
      ## build and upload production apk
      - run: flutter build apk --flavor prod -t lib/main_prod.dart
      # - uses: actions/upload-artifact@v3
      #   with:
      #     name: production-apk
      #     path: build/app/outputs/flutter-apk/app-production-release.apk
      ## build and upload development apk
      - run: flutter build apk --flavor dev -t lib/main_dev.dart
      # - uses: actions/upload-artifact@v3
      #   with:
      #     name: development-apk
      #     path: build/app/outputs/flutter-apk/app-development-release.apk
      ## build and upload development abb
      # - run: flutter build appbundle --flavor production -t lib/main_production.dart
      # - uses: actions/upload-artifact@v3
      #   with:
      #     name: development-abb
      #     path: build/app/outputs/bundle/production/Release/app-production-release.aab
      # ## push artifatcs to release
      # - name: Push to Releases
      #   uses: ncipollo/release-action@v1
      #   with:
      #     artifacts: "build/app/outputs/flutter-apk/*"
      #     tag: 0.0.94
      ##     tag: v1.0.${{ github.run_number }}
      #     token: ${{ secrets.frcicd }}