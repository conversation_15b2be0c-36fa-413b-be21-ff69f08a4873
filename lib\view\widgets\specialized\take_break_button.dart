import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/view/widgets/dialogs/break_time_dialog.dart';

class TakeBreakButton extends StatelessWidget {
  final VoidCallback? onBreakStarted;
  final VoidCallback? onBreakEnded;
  final bool isOnBreak;

  const TakeBreakButton({
    super.key,
    this.onBreakStarted,
    this.onBreakEnded,
    this.isOnBreak = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ElevatedButton.icon(
        onPressed: isOnBreak ? null : _showBreakDialog,
        style: ElevatedButton.styleFrom(
          backgroundColor: isOnBreak ? Colors.grey.shade400 : AppColors.green,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: isOnBreak ? 0 : 2,
        ),
        icon: Icon(
          isOnBreak ? Icons.pause_circle_filled : Icons.coffee,
          size: 24,
        ),
        label: Text(
          isOnBreak ? 'On Break' : 'Take Break',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  void _showBreakDialog() {
    Get.dialog(
      BreakTimeDialog(
        onConfirm: () {
          onBreakStarted?.call();
          Get.back();
          _showBreakTimerDialog();
        },
        onCancel: () {
          Get.back();
        },
      ),
    );
  }

  void _showBreakTimerDialog() {
    Get.dialog(
      BreakTimerDialog(
        onStopBreak: () {
          onBreakEnded?.call();
          Get.back();
        },
        onCancel: () {
          Get.back();
        },
      ),
      barrierDismissible: false,
    );
  }
}

class ShiftStatusCard extends StatefulWidget {
  final String shiftType;
  final String shiftTime;
  final bool isActive;

  const ShiftStatusCard({
    super.key,
    required this.shiftType,
    required this.shiftTime,
    this.isActive = false,
  });

  @override
  State<ShiftStatusCard> createState() => _ShiftStatusCardState();
}

class _ShiftStatusCardState extends State<ShiftStatusCard> {
  bool _isOnBreak = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Shift info
          Row(
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: widget.isActive ? AppColors.green : Colors.grey,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.shiftType,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      widget.shiftTime,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: widget.isActive ? AppColors.green.withValues(alpha: 0.1) : Colors.grey.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  widget.isActive ? 'Active' : 'Inactive',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: widget.isActive ? AppColors.green : Colors.grey,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Break status
          if (_isOnBreak) ...[
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.pause_circle_filled,
                    color: Colors.orange,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'Currently on break',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.orange,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Take break button
          if (widget.isActive)
            TakeBreakButton(
              isOnBreak: _isOnBreak,
              onBreakStarted: () {
                setState(() {
                  _isOnBreak = true;
                });
              },
              onBreakEnded: () {
                setState(() {
                  _isOnBreak = false;
                });
              },
            ),
        ],
      ),
    );
  }
}
