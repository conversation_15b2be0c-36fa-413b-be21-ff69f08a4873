import 'package:flutter/material.dart';

/// Asset optimization utilities to reduce app size
class AssetOptimizer {
  
  /// Get optimized icon for common use cases
  static Widget getOptimizedIcon(String assetName, {
    double? size,
    Color? color,
  }) {
    // Map asset names to Material Icons to reduce app size
    final iconMap = {
      'working-time': Icons.access_time,
      'timer-frame': Icons.timer,
      'money-box': Icons.account_balance_wallet,
      'Bike-Icon': Icons.motorcycle,
      'booking': Icons.book_online,
      'rupee': Icons.currency_rupee,
      'family-protection': Icons.family_restroom,
      'paper_bag': Icons.shopping_bag,
      'Group': Icons.group,
    };

    final iconData = iconMap[assetName];
    
    if (iconData != null) {
      return Icon(
        iconData,
        size: size ?? 24,
        color: color,
      );
    }
    
    // Fallback to asset image if no icon mapping exists
    return Image.asset(
      'assets/icons/$assetName.png',
      width: size,
      height: size,
      color: color,
      errorBuilder: (context, error, stackTrace) {
        return Icon(
          Icons.image_not_supported,
          size: size ?? 24,
          color: color ?? Colors.grey,
        );
      },
    );
  }

  /// Get optimized image with caching and compression
  static Widget getOptimizedImage(String imagePath, {
    double? width,
    double? height,
    BoxFit? fit,
  }) {
    return Image.asset(
      imagePath,
      width: width,
      height: height,
      fit: fit ?? BoxFit.contain,
      // Enable caching to reduce memory usage
      cacheWidth: width?.toInt(),
      cacheHeight: height?.toInt(),
      errorBuilder: (context, error, stackTrace) {
        return Container(
          width: width,
          height: height,
          color: Colors.grey[200],
          child: Icon(
            Icons.image_not_supported,
            color: Colors.grey[400],
          ),
        );
      },
    );
  }

  /// Create placeholder for missing assets
  static Widget createPlaceholder({
    double? width,
    double? height,
    IconData? icon,
    String? text,
  }) {
    return Container(
      width: width ?? 50,
      height: height ?? 50,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon ?? Icons.image,
            color: Colors.grey[400],
            size: (width ?? 50) * 0.4,
          ),
          if (text != null) ...[
            SizedBox(height: 4),
            Text(
              text,
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  /// List of assets that can be safely removed
  static const List<String> removableAssets = [
    'assets/images/working-time.png', // Use Icons.access_time
    'assets/images/money-box.png',    // Use Icons.account_balance_wallet
    'assets/images/Bike-Icon.png',    // Use Icons.motorcycle
    'assets/images/booking.png',      // Use Icons.book_online
    'assets/images/rupee.png',        // Use Icons.currency_rupee
    'assets/icons/working-time.png',  // Use Icons.access_time
    'assets/icons/timer-frame.png',   // Use Icons.timer
    'assets/icons/family-protection.png', // Use Icons.family_restroom
    'assets/icons/paper_bag.png',     // Use Icons.shopping_bag
    'assets/icons/Group.png',         // Use Icons.group
  ];

  /// Essential assets that should be kept
  static const List<String> essentialAssets = [
    'assets/images/kk_rider_logo.png', // App logo
    'assets/images/rider_image.png',   // Profile placeholder
    'assets/images/capture_image.png', // Camera placeholder
    'assets/images/badge.jpg',         // Achievement badge
  ];

  /// Get asset size reduction estimate
  static String getOptimizationSummary() {
    return '''
Asset Optimization Summary:
• ${removableAssets.length} assets can be replaced with icons
• Estimated size reduction: ~500KB - 2MB
• ${essentialAssets.length} essential assets will be kept
• Use vector icons for better scalability
''';
  }
}

/// Extension to easily replace asset images with optimized versions
extension AssetOptimization on String {
  Widget toOptimizedIcon({double? size, Color? color}) {
    final assetName = split('/').last.split('.').first;
    return AssetOptimizer.getOptimizedIcon(assetName, size: size, color: color);
  }
  
  Widget toOptimizedImage({double? width, double? height, BoxFit? fit}) {
    return AssetOptimizer.getOptimizedImage(this, width: width, height: height, fit: fit);
  }
}
