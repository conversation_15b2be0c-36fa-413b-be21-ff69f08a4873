import 'package:flutter/material.dart';
import 'lib/services/auth/authentication_service.dart';
import 'lib/services/api/api_helper.dart';
import 'lib/config/env_config.dart';
import 'lib/config/flavor_config.dart';
import 'lib/models/auth_models.dart';

/// Test script to debug FE_SRInfoAfterLogin API integration
/// This script tests the API call directly to identify any issues
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize environment
  await EnvConfig.init(flavor: Flavor.development);

  // Test the API call
  await testSRInfoAfterLoginAPI();
}

Future<void> testSRInfoAfterLoginAPI() async {
  try {
    debugPrint('🧪 Testing FE_SRInfoAfterLogin API integration');

    // Test parameters (using the same values from your curl command)
    const String mobileNo = '9158584258';
    const String otp = '427617';
    const String gcmId = '';
    const String imei = '';

    debugPrint('📱 Test parameters:');
    debugPrint('   Mobile: $mobileNo');
    debugPrint('   OTP: $otp');
    debugPrint('   GCMID: $gcmId');
    debugPrint('   IMEI: $imei');

    // Initialize API helper
    final apiHelper = ApiHelper.instance;
    debugPrint('🌐 API Helper initialized');
    debugPrint('🌐 Environment info: ${apiHelper.getEnvironmentInfo()}');

    // Test direct API call using ApiHelper
    debugPrint('\n🔄 Testing direct API call...');
    final directResponse = await apiHelper.get<dynamic>(
      '/Rider/FE_SRInfoAfterLogin',
      queryParameters: {
        'MobileNo': mobileNo,
        'OTP': int.tryParse(otp) ?? 0,
        'GCMID': gcmId,
        'IMEI': imei,
      },
    );

    if (directResponse.isSuccess) {
      debugPrint('✅ Direct API call successful');
      debugPrint('📊 Response data type: ${directResponse.data.runtimeType}');
      debugPrint('📊 Response keys: ${directResponse.data?.keys?.toList()}');

      // Try to parse the response
      try {
        final srInfoResponse = SRInfoAfterLoginResponse.fromJson(directResponse.data);
        debugPrint('✅ Response parsed successfully');
        debugPrint('📊 Status: ${srInfoResponse.status}');
        debugPrint('📊 Message: ${srInfoResponse.msg}');
        debugPrint('📊 Login info count: ${srInfoResponse.loginInfo.length}');
        debugPrint('📊 Location data count: ${srInfoResponse.latLong.length}');

        if (srInfoResponse.loginInfo.isNotEmpty) {
          final firstLogin = srInfoResponse.loginInfo.first;
          debugPrint('👤 First login info:');
          debugPrint('   ID: ${firstLogin.id}');
          debugPrint('   Name: ${firstLogin.srName}');
          debugPrint('   Mobile: ${firstLogin.mobileNo}');
          debugPrint('   DC ID: ${firstLogin.dcid}');
          debugPrint('   KFH Status: ${firstLogin.kfhStatus}');
        }
      } catch (parseError) {
        debugPrint('❌ Error parsing response: $parseError');
        debugPrint('📊 Raw response: ${directResponse.data}');
      }
    } else {
      debugPrint('❌ Direct API call failed: ${directResponse.error}');
    }

    // Test using AuthenticationService
    debugPrint('\n🔄 Testing AuthenticationService method...');
    final authService = AuthenticationService.instance;
    final serviceResponse = await authService.getRiderInfoAfterLoginDetailed(
      mobileNo: mobileNo,
      otp: otp,
      gcmId: gcmId,
      imei: imei,
    );

    if (serviceResponse.isSuccess) {
      debugPrint('✅ AuthenticationService call successful');
      final srInfo = serviceResponse.data!;
      debugPrint('📊 Status: ${srInfo.status}');
      debugPrint('📊 Message: ${srInfo.msg}');
      debugPrint('📊 Login info count: ${srInfo.loginInfo.length}');

      // Test the storage method
      debugPrint('\n🔄 Testing storage method...');
      final storeResponse = await authService.fetchAndStoreRiderInfoAfterLogin(
        mobileNo: mobileNo,
        otp: otp,
        gcmId: gcmId,
        imei: imei,
      );

      if (storeResponse.isSuccess) {
        debugPrint('✅ Data stored successfully');
      } else {
        debugPrint('❌ Failed to store data: ${storeResponse.error}');
      }
    } else {
      debugPrint('❌ AuthenticationService call failed: ${serviceResponse.error}');
    }

    debugPrint('\n🧪 Test completed');
  } catch (e) {
    debugPrint('🚨 Test error: $e');
  }
}
