import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

import 'package:kisankonnect_rider/utils/responsive_utils.dart';
import 'package:get/get.dart';

class CommonAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String titleKey;
  final VoidCallback? onBackPressed;
  final List<Widget>? actions;
  final bool centerTitle;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? elevation;
  final bool automaticallyImplyLeading;

  const CommonAppBar({
    super.key,
    required this.titleKey,
    this.onBackPressed,
    this.actions,
    this.centerTitle = false,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation,
    this.automaticallyImplyLeading = true,
  });

  @override
  Widget build(BuildContext context) {
    final horizontalPadding = ResponsiveUtils.spacingM(context);
    final iconSize = ResponsiveUtils.iconSize(context, IconSizeType.medium);
    final titleFontSize = ResponsiveUtils.fontSize(context, 18);

    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.white,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20.0),
          bottomRight: Radius.circular(20.0),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.25),
            offset: const Offset(0, 8),
            blurRadius: 16,
            spreadRadius: 2,
          ),
        ],
      ),
      child: AppBar(
        backgroundColor: Colors.transparent,
        foregroundColor: foregroundColor ?? AppColors.textPrimary,
        elevation: 0, // Remove default elevation since we're using container shadow
        centerTitle: centerTitle,
        automaticallyImplyLeading: false, // We'll handle leading manually
        toolbarHeight: 54.0, // Fixed height as per specs
        titleSpacing: 0,
        title: Padding(
          padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
          child: Row(
            children: [
              // Back button (if needed)
              if (automaticallyImplyLeading && (onBackPressed != null || Navigator.canPop(context)))
                IconButton(
                  icon: Icon(
                    Icons.arrow_back_ios,
                    size: iconSize,
                    color: foregroundColor ?? AppColors.textPrimary,
                  ),
                  onPressed: onBackPressed ?? () => Get.back(),
                  padding: EdgeInsets.zero,
                  constraints: BoxConstraints(
                    minWidth: iconSize + 8,
                    minHeight: iconSize + 8,
                  ),
                ),

              // Title
              Expanded(
                child: Text(
                  AppStrings.get(titleKey),
                  style: AppTextTheme.appBarTitle.copyWith(
                    fontSize: titleFontSize,
                    fontWeight: FontWeight.w600,
                    color: foregroundColor ?? AppColors.textPrimary,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),

              // Actions
              if (actions != null) ...actions!,
            ],
          ),
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(54.0); // Fixed height as per specs
}

/// Common App Bar with custom content
class CommonAppBarWithContent extends StatelessWidget implements PreferredSizeWidget {
  final Widget content;
  final Color? backgroundColor;
  final double? elevation;

  const CommonAppBarWithContent({
    super.key,
    required this.content,
    this.backgroundColor,
    this.elevation,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.white,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20.0),
          bottomRight: Radius.circular(20.0),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.25),
            offset: const Offset(0, 8),
            blurRadius: 16,
            spreadRadius: 2,
          ),
        ],
      ),
      child: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0, // Remove default elevation since we're using container shadow
        automaticallyImplyLeading: false,
        toolbarHeight: 54.0, // Fixed height as per specs
        titleSpacing: 0,
        title: content,
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(54.0);
}
