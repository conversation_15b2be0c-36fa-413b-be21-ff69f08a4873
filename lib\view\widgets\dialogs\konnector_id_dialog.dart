import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

/// Dialog for entering Konnector ID when Konnector (Fixed salary) is selected
class KonnectorIdDialog extends StatefulWidget {
  final Function(String)? onKonnectorIdEntered;

  const KonnectorIdDialog({
    super.key,
    this.onKonnectorIdEntered,
  });

  @override
  State<KonnectorIdDialog> createState() => _KonnectorIdDialogState();
}

class _KonnectorIdDialogState extends State<KonnectorIdDialog> {
  String _konnectorId = '';
  final TextEditingController _controller = TextEditingController();

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onContinuePressed() {
    if (_konnectorId.isNotEmpty) {
      Navigator.of(context).pop();
      widget.onKonnectorIdEntered?.call(_konnectorId);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.red.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.badge,
                    color: Colors.red,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'Konnector ID',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close, color: Colors.grey),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Input field
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: TextField(
                controller: _controller,
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  setState(() {
                    _konnectorId = value;
                  });
                },
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                ),
                decoration: const InputDecoration(
                  hintText: 'Enter konnector ID',
                  border: InputBorder.none,
                  isDense: true,
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Continue button
            SizedBox(
              width: double.infinity,
              height: 48,
              child: ElevatedButton(
                onPressed: _konnectorId.isNotEmpty ? _onContinuePressed : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _konnectorId.isNotEmpty ? AppColors.green : Colors.grey,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(24),
                  ),
                ),
                child: const Text(
                  'Continue',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Show the Konnector ID input dialog
  static Future<String?> show({
    required BuildContext context,
    Function(String)? onKonnectorIdEntered,
  }) async {
    return await showDialog<String>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return KonnectorIdDialog(onKonnectorIdEntered: onKonnectorIdEntered);
      },
    );
  }
}
