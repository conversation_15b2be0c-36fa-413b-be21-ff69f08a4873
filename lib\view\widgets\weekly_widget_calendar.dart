import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:kisankonnect_rider/models/earning_models.dart';

class WeeklyCalendarWidget extends StatelessWidget {
  final List<DailyEarnings> dailyEarnings;
  final DateTime selectedDate;
  final Function(DateTime) onDateSelected;
  final VoidCallback onCalendarTap;

  const WeeklyCalendarWidget({
    super.key,
    required this.dailyEarnings,
    required this.selectedDate,
    required this.onDateSelected,
    required this.onCalendarTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Week header with calendar icon
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Week ${_getWeekNumber()}',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              GestureDetector(
                onTap: onCalendarTap,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: theme.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.calendar_month,
                    color: theme.primaryColor,
                    size: 20,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Days of week
          Row(
            children: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
                .map((day) => Expanded(
                      child: Center(
                        child: Text(
                          day,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.textTheme.bodySmall?.color?.withOpacity(0.6),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ))
                .toList(),
          ),
          const SizedBox(height: 12),

          // Calendar dates
          Row(
            children: dailyEarnings.map((dayEarning) {
              final isSelected = _isSameDay(dayEarning.date, selectedDate);
              final hasEarnings = dayEarning.hasEarnings;
              final dayNumber = dayEarning.date.day;

              return Expanded(
                child: GestureDetector(
                  onTap: () => onDateSelected(dayEarning.date),
                  child: Container(
                    height: 60,
                    margin: const EdgeInsets.symmetric(horizontal: 2),
                    decoration: BoxDecoration(
                      color: isSelected ? theme.primaryColor : Colors.transparent,
                      borderRadius: BorderRadius.circular(12),
                      border: hasEarnings && !isSelected
                          ? Border.all(
                              color: theme.primaryColor.withOpacity(0.3),
                              width: 1,
                            )
                          : null,
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          dayNumber.toString(),
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: isSelected ? Colors.white : theme.textTheme.bodyMedium?.color,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        if (hasEarnings) ...[
                          const SizedBox(height: 4),
                          Text(
                            '₹${dayEarning.amount.toInt()}',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: isSelected
                                  ? Colors.white.withOpacity(0.9)
                                  : theme.textTheme.bodySmall?.color?.withOpacity(0.7),
                              fontSize: 10,
                            ),
                          ),
                        ],
                        if (isSelected) ...[
                          const SizedBox(height: 2),
                          Container(
                            width: 6,
                            height: 6,
                            decoration: const BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year && date1.month == date2.month && date1.day == date2.day;
  }

  int _getWeekNumber() {
    if (dailyEarnings.isEmpty) return 1;

    final firstDate = dailyEarnings.first.date;
    final dayOfYear = int.parse(DateFormat('D').format(firstDate));
    return ((dayOfYear - 1) / 7).floor() + 1;
  }
}
