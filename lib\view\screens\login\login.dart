import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'dart:async';
import '../../widgets/common/common_elevated_button.dart';
import '../../widgets/forms/country_code_picker_widget.dart';
import '../../widgets/forms/pinput_otp_widget.dart';
import '../../widgets/forms/otp_channel_selector.dart';
import 'package:get/get.dart';
import '../../../controllers/auth_controller.dart';
import 'package:country_code_picker/country_code_picker.dart';
import '../../../routes/app_pages.dart';
import '../../widgets/loading/elegant_shimmer.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  late final String? initialPhone;
  late final Function(String)? onOtpVerified;
  final TextEditingController _phoneController = TextEditingController();
  final List<TextEditingController> _otpControllers = List.generate(6, (index) => TextEditingController());
  final List<FocusNode> _otpFocusNodes = List.generate(6, (index) => FocusNode());
  String _currentOtp = '';
  final AuthController _authController = Get.find<AuthController>();
  bool isOtpStep = false;
  bool whatsappChecked = false;
  final String _selectedOtpChannel = 'sms'; // 'sms' or 'whatsapp'
  int timerSeconds = 30;
  Timer? _timer;
  bool isLoading = false;

  // Country code picker variables
  CountryCode selectedCountryCode = CountryCode.fromCountryCode('IN');
  bool isAutoDetecting = true;

  @override
  void initState() {
    super.initState();
    final args = Get.arguments;
    initialPhone = args?['initialPhone'];
    onOtpVerified = args?['onOtpVerified'];

    // Check if user is already logged in and redirect to dashboard
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAuthAndRedirect();
    });

    // Handle arguments from enter number screen or re-login
    if (args != null) {
      final phoneNumber = args['phoneNumber'];
      final countryCode = args['countryCode'];
      final startWithOtp = args['startWithOtp'] ?? false;
      final isReLogin = args['isReLogin'] ?? false;

      if (phoneNumber != null) {
        _phoneController.text = phoneNumber;
      }

      if (countryCode != null) {
        selectedCountryCode = countryCode;
      }

      if (startWithOtp) {
        // Start directly with OTP step
        WidgetsBinding.instance.addPostFrameCallback((_) {
          setState(() {
            isOtpStep = true;
            startTimer();
          });
        });
      } else if (isReLogin) {
        // For re-login, automatically send OTP and go to OTP step
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _autoSendOtpForReLogin();
        });
      }
    }

    if (initialPhone != null) {
      _phoneController.text = initialPhone!;
    }

    // Add listener for phone number changes to auto-detect country
    _phoneController.addListener(_onPhoneNumberChanged);
  }

  /// Check if user is already authenticated and redirect to dashboard
  void _checkAuthAndRedirect() {
    if (_authController.isLoggedIn) {
      debugPrint('🔄 User already logged in, redirecting to dashboard');
      Get.offAllNamed(AppRoutes.dashboard);
    }
  }

  void _onPhoneNumberChanged() {
    // Disable auto-detection to prevent interference with user input
    // Users can manually select country code if needed
    return;
  }

  void _onCountryCodeChanged(CountryCode countryCode) {
    setState(() {
      selectedCountryCode = countryCode;
      isAutoDetecting = false; // Disable auto-detection when manually selected
    });
  }

  /// Clean phone number by removing non-digits and handling country codes
  String _cleanPhoneNumber(String phoneNumber) {
    // Remove any non-digit characters
    String cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');

    if (cleanNumber.isEmpty) return cleanNumber;

    debugPrint('🧹 Cleaning phone number: $cleanNumber (Country: ${selectedCountryCode.code})');

    // Handle country code scenarios
    if (selectedCountryCode.code == 'IN') {
      // For India, intelligently handle numbers starting with 91
      if (cleanNumber.startsWith('91')) {
        if (cleanNumber.length == 12) {
          // Perfect case: ************ -> 9876543210
          cleanNumber = cleanNumber.substring(2);
          debugPrint('🧹 Removing 91 prefix (12->10 digits): $phoneNumber -> $cleanNumber');
        } else if (cleanNumber.length == 13) {
          // Case with extra digit: ************0 -> 9876543210 (take middle 10)
          cleanNumber = cleanNumber.substring(2, 12);
          debugPrint('🧹 Removing 91 prefix (13->10 digits): $phoneNumber -> $cleanNumber');
        } else if (cleanNumber.length > 13) {
          // Too many digits, remove 91 and take first 10
          cleanNumber = cleanNumber.substring(2, 12);
          debugPrint('🧹 Removing 91 prefix (>13->10 digits): $phoneNumber -> $cleanNumber');
        } else if (cleanNumber.length == 11) {
          // Problematic case: 91987654321 -> 987654321 (9 digits)
          // This is likely an incomplete number, but we'll process it
          cleanNumber = cleanNumber.substring(2);
          debugPrint('🧹 Removing 91 prefix (11->9 digits - incomplete): $phoneNumber -> $cleanNumber');
        }
        // If length is 10 or less, treat as normal number without country code
      } else if (cleanNumber.startsWith('0')) {
        // Remove leading zero for Indian numbers
        cleanNumber = cleanNumber.substring(1);
        debugPrint('🧹 Removing leading zero: $phoneNumber -> $cleanNumber');
      }
    } else {
      // For other countries, remove country code if present
      String dialCode = selectedCountryCode.dialCode?.replaceAll('+', '') ?? '';
      if (dialCode.isNotEmpty && cleanNumber.startsWith(dialCode)) {
        cleanNumber = cleanNumber.substring(dialCode.length);
        debugPrint('🧹 Removing country code $dialCode: $phoneNumber -> $cleanNumber');
      }
    }

    debugPrint('🧹 Final cleaned number: $cleanNumber');
    return cleanNumber;
  }

  /// Validate phone number based on country-specific rules
  bool _isValidPhoneNumber(String phoneNumber) {
    if (phoneNumber.isEmpty) {
      debugPrint('❌ Validation failed: Empty phone number');
      return false;
    }

    debugPrint('🔍 Validating phone number: $phoneNumber (Country: ${selectedCountryCode.code})');

    bool isValid = false;
    switch (selectedCountryCode.code) {
      case 'IN':
        // Indian mobile numbers: 10 digits starting with 6, 7, 8, or 9
        isValid = phoneNumber.length == 10 && phoneNumber.startsWith(RegExp(r'[6-9]'));
        debugPrint(
            '🔍 Indian validation: Length=${phoneNumber.length}, StartsWithValidDigit=${phoneNumber.startsWith(RegExp(r'[6-9]'))}, Valid=$isValid');
        break;
      case 'US':
      case 'CA':
        // US/Canada: 10 digits
        isValid = phoneNumber.length == 10;
        debugPrint('🔍 US/CA validation: Length=${phoneNumber.length}, Valid=$isValid');
        break;
      default:
        // Other countries: 7-15 digits
        isValid = phoneNumber.length >= 7 && phoneNumber.length <= 15;
        debugPrint('🔍 Other country validation: Length=${phoneNumber.length}, Valid=$isValid');
        break;
    }

    return isValid;
  }

  @override
  void dispose() {
    _timer?.cancel();
    _phoneController.removeListener(_onPhoneNumberChanged);
    _phoneController.dispose();
    for (var controller in _otpControllers) {
      controller.dispose();
    }
    for (var focusNode in _otpFocusNodes) {
      focusNode.dispose();
    }

    super.dispose();
  }

  void startTimer() {
    timerSeconds = 30;
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (timerSeconds == 0) {
        timer.cancel();
      } else {
        setState(() {
          timerSeconds--;
        });
      }
    });
  }

  Future<void> sendOtp() async {
    String phoneNumber = _phoneController.text.trim();

    // Clean and validate phone number
    String cleanedNumber = _cleanPhoneNumber(phoneNumber);

    if (!_isValidPhoneNumber(cleanedNumber)) {
      String errorMessage = selectedCountryCode.code == 'IN'
          ? 'Please enter a valid 10 digit mobile number'
          : 'Please enter a valid phone number';

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(errorMessage)),
      );
      return;
    }

    // Update the text field with cleaned number
    if (cleanedNumber != phoneNumber) {
      _phoneController.removeListener(_onPhoneNumberChanged);
      _phoneController.text = cleanedNumber;
      _phoneController.addListener(_onPhoneNumberChanged);
    }

    setState(() {
      isLoading = true;
    });

    // Create full phone number with country code for logging
    String fullPhoneNumber = '${selectedCountryCode.dialCode}$cleanedNumber';
    debugPrint('📱 Sending OTP for number: $fullPhoneNumber');
    final success = await _authController.sendOtp(cleanedNumber, channel: _selectedOtpChannel);

    setState(() {
      isLoading = false;
    });

    if (success) {
      debugPrint('📱 OTP sent successfully, moving to OTP step');
      goToOtpStep();
    } else {
      debugPrint('📱 Failed to send OTP');
    }
  }

  void goToOtpStep() {
    debugPrint('📱 Transitioning to OTP step');
    setState(() {
      isOtpStep = true;
      startTimer();
    });
  }

  Future<void> _autoSendOtpForReLogin() async {
    final phoneNumber = _phoneController.text.trim();

    if (phoneNumber.isEmpty) {
      debugPrint('📱 No phone number for re-login, staying on phone input');
      return;
    }

    // Clean and validate the phone number
    String cleanedNumber = _cleanPhoneNumber(phoneNumber);

    if (!_isValidPhoneNumber(cleanedNumber)) {
      debugPrint('📱 Invalid phone number for re-login: $phoneNumber');
      return;
    }

    // Update the text field with cleaned number
    if (cleanedNumber != phoneNumber) {
      _phoneController.removeListener(_onPhoneNumberChanged);
      _phoneController.text = cleanedNumber;
      _phoneController.addListener(_onPhoneNumberChanged);
    }

    debugPrint('📱 Auto-sending OTP for re-login to: $cleanedNumber');

    setState(() {
      isLoading = true;
    });

    final success = await _authController.sendOtp(cleanedNumber);

    setState(() {
      isLoading = false;
    });

    if (success) {
      debugPrint('📱 OTP sent successfully for re-login, moving to OTP step');
      goToOtpStep();
    } else {
      debugPrint('📱 Failed to send OTP for re-login');
      // Stay on phone input screen if OTP sending fails
    }
  }

  String get otpText {
    return _currentOtp.isNotEmpty ? _currentOtp : _otpControllers.map((controller) => controller.text).join();
  }

  bool _isReLoginFlow() {
    final args = Get.arguments;
    return args?['isReLogin'] ?? false;
  }

  Future<void> verifyOtp() async {
    final otp = otpText;
    debugPrint('🔐 Attempting to verify OTP: $otp');

    if (otp.length != 6) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a valid 6 digit OTP')),
      );
      return;
    }

    setState(() {
      isLoading = true;
    });

    debugPrint('🔐 Calling AuthController.verifyOtp with: $otp');
    final success = await _authController.verifyOtp(otp);

    setState(() {
      isLoading = false;
    });

    debugPrint('🔐 OTP verification result: $success');
    // Navigation is handled by AuthController based on profile completion
  }

  Future<void> resendOtp() async {
    if (timerSeconds > 0) return;

    setState(() {
      isLoading = true;
    });

    // Use cleaned phone number for resend
    String cleanedNumber = _cleanPhoneNumber(_phoneController.text);
    final success = await _authController.sendOtp(cleanedNumber);

    setState(() {
      isLoading = false;
    });

    if (success) {
      startTimer();
    }
  }

  Widget buildPhoneInput() {
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const SizedBox(height: 40),
                Image.asset(
                  'assets/images/kk_rider_logo.png',
                  height: 120,
                ),
                const SizedBox(height: 40),
                Text(
                  _isReLoginFlow() ? 'Welcome back!' : 'Sign in to your account',
                  style: const TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  _isReLoginFlow() ? 'Please verify your identity to continue' : 'Login or create an account',
                  style: const TextStyle(fontSize: 16, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 40),
                Container(
                  height: 56,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(32),
                    color: Colors.white,
                  ),
                  child: Row(
                    children: [
                      // Country Code Picker
                      CountryCodePickerWidget(
                        onChanged: _onCountryCodeChanged,
                        initialSelection: selectedCountryCode,
                        enabled: true,
                      ),
                      Container(
                        width: 1,
                        height: 30,
                        color: Colors.grey.shade300,
                        margin: const EdgeInsets.symmetric(horizontal: 8),
                      ),
                      Expanded(
                        child: TextFormField(
                          controller: _phoneController,
                          keyboardType: TextInputType.number,
                          maxLength: selectedCountryCode.code == 'IN' ? 10 : 15,
                          style: const TextStyle(fontSize: 16),
                          decoration: const InputDecoration(
                            hintText: 'Enter your number',
                            hintStyle: TextStyle(color: Colors.grey),
                            border: InputBorder.none,
                            contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 16),
                            counterText: '', // Hide the character counter
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                const Align(
                  alignment: Alignment.centerLeft,
                  child: Padding(
                    padding: EdgeInsets.only(left: 8.0),
                    child: Text(
                      'Enter your 10 digit mobile number',
                      style: TextStyle(fontSize: 13, color: Colors.grey),
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                // // OTP Channel Selector
                // CompactOtpChannelToggle(
                //   selectedChannel: _selectedOtpChannel,
                //   onChannelChanged: (channel) {
                //     setState(() {
                //       _selectedOtpChannel = channel;
                //     });
                //   },
                // ),

                // const SizedBox(height: 16),
              ],
            ),
          ),
        ),
        // Fixed button at bottom
        Container(
          padding: const EdgeInsets.all(24.0),
          child: SizedBox(
            width: double.infinity,
            height: 50,
            child: CommonElevatedButton(
              onPressed: isLoading ? null : sendOtp,
              child: isLoading
                  ? SizedBox(
                      height: 20,
                      width: 60,
                      child: ElegantShimmer(
                        child: ElegantShimmerLine(
                          width: 60,
                          height: 16,
                        ),
                      ),
                    )
                  : const Text(
                      'Get OTP',
                      style: TextStyle(fontSize: 18, color: Colors.white),
                    ),
            ),
          ),
        ),
      ],
    );
  }

  Widget buildOtpInput() {
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const SizedBox(height: 40),
                Image.asset(
                  'assets/images/kk_rider_logo.png',
                  height: 120,
                ),
                const SizedBox(height: 40),
                const Text(
                  'Enter the OTP',
                  style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  'OTP sent to ${selectedCountryCode.dialCode}${_phoneController.text}',
                  style: const TextStyle(fontSize: 16, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),
                PinputOtpWidget(
                  length: 6,
                  fieldWidth: 48,
                  fieldHeight: 48,
                  borderColor: Colors.grey.shade300,
                  focusedBorderColor: Theme.of(context).primaryColor,
                  submittedBorderColor: Theme.of(context).primaryColor,
                  fillColor: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                  borderWidth: 1.0,
                  textStyle: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.black),
                  onChanged: (otp) {
                    debugPrint('🔐 OTP Changed: $otp');
                    setState(() {
                      _currentOtp = otp;
                    });
                    // Update the controllers for backward compatibility
                    for (int i = 0; i < 6; i++) {
                      if (i < otp.length) {
                        _otpControllers[i].text = otp[i];
                      } else {
                        _otpControllers[i].text = '';
                      }
                    }
                  },
                  onCompleted: (otp) {
                    debugPrint('🔐 OTP Completed: $otp');
                    setState(() {
                      _currentOtp = otp;
                    });
                    // Auto-verify when OTP is complete
                    verifyOtp();
                  },
                ),
                const SizedBox(height: 24),

                // Debug button for testing OTP autofill (only in debug mode)
                Center(
                  child: timerSeconds > 0
                      ? Text(
                          "Didn't get the OTP? Resend SMS in 00:${timerSeconds.toString().padLeft(2, '0')}s",
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                          textAlign: TextAlign.center,
                        )
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Text(
                              "Didn't get the OTP? ",
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey,
                              ),
                            ),
                            GestureDetector(
                              onTap: resendOtp,
                              child: const Text(
                                "Resend",
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.green,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                ),
                const SizedBox(height: 40),
                Row(
                  children: [
                    Checkbox(
                      value: whatsappChecked,
                      onChanged: (val) {
                        setState(() {
                          whatsappChecked = val ?? false;
                        });
                      },
                    ),
                    const Text('Get updated on Whatsapp'),
                  ],
                ),
                const SizedBox(height: 40),
              ],
            ),
          ),
        ),
        // Fixed button at bottom
        Container(
          padding: const EdgeInsets.all(24.0),
          child: SizedBox(
            width: double.infinity,
            height: 50,
            child: CommonElevatedButton(
              onPressed: isLoading ? null : verifyOtp,
              child: isLoading
                  ? SizedBox(
                      height: 20,
                      width: 60,
                      child: ElegantShimmer(
                        child: ElegantShimmerLine(
                          width: 60,
                          height: 16,
                        ),
                      ),
                    )
                  : const Text(
                      'Continue',
                      style: TextStyle(fontSize: 18, color: Colors.white),
                    ),
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      resizeToAvoidBottomInset: true,
      body: SafeArea(
        child: isOtpStep ? buildOtpInput() : buildPhoneInput(),
      ),
    );
  }
}
