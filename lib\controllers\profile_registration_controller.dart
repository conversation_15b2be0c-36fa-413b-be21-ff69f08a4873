import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/api_service/api_service.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import '../constants/storage_keys.dart';
import '../utils/error_handler.dart';

/// Controller to manage profile registration state and API calls
class ProfileRegistrationController extends GetxController {
  final SecureStorageService _storage = SecureStorageService.instance;

  // Observable state
  final RxBool _isLoading = false.obs;
  final RxString _currentStep = 'basic_details'.obs;
  final RxMap<String, dynamic> _profileData = <String, dynamic>{}.obs;
  final RxMap<String, dynamic> _documentPaths = <String, dynamic>{}.obs;
  final RxMap<String, dynamic> _workData = <String, dynamic>{}.obs;
  final RxString _errorMessage = ''.obs;

  // API Success tracking flags
  final RxBool _profileApiSuccess = false.obs;
  final RxBool _documentApiSuccess = false.obs;
  final RxBool _workApiSuccess = false.obs;

  // Getters
  bool get isLoading => _isLoading.value;
  String get currentStep => _currentStep.value;
  Map<String, dynamic> get profileData => _profileData;
  Map<String, dynamic> get documentPaths => _documentPaths;
  Map<String, dynamic> get workData => _workData;
  String get errorMessage => _errorMessage.value;

  // API Success getters
  bool get profileApiSuccess => _profileApiSuccess.value;
  bool get documentApiSuccess => _documentApiSuccess.value;
  bool get workApiSuccess => _workApiSuccess.value;

  /// Update profile data for a specific step
  void updateProfileData(String key, dynamic value) {
    _profileData[key] = value;
    debugPrint('📝 Updated profile data: $key = $value');
  }

  /// Update multiple profile data fields
  void updateMultipleProfileData(Map<String, dynamic> data) {
    _profileData.addAll(data);
    debugPrint('📝 Updated multiple profile data: $data');
  }

  /// Update document path
  void updateDocumentPath(String documentType, String path) {
    _documentPaths[documentType] = path;
    debugPrint('📄 Updated document path: $documentType = $path');
  }

  /// Update work data
  void updateWorkData(String key, dynamic value) {
    _workData[key] = value;
    debugPrint('💼 Updated work data: $key = $value');
    debugPrint('💼 Current work data: $_workData');
  }

  /// Set current step
  void setCurrentStep(String step) {
    _currentStep.value = step;
    debugPrint('📍 Current step: $step');
  }

  /// Clear error message
  void clearError() {
    _errorMessage.value = '';
  }

  /// Submit basic profile details (Steps 1-5: Basic details, Gender, Marital Status, Address, Bank Details)
  Future<bool> submitBasicProfile() async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      debugPrint('🚀 Submitting basic profile data: $_profileData');

      // Get mobile number from storage if not in profile data
      String mobileNumber = _profileData['mobileNumber'] ?? '';
      if (mobileNumber.isEmpty) {
        mobileNumber = await _storage.read(StorageKeys.mobileNumber) ?? '';
        debugPrint('📱 Retrieved mobile number from storage: $mobileNumber');
      }

      if (mobileNumber.isEmpty) {
        debugPrint('❌ No mobile number found for profile submission');
        _errorMessage.value = 'Mobile number not found. Please restart the registration process.';
        return false;
      }

      // Convert gender and marital status strings to integers
      int genderType = _getGenderTypeInt(_profileData['gender'] ?? '');
      int maritalStatusInt = _getMaritalStatusInt(_profileData['maritalStatus'] ?? '');

      // Combine address fields
      String fullAddress =
          '${_profileData['address'] ?? ''}, ${_profileData['city'] ?? ''}, ${_profileData['state'] ?? ''}, ${_profileData['pincode'] ?? ''}';

      // Use the new RiderRegisterationInsertProfile API directly
      final result = await ApiService.instance.profile.riderRegisterationInsertProfile(
        firstName: _profileData['firstName'] ?? '',
        lastName: _profileData['lastName'] ?? '',
        mobileNo: mobileNumber,
        emailID: _profileData['email'] ?? '',
        dob: _profileData['dob'] ?? '',
        genderType: genderType,
        maritalStatus: maritalStatusInt,
        societyName: _profileData['society'] ?? '',
        roomFlatNo: _profileData['flat'] ?? '',
        fullAddress: fullAddress,
        landmark: _profileData['landmark'] ?? '',
        bankName: _profileData['bankName'] ?? '',
        accountNumber: _profileData['accountNumber'] ?? '',
        ifscCode: _profileData['ifscCode'] ?? '',
        spouseName: _profileData['spouseName'],
        sDob: _profileData['spouseDob'],
        childName: _profileData['childName'],
        cDob: _profileData['childDob'],
        passbookImagePath: _documentPaths['passbookImage'],
      );

      if (result.isSuccess) {
        debugPrint('✅ Basic profile submission successful');
        debugPrint('📊 API Response: ${result.data}');

        // Check for specific success response
        if (result.data != null &&
            result.data['status'] == 200 &&
            result.data['msg'] == 'Record  Inserted successfully') {
          debugPrint('🎉 Step 1 Completed - Profile registered successfully!');

          // Set profile API success flag
          _profileApiSuccess.value = true;

          Get.snackbar(
            'Step 1 Completed ✅',
            'Profile registered successfully! Moving to next step.',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
            duration: const Duration(seconds: 4),
            icon: const Icon(Icons.check_circle, color: Colors.white),
          );
        } else {
          // Set profile API success flag even for partial success
          _profileApiSuccess.value = true;

          Get.snackbar(
            'Success',
            'Basic profile details saved successfully!',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );
        }
        return true;
      } else {
        _errorMessage.value = result.error ?? 'Failed to save basic profile';
        debugPrint('❌ Basic profile submission failed: ${result.error}');
        ErrorHandler.showErrorSnackbar(
          title: 'Profile Save Failed',
          message: result.error ?? 'Failed to save basic profile',
        );
        return false;
      }
    } catch (e) {
      _errorMessage.value = 'An unexpected error occurred';
      debugPrint('🚨 Basic profile submission error: $e');
      ErrorHandler.showErrorSnackbar(
        title: 'Error',
        message: 'An unexpected error occurred while saving your profile.',
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Submit document upload (Step 6: Document Upload)
  Future<bool> submitDocuments() async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      debugPrint('📄 Submitting documents: $_documentPaths');

      // Get mobile number from storage if not in profile data
      String mobileNumber = _profileData['mobileNumber'] ?? '';
      if (mobileNumber.isEmpty) {
        mobileNumber = await _storage.read(StorageKeys.mobileNumber) ?? '';
        debugPrint('📱 Retrieved mobile number from storage: $mobileNumber');
      }

      if (mobileNumber.isEmpty) {
        debugPrint('❌ No mobile number found for document submission');
        _errorMessage.value = 'Mobile number not found. Please restart the registration process.';
        return false;
      }

      // Use the new RiderRegisterationInsertDocument API directly
      final result = await ApiService.instance.profile.riderRegisterationInsertDocument(
        mobileNo: mobileNumber,
        panNo: _profileData['panNumber'] ?? '',
        dlNo: _profileData['dlNumber'] ?? '',
        panCardPhotoPath: _documentPaths['panCard'],
        dlPhotoPath: _documentPaths['drivingLicense'],
        selfImgPath: _documentPaths['selfie'],
      );

      if (result.isSuccess) {
        debugPrint('✅ Document upload successful with new API');
        debugPrint('📊 Response: ${result.data}');

        // Check for specific success response: {"status": 200, "msg": "Record  Inserted successfully"}
        if (result.data != null) {
          final responseData = result.data;
          if (responseData is Map<String, dynamic> &&
              responseData['status'] == 200 &&
              responseData['msg'] != null &&
              responseData['msg'].toString().contains('Record') &&
              responseData['msg'].toString().contains('Inserted successfully')) {
            debugPrint('🎉 Step 2 Completed - Documents uploaded successfully!');

            // Set document API success flag
            _documentApiSuccess.value = true;

            Get.snackbar(
              'Step 2 Completed ✅',
              'Documents uploaded successfully! Moving to next step.',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: Colors.green,
              colorText: Colors.white,
              duration: const Duration(seconds: 4),
              icon: const Icon(Icons.check_circle, color: Colors.white),
            );
          } else {
            Get.snackbar(
              'Success',
              'Documents uploaded successfully!',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: Colors.green,
              colorText: Colors.white,
            );
          }
        } else {
          Get.snackbar(
            'Success',
            'Documents uploaded successfully!',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );
        }
        return true;
      } else {
        _errorMessage.value = result.error ?? 'Failed to upload documents';
        debugPrint('❌ Document upload failed: ${result.error}');
        Get.snackbar(
          'Error',
          result.error ?? 'Failed to upload documents',
          snackPosition: SnackPosition.BOTTOM,
        );
        return false;
      }
    } catch (e) {
      _errorMessage.value = 'An unexpected error occurred';
      debugPrint('🚨 Document upload error: $e');
      Get.snackbar(
        'Error',
        'An unexpected error occurred',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Submit work details (Step 7: Work Details)
  Future<bool> submitWorkDetails() async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      debugPrint('💼 Submitting work details: $_workData');

      // Prepare and validate all fields
      final referBy =
          _workData['referBy']?.toString().isNotEmpty == true ? _workData['referBy'].toString() : 'FRIEND001';
      final subSlotName =
          _workData['subSlotName']?.toString().isNotEmpty == true ? _workData['subSlotName'].toString() : 'DEFAULT';
      final weekoffDays =
          _workData['weekoffDays']?.toString().isNotEmpty == true ? _workData['weekoffDays'].toString() : 'Monday';
      final dcid = _workData['dcid']?.toString() ?? '1';

      debugPrint('💼 Prepared API fields:');
      debugPrint('  referBy: $referBy');
      debugPrint('  subSlotName: $subSlotName');
      debugPrint('  weekoffDays: $weekoffDays');
      debugPrint('  dcid: $dcid');

      // Get mobile number from storage if not in profile data
      String mobileNumber = _profileData['mobileNumber'] ?? '';
      if (mobileNumber.isEmpty) {
        mobileNumber = await _storage.read(StorageKeys.mobileNumber) ?? '';
        debugPrint('📱 Retrieved mobile number from storage: $mobileNumber');
      }

      if (mobileNumber.isEmpty) {
        debugPrint('❌ No mobile number found for work details submission');
        _errorMessage.value = 'Mobile number not found. Please restart the registration process.';
        return false;
      }

      // Use the new RiderRegisterationInsertWork API directly
      final result = await ApiService.instance.profile.riderRegisterationInsertWork(
        mobileNo: mobileNumber,
        referBy: referBy,
        shiftID: _workData['shiftID'] ?? 1,
        vehicleType: _workData['vehicleType'] ?? 1,
        slotName: _workData['slotName'] ?? 1,
        subSlotName: subSlotName,
        weekoffDays: weekoffDays,
        dcid: dcid,
      );

      if (result.isSuccess) {
        debugPrint('✅ Work details API call completed');
        debugPrint('📊 Response: ${result.data}');

        // Check the actual response status from the API
        if (result.data != null) {
          final responseData = result.data;
          if (responseData is Map<String, dynamic>) {
            final status = responseData['status'];
            final message = responseData['msg'] ?? responseData['error'] ?? 'Unknown response';

            debugPrint('📊 API Response Status: $status');
            debugPrint('📊 API Response Message: $message');

            // Check for success status (200) and success message
            if (status == 200 && message.toString().contains('successfully')) {
              debugPrint('🎉 Step 3 Completed - Work details submitted successfully!');

              // Set work API success flag
              _workApiSuccess.value = true;

              Get.snackbar(
                'Step 3 Completed ✅',
                'Work details submitted successfully! Registration complete.',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Colors.green,
                colorText: Colors.white,
                duration: const Duration(seconds: 4),
                icon: const Icon(Icons.check_circle, color: Colors.white),
              );

              return true;
            } else {
              // API returned error status
              debugPrint('❌ Work details submission failed - API Status: $status');
              debugPrint('❌ Error Message: $message');

              _errorMessage.value = 'Work details submission failed: $message';

              Get.snackbar(
                'Submission Failed',
                message.toString(),
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Colors.red,
                colorText: Colors.white,
                duration: const Duration(seconds: 4),
                icon: const Icon(Icons.error, color: Colors.white),
              );

              return false;
            }
          } else {
            debugPrint('❌ Invalid response format: ${responseData.runtimeType}');
            _errorMessage.value = 'Invalid response format from server';
            return false;
          }
        } else {
          debugPrint('❌ Empty response from API');
          _errorMessage.value = 'Empty response from server';
          return false;
        }
      } else {
        _errorMessage.value = result.error ?? 'Failed to save work details';
        debugPrint('❌ Work details submission failed: ${result.error}');
        Get.snackbar(
          'Error',
          result.error ?? 'Failed to save work details',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }
    } catch (e) {
      _errorMessage.value = 'An unexpected error occurred';
      debugPrint('🚨 Work details submission error: $e');
      Get.snackbar(
        'Error',
        'An unexpected error occurred',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // Removed completeRegistration and checkRegistrationStatus methods
  // These used the deleted ProfileRegistrationService

  /// Reset all data
  void resetData() {
    _profileData.clear();
    _documentPaths.clear();
    _workData.clear();
    _errorMessage.value = '';
    _currentStep.value = 'basic_details';
    debugPrint('🔄 Profile registration data reset');
  }

  /// Convert gender string to integer for API
  int _getGenderTypeInt(String gender) {
    switch (gender.toLowerCase()) {
      case 'male':
        return 1;
      case 'female':
        return 2;
      case 'other':
        return 3;
      default:
        return 1; // Default to male
    }
  }

  /// Convert marital status string to integer for API
  int _getMaritalStatusInt(String status) {
    switch (status.toLowerCase()) {
      case 'single':
        return 1;
      case 'married':
        return 2;
      case 'divorced':
        return 3;
      case 'widowed':
        return 4;
      default:
        return 1; // Default to single
    }
  }

  // Removed test methods: testRiderRegistrationAPI, testDocumentRegistrationAPI, testWorkDetailsRegistrationAPI

  // All test methods removed - they were not needed for production
}
