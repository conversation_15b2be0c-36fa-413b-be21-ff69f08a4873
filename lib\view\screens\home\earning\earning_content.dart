import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import '../../../widgets/common/common_app_bar.dart';

class EarningContent extends StatefulWidget {
  final VoidCallback? onBackPressed;
  const EarningContent({super.key, this.onBackPressed});

  @override
  State<EarningContent> createState() => _EarningContentState();
}

class _EarningContentState extends State<EarningContent> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // if (_isLoading) {
    //   return const EarningContentShimmerLayout();
    // }

    return _buildEarningContent(context);
  }

  Widget _buildEarningContent(BuildContext context) {
    // Using ResponsiveUtils for consistent sizing
    final horizontalPadding = ResponsiveUtils.spacingM(context);
    final verticalPadding = ResponsiveUtils.height(context, 1.5);

    return Column(
      children: [
        // Common App Bar
        CommonAppBar(
          titleKey: 'earnings',
          onBackPressed: widget.onBackPressed,
        ),
        // Content
        Expanded(
          child: Container(
            color: Colors.grey.shade100,
            child: SingleChildScrollView(
              padding: EdgeInsets.all(horizontalPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // SR ID Section
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        AppStrings.get('srId'),
                        style: AppTextTheme.cardSubtitle.copyWith(
                          fontWeight: FontWeight.w500,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      TextButton(
                        onPressed: () {},
                        child: Text(
                          AppStrings.get('seeHistory'),
                          style: AppTextTheme.cardSubtitle.copyWith(
                            color: AppColors.green,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: verticalPadding * 0.8), // Reduced spacing

                  // Slidable Earning Cards
                  SizedBox(
                    height: ResponsiveUtils.height(context, 28), // Reduced from 38% to 28% of screen height
                    child: PageView.builder(
                      controller: _pageController,
                      itemCount: 3, // Number of cards
                      onPageChanged: (index) {
                        setState(() {
                          _currentPage = index;
                        });
                      },
                      itemBuilder: (context, index) {
                        return Container(
                          margin: EdgeInsets.symmetric(horizontal: ResponsiveUtils.spacingS(context)),
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                Color(0xFF2E7D32),
                                Color(0xFF4CAF50),
                                Color(0xFF66BB6A),
                              ],
                            ),
                            borderRadius:
                                BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
                          ),
                          child: Padding(
                            padding: EdgeInsets.all(ResponsiveUtils.spacingS(context)), // Responsive padding
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              mainAxisSize: MainAxisSize.max, // Use all available space
                              children: [
                                // Header Row
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Expanded(
                                      flex: 2,
                                      child: Text(
                                        AppStrings.get('totalEarnings'),
                                        style: AppTextTheme.cardSubtitle.copyWith(
                                          color: Colors.white,
                                          fontWeight: FontWeight.w500,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                    Expanded(
                                      flex: 3,
                                      child: Text(
                                        AppStrings.get('thisWeekRange'),
                                        style: AppTextTheme.cardCaption.copyWith(
                                          color: Colors.white,
                                          fontWeight: FontWeight.w400,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                        textAlign: TextAlign.end,
                                      ),
                                    ),
                                  ],
                                ),

                                // Amount
                                Flexible(
                                  child: Text(
                                    '₹720',
                                    style: AppTextTheme.cardTitle.copyWith(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),

                                // Order count
                                Flexible(
                                  child: Text(
                                    AppStrings.get('orderDeliveredCount'),
                                    style: AppTextTheme.cardCaption.copyWith(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w400,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),

                                // Get OTP Button
                                Flexible(
                                  child: SizedBox(
                                    width: double.infinity,
                                    height: ResponsiveUtils.height(context, 3.5), // 3.5% of screen height
                                    child: ElevatedButton(
                                      onPressed: () {},
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.white,
                                        foregroundColor: AppColors.green,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(
                                              ResponsiveUtils.borderRadius(context, BorderRadiusType.large)),
                                        ),
                                        padding: EdgeInsets.symmetric(
                                          vertical: ResponsiveUtils.height(context, 0.6), // 0.6% of screen height
                                        ),
                                      ),
                                      child: Text(
                                        AppStrings.get('getOtpButton'),
                                        style: AppTextTheme.cardSubtitle.copyWith(
                                          fontWeight: FontWeight.w600,
                                          color: AppColors.green,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),

                  SizedBox(height: verticalPadding * 0.8), // Reduced spacing

                  // Page Indicator
                  Padding(
                    padding: const EdgeInsets.only(bottom: 20.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: _currentPage == 0 ? AppColors.green : AppColors.green.withValues(alpha: 0.3),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: ResponsiveUtils.spacingS(context),
                            vertical: ResponsiveUtils.spacingXS(context),
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.green,
                            borderRadius:
                                BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.large)),
                          ),
                          child: Text(
                            '${_currentPage + 1}/3',
                            style: AppTextTheme.cardCaption.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                              fontFamily: 'Figtree',
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: _currentPage == 2 ? AppColors.green : AppColors.green.withValues(alpha: 0.3),
                          ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: verticalPadding * 1.5), // Reduced from 2

                  // Payouts Structure Section
                  Row(
                    children: [
                      Text(
                        AppStrings.get('payoutsStructure'),
                        style: AppTextTheme.cardTitle.copyWith(
                          fontSize: AppTextTheme.getResponsiveFontSize(context, 20), // Increased from 18
                          fontWeight: FontWeight.w600,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      const Spacer(),
                      Container(
                        width: ResponsiveUtils.width(context, 10),
                        height: ResponsiveUtils.width(context, 10),
                        decoration: BoxDecoration(
                          color: Colors.orange.shade100,
                          borderRadius:
                              BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.small)),
                        ),
                        child: Icon(
                          Icons.account_balance_wallet,
                          color: Colors.orange.shade600,
                          size: AppTextTheme.getResponsiveFontSize(context, 26), // Increased from 24
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: ResponsiveUtils.height(context, 1)), // Reduced from 0.015

                  Text(
                    AppStrings.get('getPaidEveryWeek'),
                    style: AppTextTheme.cardSubtitle.copyWith(
                      fontSize: AppTextTheme.getResponsiveFontSize(context, 16), // Increased from 14
                      color: AppColors.textSecondary,
                      fontWeight: FontWeight.w400,
                    ),
                  ),

                  SizedBox(height: verticalPadding * 1.2), // Reduced from 1.5

                  // Check Payouts History Button
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton(
                      onPressed: () {},
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: AppColors.green, width: 2),
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.extraLarge)),
                        ),
                        padding:
                            EdgeInsets.symmetric(vertical: ResponsiveUtils.height(context, 1.5)), // Reduced from 0.018
                      ),
                      child: Text(
                        AppStrings.get('checkPayoutsHistory'),
                        style: AppTextTheme.cardSubtitle.copyWith(
                          color: AppColors.green,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),

                  SizedBox(height: verticalPadding * 1.5), // Reduced from 2
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
