class DashboardStoryResponse {
  final String status;
  final String msg;
  final List<StoryBanner> result;
  final List<TopBanner> topBanner;

  DashboardStoryResponse({
    required this.status,
    required this.msg,
    required this.result,
    required this.topBanner,
  });

  factory DashboardStoryResponse.fromJson(Map<String, dynamic> json) {
    return DashboardStoryResponse(
      status: json['status'] ?? '',
      msg: json['msg'] ?? '',
      result: (json['result'] as List<dynamic>?)
              ?.map((item) => StoryBanner.fromJson(item as Map<String, dynamic>))
              .toList() ??
          [],
      topBanner: (json['topBannner'] as List<dynamic>?) // Note: API has typo "topBannner"
              ?.map((item) => TopBanner.fromJson(item as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'msg': msg,
      'result': result.map((item) => item.toJson()).toList(),
      'topBannner': topBanner.map((item) => item.toJson()).toList(),
    };
  }
}

class StoryBanner {
  final int riderMasterID;
  final String thumbnailImageURL;
  final String storyTitle;
  final int priority;
  final List<dynamic> details; // Empty array in response, keeping as dynamic for flexibility

  StoryBanner({
    required this.riderMasterID,
    required this.thumbnailImageURL,
    required this.storyTitle,
    required this.priority,
    required this.details,
  });

  factory StoryBanner.fromJson(Map<String, dynamic> json) {
    return StoryBanner(
      riderMasterID: json['riderMasterID'] ?? 0,
      thumbnailImageURL: json['thumbnailImageURL'] ?? '',
      storyTitle: json['storyTitle'] ?? '',
      priority: json['priority'] ?? 0,
      details: json['details'] ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'riderMasterID': riderMasterID,
      'thumbnailImageURL': thumbnailImageURL,
      'storyTitle': storyTitle,
      'priority': priority,
      'details': details,
    };
  }
}

class TopBanner {
  final String imageURL;

  TopBanner({
    required this.imageURL,
  });

  factory TopBanner.fromJson(Map<String, dynamic> json) {
    return TopBanner(
      imageURL: json['imageURL'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'imageURL': imageURL,
    };
  }
}
