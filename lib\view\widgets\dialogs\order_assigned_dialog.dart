import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/utils/responsive_utils.dart';
import '../../screens/orders/pickup_order_screen.dart';
import 'dart:async';

class OrderAssignedDialog extends StatefulWidget {
  final int totalOrders;
  final double totalEarning;
  final double totalDistance;
  final int saddleBags;
  final int silverBags;
  final VoidCallback? onAcceptOrder;

  const OrderAssignedDialog({
    super.key,
    this.totalOrders = 6,
    this.totalEarning = 125.0,
    this.totalDistance = 5.5,
    this.saddleBags = 9,
    this.silverBags = 10,
    this.onAcceptOrder,
  });

  /// Show the order assigned dialog
  static Future<bool?> show({
    int totalOrders = 6,
    double totalEarning = 125.0,
    double totalDistance = 5.5,
    int saddleBags = 9,
    int silverBags = 10,
    VoidCallback? onAcceptOrder,
  }) async {
    return await Get.dialog<bool>(
      OrderAssignedDialog(
        totalOrders: totalOrders,
        totalEarning: totalEarning,
        totalDistance: totalDistance,
        saddleBags: saddleBags,
        silverBags: silverBags,
        onAcceptOrder: onAcceptOrder,
      ),
      barrierDismissible: true,
    );
  }

  @override
  State<OrderAssignedDialog> createState() => _OrderAssignedDialogState();
}

class _OrderAssignedDialogState extends State<OrderAssignedDialog> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  late Timer _timer;
  int _countdown = 10; // Start from 10 seconds

  @override
  void initState() {
    super.initState();

    // Initialize animation controller for the circular progress
    _animationController = AnimationController(
      duration: Duration(seconds: 10),
      vsync: this,
    );

    _animation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.linear,
    ));

    // Start the countdown timer
    _startCountdown();

    // Start the animation
    _animationController.forward();
  }

  void _startCountdown() {
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      setState(() {
        if (_countdown > 0) {
          _countdown--;
        } else {
          _timer.cancel();
          // Auto-dismiss dialog when countdown reaches 0
          Get.back(result: false);
        }
      });
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: 390,
        height: 522,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with icon and title - EXACT MATCH
            Container(
              padding: EdgeInsets.all(16),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Grocery bag icon from assets
                  Container(
                    width: 40,
                    height: 40,
                    child: Image.asset(
                      'assets/icons/paper_bag.png',
                      width: 40,
                      height: 40,
                      fit: BoxFit.contain,
                    ),
                  ),
                  SizedBox(width: 16),
                  // Title and subtitle
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Order Assigned',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                            height: 1.25,
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          'Order is getting packed',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            color: Colors.grey.shade600,
                            height: 1.33,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Animated circular countdown timer
                  AnimatedBuilder(
                    animation: _animation,
                    builder: (context, child) {
                      return Container(
                        width: 36,
                        height: 36,
                        child: Stack(
                          children: [
                            // Background circle
                            Container(
                              width: 36,
                              height: 36,
                              decoration: BoxDecoration(
                                color: Colors.grey.shade200,
                                shape: BoxShape.circle,
                              ),
                            ),
                            // Progress circle
                            SizedBox(
                              width: 36,
                              height: 36,
                              child: CircularProgressIndicator(
                                value: _animation.value,
                                strokeWidth: 3,
                                backgroundColor: Colors.transparent,
                                valueColor: AlwaysStoppedAnimation<Color>(AppColors.green),
                              ),
                            ),
                            // Countdown number
                            Container(
                              width: 36,
                              height: 36,
                              child: Center(
                                child: Text(
                                  _countdown.toString(),
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.green,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),

            // Order details - EXACT MATCH layout
            Container(
              padding: EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                children: [
                  // First row: Total order and Total earning
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              AppStrings.get('totalOrder'),
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                                color: Colors.grey.shade600,
                                height: 1.33,
                              ),
                            ),
                            SizedBox(height: 4),
                            Text(
                              '${widget.totalOrders} ${AppStrings.get('ordersCount')}',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.black,
                                height: 1.25,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              AppStrings.get('totalEarning'),
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                                color: Colors.grey.shade600,
                                height: 1.33,
                              ),
                            ),
                            SizedBox(height: 4),
                            Text(
                              '₹${widget.totalEarning.toStringAsFixed(0)}',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.black,
                                height: 1.25,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  // Second row: Total distance and Total saddle bag
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              AppStrings.get('totalDistance'),
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                                color: Colors.grey.shade600,
                                height: 1.33,
                              ),
                            ),
                            SizedBox(height: 4),
                            Text(
                              '${widget.totalDistance.toStringAsFixed(2)}${AppStrings.get('kmUnit')}s',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.black,
                                height: 1.25,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              AppStrings.get('totalSaddleBag'),
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                                color: Colors.grey.shade600,
                                height: 1.33,
                              ),
                            ),
                            SizedBox(height: 4),
                            Text(
                              '${widget.saddleBags} ${AppStrings.get('bagsCount')}',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.black,
                                height: 1.25,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  // Third row: Total silver bag (single item)
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              AppStrings.get('totalSilverBag'),
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                                color: Colors.grey.shade600,
                                height: 1.33,
                              ),
                            ),
                            SizedBox(height: 4),
                            Text(
                              '${widget.silverBags} ${AppStrings.get('bagsCount')}',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.black,
                                height: 1.25,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Expanded(child: SizedBox()), // Empty space to maintain layout
                    ],
                  ),
                ],
              ),
            ),

            SizedBox(height: 24),

            // Bell notification icon - positioned to the right in rectangle with padding
            Container(
              padding: EdgeInsets.only(right: 24),
              child: Align(
                alignment: Alignment.centerRight,
                child: Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.notifications_active,
                    color: AppColors.green,
                    size: 24,
                  ),
                ),
              ),
            ),

            SizedBox(height: 24),

            // Accept order button
            Container(
              padding: EdgeInsets.all(ResponsiveUtils.spacingL(context)),
              child: SizedBox(
                width: double.infinity,
                height: ResponsiveUtils.height(context, 6),
                child: ElevatedButton(
                  onPressed: () {
                    Get.back(result: true);
                    widget.onAcceptOrder?.call();
                    // Navigate to Pickup Order Screen
                    Get.to(() => PickupOrderScreen(totalOrders: widget.totalOrders));
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.green,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius:
                          BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.extraLarge)),
                    ),
                  ),
                  child: Text(
                    AppStrings.get('acceptOrderAction'),
                    style: AppTextTheme.buttonLarge.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
