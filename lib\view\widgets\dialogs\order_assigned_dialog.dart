import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/utils/responsive_utils.dart';

class OrderAssignedDialog extends StatelessWidget {
  final int totalOrders;
  final double totalEarning;
  final double totalDistance;
  final int saddleBags;
  final int silverBags;
  final VoidCallback? onAcceptOrder;

  const OrderAssignedDialog({
    super.key,
    this.totalOrders = 6,
    this.totalEarning = 125.0,
    this.totalDistance = 5.5,
    this.saddleBags = 9,
    this.silverBags = 10,
    this.onAcceptOrder,
  });

  /// Show the order assigned dialog
  static Future<bool?> show({
    int totalOrders = 6,
    double totalEarning = 125.0,
    double totalDistance = 5.5,
    int saddleBags = 9,
    int silverBags = 10,
    VoidCallback? onAcceptOrder,
  }) async {
    return await Get.dialog<bool>(
      OrderAssignedDialog(
        totalOrders: totalOrders,
        totalEarning: totalEarning,
        totalDistance: totalDistance,
        saddleBags: saddleBags,
        silverBags: silverBags,
        onAcceptOrder: onAcceptOrder,
      ),
      barrierDismissible: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: ResponsiveUtils.spacingM(context)),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.large)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with icon and title - exact match
            Container(
              padding: EdgeInsets.all(16),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Order icon in rounded container
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Color(0xFFE8F5E9), // Light green background
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Icon(
                        Icons.shopping_bag_outlined,
                        color: AppColors.green,
                        size: 20,
                      ),
                    ),
                  ),
                  SizedBox(width: 12),
                  // Title and subtitle
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          AppStrings.get('orderAssigned'),
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                            height: 1.25,
                          ),
                        ),
                        SizedBox(height: 2),
                        Text(
                          AppStrings.get('orderGettingPacked'),
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: Colors.grey.shade600,
                            height: 1.33,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Green circular badge with number
                  Container(
                    width: 36,
                    height: 36,
                    decoration: BoxDecoration(
                      color: AppColors.green,
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        totalOrders.toString(),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 12),
                  // Close button
                  GestureDetector(
                    onTap: () => Get.back(result: false),
                    child: Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade300,
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.close,
                        size: 14,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Order details
            Container(
              padding: EdgeInsets.symmetric(horizontal: ResponsiveUtils.spacingL(context)),
              child: Column(
                children: [
                  _buildDetailRow(
                    context,
                    AppStrings.get('totalOrder'),
                    '$totalOrders ${AppStrings.get('ordersCount')}',
                  ),
                  SizedBox(height: ResponsiveUtils.height(context, 1)),
                  _buildDetailRow(
                    context,
                    AppStrings.get('totalEarning'),
                    '₹${totalEarning.toStringAsFixed(0)}',
                  ),
                  SizedBox(height: ResponsiveUtils.height(context, 1)),
                  _buildDetailRow(
                    context,
                    AppStrings.get('totalDistance'),
                    '${totalDistance.toStringAsFixed(1)} ${AppStrings.get('kmUnit')}',
                  ),
                  SizedBox(height: ResponsiveUtils.height(context, 1)),
                  _buildDetailRow(
                    context,
                    AppStrings.get('totalSaddleBag'),
                    '$saddleBags ${AppStrings.get('bagsCount')}',
                  ),
                  SizedBox(height: ResponsiveUtils.height(context, 1)),
                  _buildDetailRow(
                    context,
                    AppStrings.get('totalSilverBag'),
                    '$silverBags ${AppStrings.get('bagsCount')}',
                  ),
                ],
              ),
            ),

            SizedBox(height: ResponsiveUtils.height(context, 2)),

            // Bell icon
            Container(
              padding: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
              decoration: BoxDecoration(
                color: AppColors.green.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.notifications_active,
                color: AppColors.green,
                size: ResponsiveUtils.iconSize(context, IconSizeType.extraLarge),
              ),
            ),

            SizedBox(height: ResponsiveUtils.height(context, 2)),

            // Accept order button
            Container(
              padding: EdgeInsets.all(ResponsiveUtils.spacingL(context)),
              child: SizedBox(
                width: double.infinity,
                height: ResponsiveUtils.height(context, 6),
                child: ElevatedButton(
                  onPressed: () {
                    Get.back(result: true);
                    onAcceptOrder?.call();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.green,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius:
                          BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.extraLarge)),
                    ),
                  ),
                  child: Text(
                    AppStrings.get('acceptOrderAction'),
                    style: AppTextTheme.buttonLarge.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(BuildContext context, String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: AppTextTheme.cardSubtitle.copyWith(
            color: Colors.grey.shade600,
          ),
        ),
        Text(
          value,
          style: AppTextTheme.cardSubtitle.copyWith(
            color: Colors.black,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }
}
