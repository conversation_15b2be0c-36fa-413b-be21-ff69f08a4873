import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/utils/responsive_utils.dart';

class OrderAssignedDialog extends StatelessWidget {
  final int totalOrders;
  final double totalEarning;
  final double totalDistance;
  final int saddleBags;
  final int silverBags;
  final VoidCallback? onAcceptOrder;

  const OrderAssignedDialog({
    super.key,
    this.totalOrders = 6,
    this.totalEarning = 125.0,
    this.totalDistance = 5.5,
    this.saddleBags = 9,
    this.silverBags = 10,
    this.onAcceptOrder,
  });

  /// Show the order assigned dialog
  static Future<bool?> show({
    int totalOrders = 6,
    double totalEarning = 125.0,
    double totalDistance = 5.5,
    int saddleBags = 9,
    int silverBags = 10,
    VoidCallback? onAcceptOrder,
  }) async {
    return await Get.dialog<bool>(
      OrderAssignedDialog(
        totalOrders: totalOrders,
        totalEarning: totalEarning,
        totalDistance: totalDistance,
        saddleBags: saddleBags,
        silverBags: silverBags,
        onAcceptOrder: onAcceptOrder,
      ),
      barrierDismissible: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: ResponsiveUtils.spacingM(context)),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.large)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with icon and title
            Container(
              padding: EdgeInsets.all(ResponsiveUtils.spacingL(context)),
              child: Row(
                children: [
                  // Order icon
                  Container(
                    width: ResponsiveUtils.width(context, 12),
                    height: ResponsiveUtils.width(context, 12),
                    decoration: BoxDecoration(
                      color: AppColors.green.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
                    ),
                    child: Icon(
                      Icons.shopping_bag_outlined,
                      color: AppColors.green,
                      size: ResponsiveUtils.iconSize(context, IconSizeType.large),
                    ),
                  ),
                  SizedBox(width: ResponsiveUtils.spacingM(context)),
                  // Title and subtitle
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppStrings.get('orderAssigned'),
                          style: AppTextTheme.headingMedium.copyWith(
                            color: Colors.black,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: ResponsiveUtils.height(context, 0.5)),
                        Text(
                          AppStrings.get('orderGettingPacked'),
                          style: AppTextTheme.bodyMedium.copyWith(
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Number badge
                  Container(
                    width: ResponsiveUtils.width(context, 8),
                    height: ResponsiveUtils.width(context, 8),
                    decoration: BoxDecoration(
                      color: AppColors.green,
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        totalOrders.toString(),
                        style: AppTextTheme.headingMedium.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  // Close button
                  SizedBox(width: ResponsiveUtils.spacingS(context)),
                  GestureDetector(
                    onTap: () => Get.back(result: false),
                    child: Container(
                      padding: EdgeInsets.all(ResponsiveUtils.spacingXS(context)),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade200,
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.close,
                        size: ResponsiveUtils.iconSize(context, IconSizeType.medium),
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // Order details
            Container(
              padding: EdgeInsets.symmetric(horizontal: ResponsiveUtils.spacingL(context)),
              child: Column(
                children: [
                  _buildDetailRow(
                    context,
                    AppStrings.get('totalOrder'),
                    '$totalOrders ${AppStrings.get('ordersCount')}',
                  ),
                  SizedBox(height: ResponsiveUtils.height(context, 1)),
                  _buildDetailRow(
                    context,
                    AppStrings.get('totalEarning'),
                    '₹${totalEarning.toStringAsFixed(0)}',
                  ),
                  SizedBox(height: ResponsiveUtils.height(context, 1)),
                  _buildDetailRow(
                    context,
                    AppStrings.get('totalDistance'),
                    '${totalDistance.toStringAsFixed(1)} ${AppStrings.get('kmUnit')}',
                  ),
                  SizedBox(height: ResponsiveUtils.height(context, 1)),
                  _buildDetailRow(
                    context,
                    AppStrings.get('totalSaddleBag'),
                    '$saddleBags ${AppStrings.get('bagsCount')}',
                  ),
                  SizedBox(height: ResponsiveUtils.height(context, 1)),
                  _buildDetailRow(
                    context,
                    AppStrings.get('totalSilverBag'),
                    '$silverBags ${AppStrings.get('bagsCount')}',
                  ),
                ],
              ),
            ),
            
            SizedBox(height: ResponsiveUtils.height(context, 2)),
            
            // Bell icon
            Container(
              padding: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
              decoration: BoxDecoration(
                color: AppColors.green.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.notifications_active,
                color: AppColors.green,
                size: ResponsiveUtils.iconSize(context, IconSizeType.extraLarge),
              ),
            ),
            
            SizedBox(height: ResponsiveUtils.height(context, 2)),
            
            // Accept order button
            Container(
              padding: EdgeInsets.all(ResponsiveUtils.spacingL(context)),
              child: SizedBox(
                width: double.infinity,
                height: ResponsiveUtils.height(context, 6),
                child: ElevatedButton(
                  onPressed: () {
                    Get.back(result: true);
                    onAcceptOrder?.call();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.green,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.extraLarge)),
                    ),
                  ),
                  child: Text(
                    AppStrings.get('acceptOrderAction'),
                    style: AppTextTheme.buttonLarge.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(BuildContext context, String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: AppTextTheme.bodyMedium.copyWith(
            color: Colors.grey.shade600,
          ),
        ),
        Text(
          value,
          style: AppTextTheme.bodyMedium.copyWith(
            color: Colors.black,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }
}
