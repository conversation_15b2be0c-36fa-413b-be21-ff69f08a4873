/// All Services Export File
/// Single import file for all services in the app
/// Use: import 'package:kisankonnect_rider/services/all_services.dart';
library;

// =============================================================================
// API SERVICES
// =============================================================================
export 'api/api_helper.dart';
export 'api/common_data_service.dart';
export 'api/features/dashboard_service.dart';
export 'api/features/earnings_service.dart';
export 'api/features/cash_balance_service.dart';
export 'api/features/refer_earn_service.dart';
export 'api/features/break_management_service.dart';
export 'api/features/shift_status_service.dart';

// =============================================================================
// AUTHENTICATION SERVICES
// =============================================================================
export 'auth/authentication_service.dart';
export 'auth/profile_service.dart';

// =============================================================================
// STORAGE SERVICES
// =============================================================================
export 'storage/secure_storage_service.dart';

// =============================================================================
// UI SERVICES
// =============================================================================
export 'ui/themes.dart';
export 'ui/app_text_theme.dart';
export 'ui/app_strings.dart';
export 'ui/localization_service.dart';

// =============================================================================
// LOCATION SERVICES
// =============================================================================
export 'location/maps_service.dart';

// =============================================================================
// UTILITY SERVICES
// =============================================================================
export 'utils/environment_service.dart';
export 'utils/biometric_service.dart';
export 'utils/bank_verification_service.dart';
export 'utils/ifsc_verification_service.dart';
export 'utils/whatsapp_otp_service.dart';
export 'utils/rider_details_service.dart';

// =============================================================================
// MAIN API SERVICE (legacy but functional)
// =============================================================================
export 'api_service/api_service.dart';

// =============================================================================
// CONSTANTS AND MODELS (re-exported for convenience)
// =============================================================================
export '../constants/storage_keys.dart';
export '../constants/api_endpoints.dart';
export '../config/flavor_config.dart';
export '../utils/error_handler.dart';
export '../utils/responsive_utils.dart';
export '../models/rider_details_model.dart';
