import 'package:kisankonnect_rider/services/all_services.dart';

/// Simple Refer and Earn Service
class ReferEarnService {
  static ReferEarnService? _instance;
  static ReferEarnService get instance => _instance ??= ReferEarnService._();

  late ApiHelper _apiHelper;

  ReferEarnService._() {
    _apiHelper = ApiHelper.instance;
  }

  /// Get refer and earn banner data
  Future<ApiResponse<dynamic>> getReferEarnBanner({
    required String mobileNo,
    required String riderId,
  }) async {
    return await _apiHelper.get<dynamic>(
      ApiEndpoints.referEarnBanner,
      queryParameters: {
        'MobileNo': mobileNo,
        'riderId': riderId,
      },
    );
  }
}
