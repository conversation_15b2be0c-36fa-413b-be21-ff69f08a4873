import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_hi.dart';
import 'app_localizations_mr.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
      : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('hi'),
    Locale('mr')
  ];

  /// The name of the application
  ///
  /// In en, this message translates to:
  /// **'KisanKonnect Rider'**
  String get appName;

  /// Profile screen title
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profile;

  /// Default rider name
  ///
  /// In en, this message translates to:
  /// **'Rider Name'**
  String get riderName;

  /// Default phone number
  ///
  /// In en, this message translates to:
  /// **'+91- 98388 89898'**
  String get phoneNumber;

  /// ID Card menu item
  ///
  /// In en, this message translates to:
  /// **'ID Card'**
  String get idCard;

  /// Cash Balance menu item
  ///
  /// In en, this message translates to:
  /// **'Cash Balance'**
  String get cashBalance;

  /// My Shift menu item
  ///
  /// In en, this message translates to:
  /// **'My Shift'**
  String get myShift;

  /// My KFH Location menu item
  ///
  /// In en, this message translates to:
  /// **'My KFH Location'**
  String get myKFHLocation;

  /// Trip History menu item
  ///
  /// In en, this message translates to:
  /// **'Trip History'**
  String get tripHistory;

  /// Kissan Store menu item
  ///
  /// In en, this message translates to:
  /// **'Kissan Store'**
  String get kisanStore;

  /// New trends badge text
  ///
  /// In en, this message translates to:
  /// **'New trends'**
  String get newTrends;

  /// Refer & Earn menu item
  ///
  /// In en, this message translates to:
  /// **'Refer & Earn'**
  String get referAndEarn;

  /// Referral bonus subtitle text
  ///
  /// In en, this message translates to:
  /// **'10,000+ riders are earning referral bonus'**
  String get referralBonusText;

  /// Help & Support menu item
  ///
  /// In en, this message translates to:
  /// **'Help & Support'**
  String get helpAndSupport;

  /// App version text
  ///
  /// In en, this message translates to:
  /// **'App version v0.1.10'**
  String get appVersion;

  /// Featured products section title
  ///
  /// In en, this message translates to:
  /// **'Featured products'**
  String get featuredProducts;

  /// Big discount banner text
  ///
  /// In en, this message translates to:
  /// **'BIG Discount'**
  String get bigDiscount;

  /// Order now button text
  ///
  /// In en, this message translates to:
  /// **'Order Now'**
  String get orderNow;

  /// Cart review screen title
  ///
  /// In en, this message translates to:
  /// **'Cart review'**
  String get cartReview;

  /// Cart details section title
  ///
  /// In en, this message translates to:
  /// **'Cart details'**
  String get cartDetails;

  /// Wallet section title
  ///
  /// In en, this message translates to:
  /// **'Wallet'**
  String get wallet;

  /// KisanKonnect Wallet option
  ///
  /// In en, this message translates to:
  /// **'KisanKonnect Wallet'**
  String get kisanKonnectWallet;

  /// KisanKash option
  ///
  /// In en, this message translates to:
  /// **'KisanKash'**
  String get kisanKash;

  /// Balance label
  ///
  /// In en, this message translates to:
  /// **'Balance'**
  String get balance;

  /// Add button text
  ///
  /// In en, this message translates to:
  /// **'Add'**
  String get add;

  /// Redeem button text
  ///
  /// In en, this message translates to:
  /// **'Redeem'**
  String get redeem;

  /// Select payment mode section title
  ///
  /// In en, this message translates to:
  /// **'Select your payment mode'**
  String get selectPaymentMode;

  /// Cards/UPI/Netbanking payment option
  ///
  /// In en, this message translates to:
  /// **'Cards/UPI/Netbanking'**
  String get cardsUPINetbanking;

  /// Pay with UPI section title
  ///
  /// In en, this message translates to:
  /// **'Pay with UPI'**
  String get payWithUPI;

  /// UPI instruction text
  ///
  /// In en, this message translates to:
  /// **'Use any UPI app on your phone to pay'**
  String get useAnyUPIApp;

  /// Pay Now button text
  ///
  /// In en, this message translates to:
  /// **'Pay Now'**
  String get payNow;

  /// Order success message
  ///
  /// In en, this message translates to:
  /// **'Your order placed\nsuccessfully'**
  String get orderPlacedSuccessfully;

  /// Continue shopping button text
  ///
  /// In en, this message translates to:
  /// **'Continue Shopping'**
  String get continueShopping;

  /// View my orders button text
  ///
  /// In en, this message translates to:
  /// **'View My Orders'**
  String get viewMyOrders;

  /// Select address screen title
  ///
  /// In en, this message translates to:
  /// **'Select Address'**
  String get selectAddress;

  /// Select location screen title
  ///
  /// In en, this message translates to:
  /// **'Select your location'**
  String get selectYourLocation;

  /// WhatsApp updates checkbox text
  ///
  /// In en, this message translates to:
  /// **'Get updated on Whatsapp'**
  String get getUpdatedOnWhatsapp;

  /// Continue button text
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get continueButton;

  /// Filter dialog title
  ///
  /// In en, this message translates to:
  /// **'Select your preferable filter'**
  String get selectPreferableFilter;

  /// Trip ascending filter option
  ///
  /// In en, this message translates to:
  /// **'Trip in Ascending order'**
  String get tripAscending;

  /// Trip descending filter option
  ///
  /// In en, this message translates to:
  /// **'Trip in Descending order'**
  String get tripDescending;

  /// Success trip filter option
  ///
  /// In en, this message translates to:
  /// **'Only Success Trip'**
  String get onlySuccessTrip;

  /// Failed trip filter option
  ///
  /// In en, this message translates to:
  /// **'Only Failed Trip'**
  String get onlyFailedTrip;

  /// Okay button text
  ///
  /// In en, this message translates to:
  /// **'Okay'**
  String get okay;

  /// Earnings summary section title
  ///
  /// In en, this message translates to:
  /// **'My Earnings'**
  String get myEarnings;

  /// Today tab in earnings
  ///
  /// In en, this message translates to:
  /// **'Today'**
  String get today;

  /// This week tab in earnings
  ///
  /// In en, this message translates to:
  /// **'This week'**
  String get thisWeek;

  /// This month tab in earnings
  ///
  /// In en, this message translates to:
  /// **'This month'**
  String get thisMonth;

  /// Total earnings of current day
  ///
  /// In en, this message translates to:
  /// **'Total\'s earnings of the day'**
  String get totalEarningsOfTheDay;

  /// Total earnings of previous day
  ///
  /// In en, this message translates to:
  /// **'Total\'s earnings of the previous day'**
  String get totalEarningsOfThePreviousDay;

  /// Order delivered label
  ///
  /// In en, this message translates to:
  /// **'Order Delivered'**
  String get orderDelivered;

  /// Order earning label
  ///
  /// In en, this message translates to:
  /// **'Order Earning'**
  String get orderEarning;

  /// Rain surge earning label
  ///
  /// In en, this message translates to:
  /// **'Rain Surge Earning'**
  String get rainSurgeEarning;

  /// Total incentive label
  ///
  /// In en, this message translates to:
  /// **'Total Incentive'**
  String get totalIncentive;

  /// Incentive subtitle explanation
  ///
  /// In en, this message translates to:
  /// **'Applied if you work on Sat and Sun'**
  String get incentiveSubtitle;

  /// Incentive banner message
  ///
  /// In en, this message translates to:
  /// **'Do 15 more orders and earn'**
  String get doMoreOrdersAndEarn;

  /// View all earnings button text
  ///
  /// In en, this message translates to:
  /// **'View all earnings & incentives'**
  String get viewAllEarningsAndIncentives;

  /// Orders unit text
  ///
  /// In en, this message translates to:
  /// **'orders'**
  String get orders;

  /// Refer a friend and earn title
  ///
  /// In en, this message translates to:
  /// **'Refer a friend an earn'**
  String get referFriendAndEarn;

  /// Your friend gets label
  ///
  /// In en, this message translates to:
  /// **'Your friend gets'**
  String get yourFriendGets;

  /// On joining text
  ///
  /// In en, this message translates to:
  /// **' on joining!'**
  String get onJoining;

  /// Total referral earnings text
  ///
  /// In en, this message translates to:
  /// **'Total referral earnings: ₹5500'**
  String get totalReferralEarnings;

  /// Friends referred count
  ///
  /// In en, this message translates to:
  /// **'2 friends referred'**
  String get friendsReferred;

  /// Share referral code title
  ///
  /// In en, this message translates to:
  /// **'Share your referral code'**
  String get shareYourReferralCode;

  /// How it works section title
  ///
  /// In en, this message translates to:
  /// **'How it works'**
  String get howItWorks;

  /// Refer in simple steps subtitle
  ///
  /// In en, this message translates to:
  /// **'Refer in 3 simple steps'**
  String get referInSimpleSteps;

  /// Step 1 description
  ///
  /// In en, this message translates to:
  /// **'Copy Code or Share via Whatsapp'**
  String get copyCodeOrShareViaWhatsapp;

  /// Step 2 description
  ///
  /// In en, this message translates to:
  /// **'Complete the Target'**
  String get completeTheTarget;

  /// Step 3 description
  ///
  /// In en, this message translates to:
  /// **'Enjoy the Bonus'**
  String get enjoyTheBonus;

  /// Your referrals section title
  ///
  /// In en, this message translates to:
  /// **'Your referrals'**
  String get yourReferrals;

  /// Pending status
  ///
  /// In en, this message translates to:
  /// **'Pending'**
  String get pending;

  /// Success status
  ///
  /// In en, this message translates to:
  /// **'Success'**
  String get success;

  /// Failed status
  ///
  /// In en, this message translates to:
  /// **'Failed'**
  String get failed;

  /// Invite via WhatsApp button text
  ///
  /// In en, this message translates to:
  /// **'Invite via WhatsApp'**
  String get inviteViaWhatsApp;

  /// Referral code copied message
  ///
  /// In en, this message translates to:
  /// **'Referral code copied to clipboard!'**
  String get referralCodeCopied;

  /// WhatsApp share message
  ///
  /// In en, this message translates to:
  /// **'🎉 Join KisanKonnect and earn ₹10,000 on joining!\n\nUse my referral code: {code}\n\nDownload the app and start earning today!'**
  String joinKisanKonnectMessage(String code);
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en', 'hi', 'mr'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
    case 'hi':
      return AppLocalizationsHi();
    case 'mr':
      return AppLocalizationsMr();
  }

  throw FlutterError(
      'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
      'an issue with the localizations generation tool. Please file an issue '
      'on GitHub with a reproducible sample app and the gen-l10n configuration '
      'that was used.');
}
