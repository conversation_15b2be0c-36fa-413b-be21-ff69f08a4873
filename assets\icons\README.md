# Icons Directory

This directory contains custom icon assets for the KisanKonnect Rider app.

## Required Files

### working-time.png
- **Purpose**: Clock icon for shift info section header
- **Size**: 24x24px (will be scaled automatically)
- **Format**: PNG with transparency
- **Usage**: `lib/view/screens/home/<USER>/shift_info_section.dart` (line 194-198)

### timer-frame.png
- **Purpose**: Timer frame icon for work timer display
- **Size**: 24x24px
- **Format**: PNG with transparency
- **Usage**: `lib/view/screens/home/<USER>/shift_info_section.dart` (line 222-226)
- **Description**: Green circular frame with timer icon inside

## Usage

### working-time.png
Used in the shift status row header:
- Shows next to shift time (9:30 am - 6:00 pm)
- No color tinting applied

### timer-frame.png
Used in the work timer row:
- Shows next to "04:30 hr" time display
- Replaces the previous green circular background with white timer icon
- No color tinting applied (should be pre-colored)

## Adding the Images

1. Save your clock icon image as `working-time.png` in this directory
2. Save your timer frame icon as `timer-frame.png` in this directory
3. Ensure both images have transparent backgrounds
4. Images should be 24x24px for optimal display
