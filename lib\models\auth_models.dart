/// Response model for the KisanKonnect Rider Login API
class RiderLoginResponse {
  final String status;
  final String msg;
  final RiderDetail riderDetail;

  RiderLoginResponse({
    required this.status,
    required this.msg,
    required this.riderDetail,
  });

  factory RiderLoginResponse.fromJson(Map<String, dynamic> json) {
    return RiderLoginResponse(
      status: json['status']?.toString() ?? '200',
      msg: json['msg']?.toString() ?? 'Success',
      riderDetail: RiderDetail.fromJson(json['fE_RiderDetail'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'msg': msg,
      'fE_RiderDetail': riderDetail.toJson(),
    };
  }

  bool get isSuccess => status == '200';
}

/// Rider detail model containing registration status and OTP
class RiderDetail {
  final int regStatus;
  final int approvalStatus;
  final int otp;

  RiderDetail({
    required this.regStatus,
    required this.approvalStatus,
    required this.otp,
  });

  factory RiderDetail.fromJson(Map<String, dynamic> json) {
    return RiderDetail(
      regStatus: json['regStatus'] ?? 0,
      approvalStatus: json['approvalStatus'] ?? 0,
      otp: json['otp'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'regStatus': regStatus,
      'approvalStatus': approvalStatus,
      'otp': otp,
    };
  }

  /// Get registration status description
  String get regStatusDescription {
    switch (regStatus) {
      case 0:
        return 'Profile not created';
      case 1:
        return 'Name and address filled';
      case 2:
        return 'Documents uploaded';
      case 3:
        return 'Profile completed successfully';
      default:
        return 'Unknown status';
    }
  }

  /// Check if profile is complete
  bool get isProfileComplete => regStatus == 3;

  /// Check if basic info is filled
  bool get isBasicInfoFilled => regStatus >= 1;

  /// Check if documents are uploaded
  bool get isDocumentsUploaded => regStatus >= 2;
}

/// Backward compatibility alias
typedef LoginResponse = RiderLoginResponse;

/// Response model for FE_SRInfoAfterLogin API
class SRInfoAfterLoginResponse {
  final String status;
  final String msg;
  final List<LoginInfo> loginInfo;
  final List<LatLong> latLong;

  SRInfoAfterLoginResponse({
    required this.status,
    required this.msg,
    required this.loginInfo,
    required this.latLong,
  });

  factory SRInfoAfterLoginResponse.fromJson(Map<String, dynamic> json) {
    return SRInfoAfterLoginResponse(
      status: json['status']?.toString() ?? '200',
      msg: json['msg']?.toString() ?? 'Success',
      loginInfo: (json['loginInfo'] as List<dynamic>?)
              ?.map((item) => LoginInfo.fromJson(item as Map<String, dynamic>))
              .toList() ??
          [],
      latLong:
          (json['latLong'] as List<dynamic>?)?.map((item) => LatLong.fromJson(item as Map<String, dynamic>)).toList() ??
              [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'msg': msg,
      'loginInfo': loginInfo.map((item) => item.toJson()).toList(),
      'latLong': latLong.map((item) => item.toJson()).toList(),
    };
  }

  bool get isSuccess => status == '200';
  LoginInfo? get primaryLoginInfo => loginInfo.isNotEmpty ? loginInfo.first : null;
}

/// Login info model containing detailed user information
class LoginInfo {
  final int id;
  final String applicationVersionSR;
  final String areainMeter;
  final String dunzokey;
  final String dunzotoken;
  final String token;
  final String locusClient;
  final String locusUser;
  final String locuspasswrod;
  final String mobileNo;
  final String srName;
  final String userName;
  final String password;
  final String userType;
  final String downloaddate;
  final String dcid;
  final String latitude;
  final String longitude;
  final String tataToken;
  final String numberEnable;
  final String loginStatus;
  final String loginStatusDc;
  final String imagestatus;
  final int storeStatus;
  final int orderDelivery;
  final String orderAssign;
  final int kfhStatus;
  final String? weekoffDays;
  final String? srShift;
  final String dcStatus;
  final int docStatus;
  final int totalHourShift;
  final String msg;
  final String statusCode;

  LoginInfo({
    required this.id,
    required this.applicationVersionSR,
    required this.areainMeter,
    required this.dunzokey,
    required this.dunzotoken,
    required this.token,
    required this.locusClient,
    required this.locusUser,
    required this.locuspasswrod,
    required this.mobileNo,
    required this.srName,
    required this.userName,
    required this.password,
    required this.userType,
    required this.downloaddate,
    required this.dcid,
    required this.latitude,
    required this.longitude,
    required this.tataToken,
    required this.numberEnable,
    required this.loginStatus,
    required this.loginStatusDc,
    required this.imagestatus,
    required this.storeStatus,
    required this.orderDelivery,
    required this.orderAssign,
    required this.kfhStatus,
    this.weekoffDays,
    this.srShift,
    required this.dcStatus,
    required this.docStatus,
    required this.totalHourShift,
    required this.msg,
    required this.statusCode,
  });

  factory LoginInfo.fromJson(Map<String, dynamic> json) {
    return LoginInfo(
      id: json['id'] ?? 0,
      applicationVersionSR: json['applicationVersionSR']?.toString() ?? '',
      areainMeter: json['areainMeter']?.toString() ?? '',
      dunzokey: json['dunzokey']?.toString() ?? '',
      dunzotoken: json['dunzotoken']?.toString() ?? '',
      token: json['token']?.toString() ?? '',
      locusClient: json['locusClient']?.toString() ?? '',
      locusUser: json['locusUser']?.toString() ?? '',
      locuspasswrod: json['locuspasswrod']?.toString() ?? '',
      mobileNo: json['mobileNo']?.toString() ?? '',
      srName: json['srName']?.toString() ?? '',
      userName: json['userName']?.toString() ?? '',
      password: json['password']?.toString() ?? '',
      userType: json['userType']?.toString() ?? '',
      downloaddate: json['downloaddate']?.toString() ?? '',
      dcid: json['dcid']?.toString() ?? '',
      latitude: json['latitude']?.toString() ?? '',
      longitude: json['longitude']?.toString() ?? '',
      tataToken: json['tata_token']?.toString() ?? '',
      numberEnable: json['number_enable']?.toString() ?? '',
      loginStatus: json['loginStatus']?.toString() ?? '',
      loginStatusDc: json['loginStatus_dc']?.toString() ?? '',
      imagestatus: json['imagestatus']?.toString() ?? '',
      storeStatus: json['storeStatus'] ?? 0,
      orderDelivery: json['order_delivery'] ?? 0,
      orderAssign: json['order_assign']?.toString() ?? '',
      kfhStatus: json['kfhStatus'] ?? 0,
      weekoffDays: json['weekoffDays']?.toString(),
      srShift: json['srShift']?.toString(),
      dcStatus: json['dcStatus']?.toString() ?? '',
      docStatus: json['doc_status'] ?? 0,
      totalHourShift: json['total_hour_shift'] ?? 0,
      msg: json['msg']?.toString() ?? '',
      statusCode: json['statusCode']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'applicationVersionSR': applicationVersionSR,
      'areainMeter': areainMeter,
      'dunzokey': dunzokey,
      'dunzotoken': dunzotoken,
      'token': token,
      'locusClient': locusClient,
      'locusUser': locusUser,
      'locuspasswrod': locuspasswrod,
      'mobileNo': mobileNo,
      'srName': srName,
      'userName': userName,
      'password': password,
      'userType': userType,
      'downloaddate': downloaddate,
      'dcid': dcid,
      'latitude': latitude,
      'longitude': longitude,
      'tata_token': tataToken,
      'number_enable': numberEnable,
      'loginStatus': loginStatus,
      'loginStatus_dc': loginStatusDc,
      'imagestatus': imagestatus,
      'storeStatus': storeStatus,
      'order_delivery': orderDelivery,
      'order_assign': orderAssign,
      'kfhStatus': kfhStatus,
      'weekoffDays': weekoffDays,
      'srShift': srShift,
      'dcStatus': dcStatus,
      'doc_status': docStatus,
      'total_hour_shift': totalHourShift,
      'msg': msg,
      'statusCode': statusCode,
    };
  }
}

/// Location data model
class LatLong {
  final int id;
  final String type;
  final double latitude;
  final double longitude;
  final String name;

  LatLong({
    required this.id,
    required this.type,
    required this.latitude,
    required this.longitude,
    required this.name,
  });

  factory LatLong.fromJson(Map<String, dynamic> json) {
    return LatLong(
      id: json['id'] ?? 0,
      type: json['type']?.toString() ?? '',
      latitude: (json['latitude'] is String)
          ? double.tryParse(json['latitude']) ?? 0.0
          : (json['latitude']?.toDouble() ?? 0.0),
      longitude: (json['longitude'] is String)
          ? double.tryParse(json['longitude']) ?? 0.0
          : (json['longitude']?.toDouble() ?? 0.0),
      name: json['name']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'latitude': latitude,
      'longitude': longitude,
      'name': name,
    };
  }
}

/// Profile registration request model (matches Swagger RiderRegisterationInsertProfile)
class ProfileRegistrationRequest {
  final String firstName;
  final String lastName;
  final String mobileNo;
  final String emailID;
  final String dob;
  final int genderType;
  final int maritalStatus;
  final String? spouseName;
  final String? sDob;
  final String? childName;
  final String? cDob;
  final String societyName;
  final String roomFlatNo;
  final String fullAddress;
  final String landmark;
  final String bankName;
  final String accountNumber;
  final String ifscCode;

  ProfileRegistrationRequest({
    required this.firstName,
    required this.lastName,
    required this.mobileNo,
    required this.emailID,
    required this.dob,
    required this.genderType,
    required this.maritalStatus,
    this.spouseName,
    this.sDob,
    this.childName,
    this.cDob,
    required this.societyName,
    required this.roomFlatNo,
    required this.fullAddress,
    required this.landmark,
    required this.bankName,
    required this.accountNumber,
    required this.ifscCode,
  });

  /// Convert to query parameters for multipart request
  Map<String, String> toQueryParams() {
    final params = <String, String>{
      'FirstName': firstName,
      'LastName': lastName,
      'MobileNo': mobileNo,
      'EmailID': emailID,
      'DOB': dob,
      'GenderType': genderType.toString(),
      'MaritalStatus': maritalStatus.toString(),
      'SocietyName': societyName,
      'RoomFlatNo': roomFlatNo,
      'FullAddress': fullAddress,
      'Landmark': landmark,
      'BankName': bankName,
      'AccountNumber': accountNumber,
      'IFSCCode': ifscCode,
    };

    // Add optional fields if provided
    if (spouseName != null && spouseName!.isNotEmpty) {
      params['SpouseName'] = spouseName!;
    }
    if (sDob != null && sDob!.isNotEmpty) {
      params['SDob'] = sDob!;
    }
    if (childName != null && childName!.isNotEmpty) {
      params['ChildName'] = childName!;
    }
    if (cDob != null && cDob!.isNotEmpty) {
      params['CDob'] = cDob!;
    }

    return params;
  }
}

/// Work details registration request model
class WorkRegistrationRequest {
  final String mobileNo;
  final String? referBy;
  final int shiftID;
  final int vehicleType;
  final int slotName;
  final String? subSlotName;
  final String? weekoffDays;
  final int? dcid;

  WorkRegistrationRequest({
    required this.mobileNo,
    this.referBy,
    required this.shiftID,
    required this.vehicleType,
    required this.slotName,
    this.subSlotName,
    this.weekoffDays,
    this.dcid,
  });

  Map<String, dynamic> toJson() {
    return {
      'mobileNo': mobileNo,
      'referBy': referBy,
      'shiftID': shiftID,
      'vehicleType': vehicleType,
      'slotName': slotName,
      'subSlotName': subSlotName,
      'weekoffDays': weekoffDays,
      'dcid': dcid,
    };
  }
}

/// Generic API response for profile operations
class ProfileApiResponse {
  final int status;
  final String msg;

  ProfileApiResponse({
    required this.status,
    required this.msg,
  });

  factory ProfileApiResponse.fromJson(Map<String, dynamic> json) {
    return ProfileApiResponse(
      status: json['status'] ?? 200,
      msg: json['msg'] ?? 'Success',
    );
  }

  bool get isSuccess => status == 200;
}

/// Progress tracking model for server-side storage
class ProfileProgressRequest {
  final String mobileNo;
  final int currentStep;
  final Map<String, dynamic> profileData;
  final Map<String, dynamic> workData;
  final List<String> uploadedImages;

  ProfileProgressRequest({
    required this.mobileNo,
    required this.currentStep,
    required this.profileData,
    required this.workData,
    required this.uploadedImages,
  });

  Map<String, dynamic> toJson() {
    return {
      'MobileNo': mobileNo,
      'CurrentStep': currentStep,
      'ProfileData': profileData,
      'WorkData': workData,
      'UploadedImages': uploadedImages,
      'LastUpdated': DateTime.now().toIso8601String(),
    };
  }
}

/// Progress tracking response model
class ProfileProgressResponse {
  final int status;
  final String msg;
  final ProfileProgressData? data;

  ProfileProgressResponse({
    required this.status,
    required this.msg,
    this.data,
  });

  bool get isSuccess => status == 200;

  factory ProfileProgressResponse.fromJson(Map<String, dynamic> json) {
    return ProfileProgressResponse(
      status: json['status'] ?? 0,
      msg: json['msg'] ?? '',
      data: json['data'] != null ? ProfileProgressData.fromJson(json['data']) : null,
    );
  }
}

/// Profile progress data model
class ProfileProgressData {
  final int currentStep;
  final Map<String, dynamic> profileData;
  final Map<String, dynamic> workData;
  final List<String> uploadedImages;
  final String lastUpdated;

  ProfileProgressData({
    required this.currentStep,
    required this.profileData,
    required this.workData,
    required this.uploadedImages,
    required this.lastUpdated,
  });

  factory ProfileProgressData.fromJson(Map<String, dynamic> json) {
    return ProfileProgressData(
      currentStep: json['CurrentStep'] ?? 0,
      profileData: Map<String, dynamic>.from(json['ProfileData'] ?? {}),
      workData: Map<String, dynamic>.from(json['WorkData'] ?? {}),
      uploadedImages: List<String>.from(json['UploadedImages'] ?? []),
      lastUpdated: json['LastUpdated'] ?? '',
    );
  }
}

class UserProfileResponse {
  final bool success;
  final String message;
  final UserProfile? profile;
  final bool isProfileComplete;
  final String? token;

  UserProfileResponse({
    required this.success,
    required this.message,
    this.profile,
    required this.isProfileComplete,
    this.token,
  });

  factory UserProfileResponse.fromJson(Map<String, dynamic> json) {
    return UserProfileResponse(
      success: json['success'] ?? true,
      message: json['message'] ?? 'Login successful',
      profile: json['profile'] != null ? UserProfile.fromJson(json['profile']) : null,
      isProfileComplete: json['isProfileComplete'] ?? false,
      token: json['token']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'profile': profile?.toJson(),
      'isProfileComplete': isProfileComplete,
      'token': token,
    };
  }
}

class UserProfile {
  final String? id;
  final String? name;
  final String? email;
  final String mobileNumber;
  final String? profileImage;
  final String? address;
  final String? city;
  final String? state;
  final String? pincode;
  final String? aadharNumber;
  final String? panNumber;
  final String? drivingLicense;
  final String? vehicleNumber;
  final String? vehicleType;
  final bool isActive;
  final bool isVerified;
  final int regStatus;
  final int approvalStatus;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  UserProfile({
    this.id,
    this.name,
    this.email,
    required this.mobileNumber,
    this.profileImage,
    this.address,
    this.city,
    this.state,
    this.pincode,
    this.aadharNumber,
    this.panNumber,
    this.drivingLicense,
    this.vehicleNumber,
    this.vehicleType,
    this.isActive = true,
    this.isVerified = false,
    this.regStatus = 0,
    this.approvalStatus = 0,
    this.createdAt,
    this.updatedAt,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      id: json['id']?.toString(),
      name: json['name']?.toString(),
      email: json['email']?.toString(),
      mobileNumber: json['mobileNumber']?.toString() ?? json['mobile']?.toString() ?? '',
      profileImage: json['profileImage']?.toString(),
      address: json['address']?.toString(),
      city: json['city']?.toString(),
      state: json['state']?.toString(),
      pincode: json['pincode']?.toString(),
      aadharNumber: json['aadharNumber']?.toString(),
      panNumber: json['panNumber']?.toString(),
      drivingLicense: json['drivingLicense']?.toString(),
      vehicleNumber: json['vehicleNumber']?.toString(),
      vehicleType: json['vehicleType']?.toString(),
      isActive: json['isActive'] ?? true,
      isVerified: json['isVerified'] ?? false,
      regStatus: json['regStatus'] ?? 0,
      approvalStatus: json['approvalStatus'] ?? 0,
      createdAt: json['createdAt'] != null ? DateTime.tryParse(json['createdAt']) : null,
      updatedAt: json['updatedAt'] != null ? DateTime.tryParse(json['updatedAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'mobileNumber': mobileNumber,
      'profileImage': profileImage,
      'address': address,
      'city': city,
      'state': state,
      'pincode': pincode,
      'aadharNumber': aadharNumber,
      'panNumber': panNumber,
      'drivingLicense': drivingLicense,
      'vehicleNumber': vehicleNumber,
      'vehicleType': vehicleType,
      'isActive': isActive,
      'isVerified': isVerified,
      'regStatus': regStatus,
      'approvalStatus': approvalStatus,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  UserProfile copyWith({
    String? id,
    String? name,
    String? email,
    String? mobileNumber,
    String? profileImage,
    String? address,
    String? city,
    String? state,
    String? pincode,
    String? aadharNumber,
    String? panNumber,
    String? drivingLicense,
    String? vehicleNumber,
    String? vehicleType,
    bool? isActive,
    bool? isVerified,
    int? regStatus,
    int? approvalStatus,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserProfile(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      mobileNumber: mobileNumber ?? this.mobileNumber,
      profileImage: profileImage ?? this.profileImage,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      pincode: pincode ?? this.pincode,
      aadharNumber: aadharNumber ?? this.aadharNumber,
      panNumber: panNumber ?? this.panNumber,
      drivingLicense: drivingLicense ?? this.drivingLicense,
      vehicleNumber: vehicleNumber ?? this.vehicleNumber,
      vehicleType: vehicleType ?? this.vehicleType,
      isActive: isActive ?? this.isActive,
      isVerified: isVerified ?? this.isVerified,
      regStatus: regStatus ?? this.regStatus,
      approvalStatus: approvalStatus ?? this.approvalStatus,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  bool get isProfileComplete {
    // Use regStatus from API response
    return regStatus == 3;
  }

  /// Get registration status description
  String get regStatusDescription {
    switch (regStatus) {
      case 0:
        return 'Profile not created';
      case 1:
        return 'Name and address filled';
      case 2:
        return 'Documents uploaded';
      case 3:
        return 'Profile completed successfully';
      default:
        return 'Unknown status';
    }
  }

  /// Check if basic info is filled
  bool get isBasicInfoFilled => regStatus >= 1;

  /// Check if documents are uploaded
  bool get isDocumentsUploaded => regStatus >= 2;
}

class AuthState {
  final bool isLoggedIn;
  final UserProfile? user;
  final String? token;
  final bool isLoading;
  final String? error;

  AuthState({
    this.isLoggedIn = false,
    this.user,
    this.token,
    this.isLoading = false,
    this.error,
  });

  AuthState copyWith({
    bool? isLoggedIn,
    UserProfile? user,
    String? token,
    bool? isLoading,
    String? error,
  }) {
    return AuthState(
      isLoggedIn: isLoggedIn ?? this.isLoggedIn,
      user: user ?? this.user,
      token: token ?? this.token,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}
