import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/bindings/app_bindings.dart';
import 'package:kisankonnect_rider/l10n/app_localizations.dart';
import 'package:kisankonnect_rider/routes/app_pages.dart';

import 'package:kisankonnect_rider/config/flavor_config.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:device_preview/device_preview.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize development environment (required for ApiService)
  await Environment.init(flavor: Flavor.development);

  // Initialize all dependencies using centralized bindings
  InitialBindings().dependencies();

  // Initialize Maps Service
  await MapsService.instance.initialize();

  // Run the app with Device Preview for responsive testing
  runApp(
    DevicePreview(
      enabled: !kReleaseMode, // Enable only in debug mode
      builder: (context) => const MyDevApp(),
    ),
  );
}

class MyDevApp extends StatefulWidget {
  const MyDevApp({super.key});

  @override
  State<MyDevApp> createState() => _MyDevAppState();
}

class _MyDevAppState extends State<MyDevApp> {
  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'KisanKonnect Rider - Dev Mode',
      debugShowCheckedModeBanner: false,
      theme: appTheme,
      initialRoute: AppRoutes.splash,
      getPages: AppPages.pages,

      // DevicePreview integration
      useInheritedMediaQuery: true,
      locale: DevicePreview.locale(context) ?? Get.find<LocalizationService>().locale,
      builder: DevicePreview.appBuilder,

      // Localization
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: LocalizationService.supportedLocales,
      fallbackLocale: LocalizationService.fallbackLocale,
    );
  }
}
