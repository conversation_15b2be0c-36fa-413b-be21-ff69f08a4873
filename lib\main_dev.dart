
import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/flavors.dart';
import 'package:kisankonnect_rider/view/screens/splash/splash_screen.dart';

void main() async {
  FlavorSettings.setDevSettings();
  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: SplashScreen(),
    );
  }
}

