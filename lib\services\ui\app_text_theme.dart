import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../all_services.dart';

class AppTextTheme {
  // Private constructor to prevent instantiation
  AppTextTheme._();

  // Using Figtree font from Google Fonts

  // Base font sizes
  static const double _fontSizeXS = 10.0;
  static const double _fontSizeS = 12.0;
  static const double _fontSizeM = 14.0;
  static const double _fontSizeL = 16.0;
  static const double _fontSizeXL = 18.0;
  static const double _fontSizeXXL = 20.0;
  static const double _fontSizeXXXL = 22.0;
  static const double _fontSizeHuge = 24.0;
  static const double _fontSizeGiant = 28.0;
  static const double _fontSizeMassive = 32.0;

  // Font weights
  static const FontWeight _light = FontWeight.w300;
  static const FontWeight _regular = FontWeight.w400;
  static const FontWeight _medium = FontWeight.w500;
  static const FontWeight _semiBold = FontWeight.w600;
  static const FontWeight _bold = FontWeight.w700;
  static const FontWeight _extraBold = FontWeight.w800;

  // Line heights
  static const double _lineHeightTight = 1.2;
  static const double _lineHeightNormal = 1.4;
  static const double _lineHeightRelaxed = 1.6;

  /// Main TextTheme for the app using Figtree font from Google Fonts
  static TextTheme get textTheme => TextTheme(
        // Display styles (largest text)
        displayLarge: GoogleFonts.figtree(
          fontSize: _fontSizeMassive,
          fontWeight: _extraBold,
          color: AppColors.textPrimary,
          height: _lineHeightTight,
        ),
        displayMedium: GoogleFonts.figtree(
          fontSize: _fontSizeGiant,
          fontWeight: _bold,
          color: AppColors.textPrimary,
          height: _lineHeightTight,
        ),
        displaySmall: GoogleFonts.figtree(
          fontSize: _fontSizeHuge,
          fontWeight: _bold,
          color: AppColors.textPrimary,
          height: _lineHeightTight,
        ),

        // Headline styles
        headlineLarge: GoogleFonts.figtree(
          fontSize: _fontSizeXXXL,
          fontWeight: _bold,
          color: AppColors.textPrimary,
          height: _lineHeightNormal,
        ),
        headlineMedium: GoogleFonts.figtree(
          fontSize: _fontSizeXXL,
          fontWeight: _semiBold,
          color: AppColors.textPrimary,
          height: _lineHeightNormal,
        ),
        headlineSmall: GoogleFonts.figtree(
          fontSize: _fontSizeXL,
          fontWeight: _semiBold,
          color: AppColors.textPrimary,
          height: _lineHeightNormal,
        ),

        // Title styles
        titleLarge: GoogleFonts.figtree(
          fontSize: _fontSizeXL,
          fontWeight: _semiBold,
          color: AppColors.textPrimary,
          height: _lineHeightNormal,
        ),
        titleMedium: GoogleFonts.figtree(
          fontSize: _fontSizeL,
          fontWeight: _medium,
          color: AppColors.textPrimary,
          height: _lineHeightNormal,
        ),
        titleSmall: GoogleFonts.figtree(
          fontSize: _fontSizeM,
          fontWeight: _medium,
          color: AppColors.textPrimary,
          height: _lineHeightNormal,
        ),

        // Body styles
        bodyLarge: GoogleFonts.figtree(
          fontSize: _fontSizeL,
          fontWeight: _regular,
          color: AppColors.textPrimary,
          height: _lineHeightNormal,
        ),
        bodyMedium: GoogleFonts.figtree(
          fontSize: _fontSizeM,
          fontWeight: _regular,
          color: AppColors.textPrimary,
          height: _lineHeightNormal,
        ),
        bodySmall: GoogleFonts.figtree(
          fontSize: _fontSizeS,
          fontWeight: _regular,
          color: AppColors.textSecondary,
          height: _lineHeightNormal,
        ),

        // Label styles
        labelLarge: GoogleFonts.figtree(
          fontSize: _fontSizeM,
          fontWeight: _medium,
          color: AppColors.textPrimary,
          height: _lineHeightNormal,
        ),
        labelMedium: GoogleFonts.figtree(
          fontSize: _fontSizeS,
          fontWeight: _medium,
          color: AppColors.textPrimary,
          height: _lineHeightNormal,
        ),
        labelSmall: GoogleFonts.figtree(
          fontSize: _fontSizeXS,
          fontWeight: _medium,
          color: AppColors.textSecondary,
          height: _lineHeightNormal,
        ),
      );

  // Custom text styles for specific use cases

  /// App Bar title style
  static TextStyle appBarTitle = GoogleFonts.figtree(
    fontSize: _fontSizeXL,
    fontWeight: _semiBold,
    color: Colors.white,
    height: _lineHeightNormal,
  );

  /// Button text styles
  static TextStyle buttonLarge = GoogleFonts.figtree(
    fontSize: _fontSizeL,
    fontWeight: _semiBold,
    color: Colors.white,
    height: _lineHeightNormal,
  );

  static TextStyle buttonMedium = GoogleFonts.figtree(
    fontSize: _fontSizeM,
    fontWeight: _medium,
    color: Colors.white,
    height: _lineHeightNormal,
  );

  static TextStyle buttonSmall = GoogleFonts.figtree(
    fontSize: _fontSizeS,
    fontWeight: _medium,
    color: Colors.white,
    height: _lineHeightNormal,
  );

  /// Input field styles
  static TextStyle inputText = GoogleFonts.figtree(
    fontSize: _fontSizeL,
    fontWeight: _regular,
    color: AppColors.textPrimary,
    height: _lineHeightNormal,
  );

  static TextStyle inputHint = GoogleFonts.figtree(
    fontSize: _fontSizeL,
    fontWeight: _regular,
    color: AppColors.textLight,
    height: _lineHeightNormal,
  );

  static TextStyle inputLabel = GoogleFonts.figtree(
    fontSize: _fontSizeS,
    fontWeight: _medium,
    color: AppColors.textSecondary,
    height: _lineHeightNormal,
  );

  /// Card and list item styles
  static TextStyle cardTitle = GoogleFonts.figtree(
    fontSize: _fontSizeL,
    fontWeight: _semiBold,
    color: AppColors.textPrimary,
    height: _lineHeightNormal,
  );

  static TextStyle cardSubtitle = GoogleFonts.figtree(
    fontSize: _fontSizeM,
    fontWeight: _regular,
    color: AppColors.textSecondary,
    height: _lineHeightNormal,
  );

  static TextStyle cardCaption = GoogleFonts.figtree(
    fontSize: _fontSizeS,
    fontWeight: _regular,
    color: AppColors.textLight,
    height: _lineHeightNormal,
  );

  /// Price and amount styles
  static TextStyle priceAmount = GoogleFonts.figtree(
    fontSize: _fontSizeL,
    fontWeight: _bold,
    color: AppColors.green,
    height: _lineHeightNormal,
  );

  static TextStyle priceLarge = GoogleFonts.figtree(
    fontSize: _fontSizeXL,
    fontWeight: _bold,
    color: AppColors.green,
    height: _lineHeightNormal,
  );

  /// Status text styles
  static TextStyle statusSuccess = GoogleFonts.figtree(
    fontSize: _fontSizeS,
    fontWeight: _medium,
    color: AppColors.success,
    height: _lineHeightNormal,
  );

  static TextStyle statusError = GoogleFonts.figtree(
    fontSize: _fontSizeS,
    fontWeight: _medium,
    color: AppColors.error,
    height: _lineHeightNormal,
  );

  static TextStyle statusWarning = GoogleFonts.figtree(
    fontSize: _fontSizeS,
    fontWeight: _medium,
    color: AppColors.warning,
    height: _lineHeightNormal,
  );

  /// Link styles
  static TextStyle link = GoogleFonts.figtree(
    fontSize: _fontSizeM,
    fontWeight: _medium,
    color: AppColors.green,
    decoration: TextDecoration.underline,
    height: _lineHeightNormal,
  );

  /// OTP input style
  static TextStyle otpInput = GoogleFonts.figtree(
    fontSize: _fontSizeXL,
    fontWeight: _bold,
    color: AppColors.textPrimary,
    height: _lineHeightNormal,
  );

  /// Helper method to get responsive font size
  /// @deprecated Use ResponsiveUtils.fontSize() instead
  static double getResponsiveFontSize(BuildContext context, double baseFontSize) {
    return ResponsiveUtils.fontSize(context, baseFontSize);
  }

  /// Helper method to create responsive text style
  static TextStyle responsive(BuildContext context, TextStyle baseStyle) {
    return baseStyle.copyWith(
      fontSize: ResponsiveUtils.fontSize(context, baseStyle.fontSize ?? _fontSizeM),
    );
  }
}
