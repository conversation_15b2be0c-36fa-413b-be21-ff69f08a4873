import 'package:flutter/material.dart';
import 'package:get/get.dart';

// Controllers
import '../controllers/auth_controller.dart';
import '../controllers/profile_controller.dart';
import '../controllers/profile_registration_controller.dart';
import '../controllers/dashboard_story_controller.dart';
import '../controllers/reports_controller.dart';
import '../controllers/rider_status_controller.dart';
import '../controllers/earnings_controller.dart';
import '../controllers/cash_balance_controller.dart';
import '../controllers/common_data_controller.dart';
import '../controllers/shift_controller.dart';
import '../controllers/refer_earn_controller.dart';

// Services (using all_services.dart)
import '../services/all_services.dart';

/// Centralized dependency injection for the entire app
class AppBindings extends Bindings {
  @override
  void dependencies() {
    // Core services
    Get.put<LocalizationService>(LocalizationService(), permanent: true);
    Get.put<SecureStorageService>(SecureStorageService.instance, permanent: true);
    Get.put<EnvironmentService>(EnvironmentService.instance, permanent: true);
    Get.put<ApiService>(ApiService.instance, permanent: true); // Main API service

    // All services are now available through all_services.dart

    // Controllers
    Get.put<AuthController>(AuthController(), permanent: true);
    Get.put<CommonDataController>(CommonDataController(), permanent: true);
    Get.put<ProfileController>(ProfileController(), permanent: true);
    Get.lazyPut<ProfileRegistrationController>(() => ProfileRegistrationController());
    Get.put<DashboardStoryController>(DashboardStoryController(), permanent: true);
    Get.put<ReportsController>(ReportsController(), permanent: true);
    Get.put<RiderStatusController>(RiderStatusController(), permanent: true);
    Get.put<EarningsController>(EarningsController(), permanent: true);
    Get.put<CashBalanceController>(CashBalanceController(), permanent: true);
    Get.put<ShiftController>(ShiftController(), permanent: true);
    Get.lazyPut<ReferEarnController>(() => ReferEarnController());

    debugPrint('✅ AppBindings: All dependencies initialized');
  }
}

/// Initial bindings for app startup
class InitialBindings extends Bindings {
  @override
  void dependencies() {
    Get.put<LocalizationService>(LocalizationService(), permanent: true);
    Get.put<AuthController>(AuthController(), permanent: true);
    Get.put<CommonDataController>(CommonDataController(), permanent: true);
    Get.put<ProfileController>(ProfileController(), permanent: true);
    debugPrint('✅ InitialBindings: Essential dependencies initialized');
  }
}

/// Profile bindings for create profile screen
class ProfileBindings extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<ProfileController>(() => ProfileController());
    Get.lazyPut<ProfileRegistrationController>(() => ProfileRegistrationController());

    // Add CommonDataController for work details and bank details
    if (!Get.isRegistered<CommonDataController>()) {
      Get.put<CommonDataController>(CommonDataController(), permanent: true);
    }

    debugPrint('✅ ProfileBindings: Profile controllers initialized');
  }
}
