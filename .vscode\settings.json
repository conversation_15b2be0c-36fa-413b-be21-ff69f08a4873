{
   "workbench.colorCustomizations": {
      "activityBar.activeBackground": "#65c89b",
      "activityBar.background": "#65c89b",
      "activityBar.foreground": "#15202b",
      "activityBar.inactiveForeground": "#15202b99",
      "activityBarBadge.background": "#945bc4",
      "activityBarBadge.foreground": "#e7e7e7",
      "commandCenter.border": "#15202b99",
      "sash.hoverBorder": "#65c89b",
      "statusBar.background": "#42b883",
      "statusBar.foreground": "#15202b",
      "statusBarItem.hoverBackground": "#359268",
      "statusBarItem.remoteBackground": "#42b883",
      "statusBarItem.remoteForeground": "#15202b",
      "titleBar.activeBackground": "#42b883",
      "titleBar.activeForeground": "#15202b",
      "titleBar.inactiveBackground": "#42b88399",
      "titleBar.inactiveForeground": "#15202b99"
   },
   "peacock.color": "#42b883",

   // Flutter/Dart Analysis Settings
   "dart.analysisServerFolding": false,
   "dart.showLintNames": true,
   "dart.showTodos": true,
   "dart.analysisExcludedFolders": [],
   "dart.enableSdkFormatter": true,
   "dart.lineLength": 120,
   "dart.insertArgumentPlaceholders": false,
   "dart.previewFlutterUiGuides": true,
   "dart.previewFlutterUiGuidesCustomTracking": true,

   // Error Display Settings
   "problems.showCurrentInStatus": true,
   "problems.sortOrder": "severity",
   "editor.showUnused": true,
   "editor.showDeprecated": true,
   "editor.renderWhitespace": "boundary",
   "editor.rulers": [80, 120],

   // Flutter Specific
   "flutter.checkForSdkUpdates": true,
   "flutter.experimentalRefactors": true,
   "flutter.showStructuredErrors": true,

   // File Associations
   "[dart]": {
      "editor.formatOnSave": true,
      "editor.formatOnType": true,
      "editor.rulers": [80],
      "editor.selectionHighlight": false,
      "editor.suggest.snippetsPreventQuickSuggestions": false,
      "editor.suggestSelection": "first",
      "editor.tabCompletion": "onlySnippets",
      "editor.wordBasedSuggestions": "off"
   }
}