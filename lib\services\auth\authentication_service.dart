import 'package:flutter/foundation.dart';
import '../../models/auth_models.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

/// Simple Authentication Service
class AuthenticationService {
  static AuthenticationService? _instance;
  static AuthenticationService get instance => _instance ??= AuthenticationService._();

  late ApiHelper _apiHelper;

  AuthenticationService._() {
    _apiHelper = ApiHelper.instance;
  }

  /// Send OTP via SMS (existing method)
  Future<ApiResponse<LoginResponse>> sendOtp(String mobileNumber) async {
    return await sendOtpViaChannel(mobileNumber, 'sms');
  }

  /// Send OTP via WhatsApp
  Future<ApiResponse<LoginResponse>> sendWhatsAppOtp(String mobileNumber) async {
    return await sendOtpViaChannel(mobileNumber, 'whatsapp');
  }

  /// Send OTP via specified channel (SMS or WhatsApp)
  Future<ApiResponse<LoginResponse>> sendOtpViaChannel(
    String mobileNumber,
    String channel,
  ) async {
    try {
      // First, get OTP from the backend
      final otpResponse = await _apiHelper.get<dynamic>(
        ApiEndpoints.riderLogin,
        queryParameters: {
          'MobileNo': mobileNumber,
          'Channel': channel, // 'sms' or 'whatsapp'
        },
      );

      if (otpResponse.isSuccess) {
        final loginResponse = LoginResponse.fromJson(otpResponse.data);

        // If channel is WhatsApp, send via WhatsApp service
        if (channel == 'whatsapp') {
          final whatsappService = WhatsAppOtpService.instance;
          final whatsappResult = await whatsappService.sendWhatsAppOtp(
            phoneNumber: mobileNumber,
            otp: loginResponse.riderDetail.otp.toString(),
          );

          if (!whatsappResult.isSuccess) {
            return ApiResponse.error('Failed to send WhatsApp OTP: ${whatsappResult.error}');
          }
        }

        return ApiResponse.success(loginResponse);
      } else {
        return ApiResponse.error(otpResponse.error ?? 'Failed to send OTP');
      }
    } catch (e) {
      return ApiResponse.error('Failed to send OTP: $e');
    }
  }

  /// Verify OTP using the same login API (FE_RidersLoginNewV1)
  Future<ApiResponse<LoginResponse>> verifyOtp({
    required String mobileNumber,
    required String otp,
    String? gcmId,
    String? imei,
  }) async {
    try {
      final response = await _apiHelper.get<dynamic>(
        ApiEndpoints.riderLogin, // Use same login API for verification
        queryParameters: {
          'MobileNo': mobileNumber,
          'OTP': int.tryParse(otp) ?? 0,
          'GCMID': gcmId ?? '',
          'IMEI': imei ?? _apiHelper.deviceId ?? 'unknown',
        },
      );

      if (response.isSuccess) {
        final loginResponse = LoginResponse.fromJson(response.data);
        return ApiResponse.success(loginResponse);
      } else {
        return ApiResponse.error(response.error ?? 'Failed to verify OTP');
      }
    } catch (e) {
      return ApiResponse.error('Failed to verify OTP: $e');
    }
  }

  // Removed redundant methods: getRiderProfileDetails and getRiderInfoAfterLogin

  /// Get detailed rider info after login using FE_SRInfoAfterLogin API
  Future<ApiResponse<SRInfoAfterLoginResponse>> getRiderInfoAfterLoginDetailed({
    required String mobileNo,
    required String otp,
    String? gcmId,
    String? imei,
  }) async {
    try {
      debugPrint('🔄 Calling FE_SRInfoAfterLogin API');
      debugPrint('📱 Mobile: $mobileNo, OTP: $otp');

      final response = await _apiHelper.get<dynamic>(
        ApiEndpoints.userInfoAfterLogin, // Use the constant from ApiEndpoints
        queryParameters: {
          'MobileNo': mobileNo,
          'OTP': int.tryParse(otp) ?? 0,
          'GCMID': gcmId ?? '',
          'IMEI': imei ?? _apiHelper.deviceId ?? '',
        },
      );

      if (response.isSuccess && response.data != null) {
        debugPrint('✅ FE_SRInfoAfterLogin API response received');
        final srInfoResponse = SRInfoAfterLoginResponse.fromJson(response.data);

        if (srInfoResponse.isSuccess) {
          debugPrint('✅ SRInfoAfterLogin parsed successfully');
          debugPrint('📊 Login info count: ${srInfoResponse.loginInfo.length}');
          debugPrint('📍 Location data count: ${srInfoResponse.latLong.length}');

          return ApiResponse.success(srInfoResponse);
        } else {
          debugPrint('❌ SRInfoAfterLogin API returned error: ${srInfoResponse.msg}');
          return ApiResponse.error(srInfoResponse.msg);
        }
      } else {
        debugPrint('❌ FE_SRInfoAfterLogin API call failed: ${response.error}');
        return ApiResponse.error(response.error ?? 'Failed to get detailed rider info after login');
      }
    } catch (e) {
      debugPrint('🚨 Exception in getRiderInfoAfterLoginDetailed: $e');
      return ApiResponse.error('Failed to get detailed rider info: $e');
    }
  }

  /// Fetch and store detailed rider information after successful login
  /// This method should be called after OTP verification to get complete user data
  Future<ApiResponse<bool>> fetchAndStoreRiderInfoAfterLogin({
    required String mobileNo,
    required String otp,
    String? gcmId,
    String? imei,
  }) async {
    try {
      debugPrint('🔄 Fetching and storing rider info after login');

      // Call the FE_SRInfoAfterLogin API
      final response = await getRiderInfoAfterLoginDetailed(
        mobileNo: mobileNo,
        otp: otp,
        gcmId: gcmId,
        imei: imei,
      );

      if (response.isSuccess && response.data != null) {
        final srInfo = response.data!;

        // Extract and store important user data from the first login info
        if (srInfo.loginInfo.isNotEmpty) {
          final primaryLoginInfo = srInfo.primaryLoginInfo!;

          // Store essential user data in local storage
          await _storeUserDataFromSRInfo(primaryLoginInfo);

          debugPrint('✅ Rider info fetched and stored successfully');
          debugPrint('👤 User ID: ${primaryLoginInfo.id}');
          debugPrint('👤 User Name: ${primaryLoginInfo.srName}');
          debugPrint('🏢 DC ID: ${primaryLoginInfo.dcid}');
          debugPrint('📱 Mobile: ${primaryLoginInfo.mobileNo}');

          return ApiResponse.success(true);
        } else {
          debugPrint('❌ No login info found in response');
          return ApiResponse.error('No user data found in response');
        }
      } else {
        debugPrint('❌ Failed to fetch rider info: ${response.error}');
        return ApiResponse.error(response.error ?? 'Failed to fetch rider info');
      }
    } catch (e) {
      debugPrint('🚨 Exception in fetchAndStoreRiderInfoAfterLogin: $e');
      return ApiResponse.error('Failed to fetch and store rider info: $e');
    }
  }

  /// Store user data from SRInfoAfterLogin response
  Future<void> _storeUserDataFromSRInfo(LoginInfo loginInfo) async {
    try {
      final storage = SecureStorageService.instance;

      // Store essential user data using StorageKeys constants
      await storage.write(StorageKeys.riderId, loginInfo.id.toString());
      await storage.write(StorageKeys.riderName, loginInfo.srName);
      await storage.write(StorageKeys.userName, loginInfo.userName);
      await storage.write(StorageKeys.dcId, loginInfo.dcid);
      await storage.write(StorageKeys.mobileNumber, loginInfo.mobileNo);
      await storage.write(StorageKeys.kfhStatus, loginInfo.kfhStatus.toString());
      await storage.write(StorageKeys.userType, loginInfo.userType);
      await storage.write(StorageKeys.apiToken, loginInfo.token);
      await storage.write(StorageKeys.dunzoKey, loginInfo.dunzokey);
      await storage.write(StorageKeys.dunzoToken, loginInfo.dunzotoken);
      await storage.write(StorageKeys.tataToken, loginInfo.tataToken);
      await storage.write(StorageKeys.locusClient, loginInfo.locusClient);
      await storage.write(StorageKeys.locusUser, loginInfo.locusUser);
      await storage.write(StorageKeys.locusPassword, loginInfo.locuspasswrod);

      // Store location data if available
      if (loginInfo.latitude.isNotEmpty && loginInfo.longitude.isNotEmpty) {
        await storage.write(StorageKeys.latitude, loginInfo.latitude);
        await storage.write(StorageKeys.longitude, loginInfo.longitude);
      }

      // Store shift information
      if (loginInfo.srShift != null && loginInfo.srShift!.isNotEmpty) {
        await storage.write(StorageKeys.srShift, loginInfo.srShift!);
      }

      // Store additional fields
      await storage.write(StorageKeys.applicationVersionSr, loginInfo.applicationVersionSR);
      await storage.write(StorageKeys.areaInMeter, loginInfo.areainMeter);
      await storage.write(StorageKeys.downloadDate, loginInfo.downloaddate);
      await storage.write(StorageKeys.password, loginInfo.password);

      debugPrint('✅ User data stored in local storage');
    } catch (e) {
      debugPrint('🚨 Error storing user data: $e');
    }
  }

  // Removed updateProfile method - not used in auth flow
}
