import 'package:flutter/material.dart';
import '../utils/asset_optimizer.dart';

/// Example widget showing how to use optimized assets
class OptimizedAssetWidget extends StatelessWidget {
  const OptimizedAssetWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Before: Image.asset('assets/icons/working-time.png')
        // After: Use Material Icon
        AssetOptimizer.getOptimizedIcon('working-time', size: 24, color: Colors.blue),

        // Before: Image.asset('assets/images/money-box.png')
        // After: Use Material Icon
        AssetOptimizer.getOptimizedIcon('money-box', size: 32, color: Colors.green),

        // Essential assets still use images
        AssetOptimizer.getOptimizedImage(
          'assets/images/kk_rider_logo.png',
          width: 100,
          height: 100,
        ),

        // Placeholder for removed assets
        AssetOptimizer.createPlaceholder(
          width: 50,
          height: 50,
          icon: Icons.image,
          text: 'Optimized',
        ),
      ],
    );
  }
}
