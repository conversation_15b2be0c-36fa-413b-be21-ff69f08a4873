import 'package:flutter/material.dart';
import '../utils/asset_optimizer.dart';

/// Example widget showing how to use optimized assets
class OptimizedAssetWidget extends StatelessWidget {
  const OptimizedAssetWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Before: Image.asset('assets/icons/working-time.png')
        // After: Use Material Icon
        AssetOptimizer.getOptimizedIcon('working-time', size: 24, color: Colors.blue),
        
        // Before: Image.asset('assets/images/money-box.png')
        // After: Use Material Icon
        AssetOptimizer.getOptimizedIcon('money-box', size: 32, color: Colors.green),
        
        // Essential assets still use images
        AssetOptimizer.getOptimizedImage(
          'assets/images/kk_rider_logo.png',
          width: 100,
          height: 100,
        ),
        
        // Placeholder for removed assets
        AssetOptimizer.createPlaceholder(
          width: 50,
          height: 50,
          icon: Icons.image,
          text: 'Optimized',
        ),
      ],
    );
  }
}

/// Example of replacing asset images with icons in existing widgets
class EarningCard extends StatelessWidget {
  const EarningCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: ListTile(
        // Before: Image.asset('assets/images/rupee.png')
        // After: Material Icon (saves ~50KB)
        leading: Icon(Icons.currency_rupee, color: Colors.green),
        title: Text('Today\'s Earnings'),
        subtitle: Text('₹1,250'),
        // Before: Image.asset('assets/icons/working-time.png')
        // After: Material Icon (saves ~20KB)
        trailing: Icon(Icons.access_time, color: Colors.grey),
      ),
    );
  }
}

/// Example of order status with optimized icons
class OrderStatusWidget extends StatelessWidget {
  final String status;
  
  const OrderStatusWidget({super.key, required this.status});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // Before: Multiple PNG files for different statuses
        // After: Single icon with different colors (saves ~100KB)
        _getStatusIcon(status),
        SizedBox(width: 8),
        Text(status),
      ],
    );
  }
  
  Widget _getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Icon(Icons.pending, color: Colors.orange);
      case 'picked':
        return Icon(Icons.shopping_bag, color: Colors.blue);
      case 'delivered':
        return Icon(Icons.check_circle, color: Colors.green);
      default:
        return Icon(Icons.info, color: Colors.grey);
    }
  }
}

/// Vehicle type selector with optimized icons
class VehicleTypeSelector extends StatelessWidget {
  const VehicleTypeSelector({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Before: Image.asset('assets/images/Bike-Icon.png')
        // After: Material Icon (saves ~30KB)
        _buildVehicleOption(Icons.motorcycle, 'Bike'),
        
        // Before: Image.asset('assets/images/car-icon.png')
        // After: Material Icon (saves ~30KB)
        _buildVehicleOption(Icons.directions_car, 'Car'),
        
        // Before: Image.asset('assets/images/truck-icon.png')
        // After: Material Icon (saves ~30KB)
        _buildVehicleOption(Icons.local_shipping, 'Truck'),
      ],
    );
  }
  
  Widget _buildVehicleOption(IconData icon, String label) {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, size: 32),
        ),
        SizedBox(height: 8),
        Text(label),
      ],
    );
  }
}
