import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

import 'package:kisankonnect_rider/utils/responsive_utils.dart';
import 'package:kisankonnect_rider/utils/asset_optimizer.dart';
import 'package:get/get.dart';
import 'dart:async';
import '../../../widgets/dialogs/break_time_dialog.dart';
import '../../../../controllers/shift_controller.dart';

class ShiftInfoSection extends StatefulWidget {
  final bool isOnline;
  const ShiftInfoSection({required this.isOnline, super.key});

  @override
  State<ShiftInfoSection> createState() => _ShiftInfoSectionState();
}

class _ShiftInfoSectionState extends State<ShiftInfoSection> {
  final ShiftController _shiftController = Get.find<ShiftController>();
  bool _isOnBreak = false;
  int _breakTimeLeft = 30 * 60; // 30 minutes in seconds
  Timer? _breakTimer;
  void _showBreakDialog() {
    Get.dialog(
      BreakTimeDialog(
        onConfirm: () async {
          await _startBreak();
          if (mounted) Navigator.pop(context);
        },
        onCancel: () {
          Navigator.pop(context);
        },
      ),
      barrierDismissible: false,
    );
  }

  Future<void> _startBreak() async {
    // Call API to submit break time
    final success = await _shiftController.startBreak('30'); // 30 minutes break

    if (success) {
      setState(() {
        _isOnBreak = true;
        _breakTimeLeft = 30 * 60; // Reset to 30 minutes
      });

      // Start countdown timer
      _breakTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (_breakTimeLeft > 0) {
          setState(() {
            _breakTimeLeft--;
          });
        } else {
          _endBreak();
        }
      });

      Get.snackbar(
        'Break Started',
        'Your 30-minute break has been started',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } else {
      Get.snackbar(
        'Error',
        'Failed to start break. Please try again.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> _endBreak() async {
    _breakTimer?.cancel();

    // Call API to stop break
    await _shiftController.stopBreak();

    setState(() {
      _isOnBreak = false;
      _breakTimeLeft = 30 * 60;
    });

    Get.snackbar(
      'Break Ended',
      'You are now back online',
      backgroundColor: Colors.green,
      colorText: Colors.white,
    );
  }

  String _formatBreakTime() {
    final minutes = _breakTimeLeft ~/ 60;
    final seconds = _breakTimeLeft % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  void _showStopBreakDialog() {
    final minutes = _breakTimeLeft ~/ 60;
    final seconds = _breakTimeLeft % 60;

    Get.dialog(
      BreakTimerDialog(
        remainingMinutes: minutes,
        remainingSeconds: seconds,
        onStopBreak: () {
          _endBreak();
          Navigator.pop(context);
        },
        onCancel: () {
          Navigator.pop(context);
        },
      ),
      barrierDismissible: false,
    );
  }

  @override
  void dispose() {
    _breakTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Using ResponsiveUtils for consistent sizing
    final horizontalPadding = ResponsiveUtils.spacingM(context); // 4% of screen width
    final verticalPadding = ResponsiveUtils.height(context, 1.5); // 1.5% of screen height
    final borderRadius = ResponsiveUtils.borderRadius(context, BorderRadiusType.medium); // Responsive radius
    final iconSize = ResponsiveUtils.iconSize(context, IconSizeType.medium);

    return Container(
      // margin: EdgeInsets.symmetric(horizontal: horizontalPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
        vertical: verticalPadding,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header section with greeting
          if (!widget.isOnline) ...[
            Text(
              'Hi Rider Name, Store ID (Store Name)',
              style: AppTextTheme.cardTitle,
            ),
            SizedBox(height: ResponsiveUtils.height(context, 1)),
          ],

          // Shift status row
          Row(
            children: [
              // Status badge
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: ResponsiveUtils.spacingS(context),
                  vertical: ResponsiveUtils.height(context, 0.5),
                ),
                decoration: BoxDecoration(
                  color: widget.isOnline ? AppColors.greenLight : Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
                ),
                child: Text(
                  widget.isOnline ? 'Shift Ongoing' : 'Shift Will Start',
                  style: AppTextTheme.cardCaption.copyWith(
                    fontWeight: FontWeight.w600,
                    color: widget.isOnline ? AppColors.green : Colors.grey.shade600,
                  ),
                ),
              ),
              SizedBox(width: ResponsiveUtils.spacingS(context)),
              // Shift time
              Text(
                '9:30 am - 6:00 pm',
                style: AppTextTheme.cardTitle,
              ),
              const Spacer(),
              // Clock icon
              AssetOptimizer.getOptimizedIcon(
                'working-time',
                size: iconSize,
                color: widget.isOnline ? AppColors.green : Colors.grey.shade600,
              ),
            ],
          ),

          // Dotted divider line
          SizedBox(height: ResponsiveUtils.height(context, 1.5)),
          CustomPaint(
            size: Size(double.infinity, 1),
            painter: DottedLinePainter(
              color: Colors.grey.shade300,
              strokeWidth: 1.0,
              dashWidth: 4.0,
              dashSpace: 4.0,
            ),
          ),

          // Online mode: Timer and break button
          if (widget.isOnline) ...[
            SizedBox(height: ResponsiveUtils.height(context, 1.5)),

            // Work timer row
            Row(
              crossAxisAlignment: CrossAxisAlignment.center, // Center align all elements
              children: [
                // Timer frame icon
                AssetOptimizer.getOptimizedIcon(
                  'timer-frame',
                  size: 24,
                  color: AppColors.textPrimary,
                ),
                SizedBox(width: ResponsiveUtils.spacingS(context)),

                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    RichText(
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: '04:30',
                            style: AppTextTheme.cardTitle.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                              fontSize: 16,
                            ),
                          ),
                          TextSpan(
                            text: ' hr',
                            style: AppTextTheme.cardSubtitle.copyWith(
                              color: AppColors.textSecondary,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 2),
                    Text(
                      'out of 8h',
                      style: AppTextTheme.cardSubtitle.copyWith(
                        color: AppColors.textSecondary,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),

                const Spacer(),

                // Vertical dotted line divider (centered)
                if (!_isOnBreak) ...[
                  SizedBox(
                    height: 40, // Increased height to span the full row height
                    width: 1,
                    child: CustomPaint(
                      size: Size(1, 40),
                      painter: VerticalDottedLinePainter(
                        color: Colors.grey.shade300,
                        strokeWidth: 1.0,
                        dashHeight: 3.0,
                        dashSpace: 3.0,
                      ),
                    ),
                  ),
                  SizedBox(width: 16), // Space between line and button
                ],

                // Take break button (when not on break)
                if (!_isOnBreak)
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                      border: Border.all(
                        color: AppColors.green,
                        width: 1.5,
                      ),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: GestureDetector(
                      onTap: widget.isOnline ? _showBreakDialog : null,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          AssetOptimizer.getOptimizedIcon(
                            'Group',
                            size: 16,
                            color: AppColors.textSecondary,
                          ),
                          SizedBox(width: 8),
                          Text(
                            'Take break',
                            style: AppTextTheme.cardSubtitle.copyWith(
                              color: AppColors.green,
                              fontWeight: FontWeight.w500,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),

            // Break timer row (when on break)
            if (_isOnBreak) ...[
              SizedBox(height: ResponsiveUtils.height(context, 1)),
              GestureDetector(
                onTap: _showStopBreakDialog,
                child: Row(
                  children: [
                    Icon(
                      Icons.pause_circle_filled,
                      color: Colors.orange,
                      size: iconSize,
                    ),
                    SizedBox(width: ResponsiveUtils.spacingS(context)),
                    Text(
                      _formatBreakTime(),
                      style: AppTextTheme.cardTitle.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                    SizedBox(width: ResponsiveUtils.spacingXS(context)),
                    Text(
                      'min left',
                      style: AppTextTheme.cardSubtitle.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    SizedBox(width: ResponsiveUtils.spacingXS(context)),
                    Text(
                      'out of 30 min break',
                      style: AppTextTheme.cardSubtitle.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ],
      ),
    );
  }
}

/// Custom painter for creating dotted lines
class DottedLinePainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double dashWidth;
  final double dashSpace;

  DottedLinePainter({
    required this.color,
    this.strokeWidth = 1.0,
    this.dashWidth = 4.0,
    this.dashSpace = 4.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    double startX = 0;
    final y = size.height / 2;

    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, y),
        Offset(startX + dashWidth, y),
        paint,
      );
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

/// Custom painter for creating vertical dotted lines
class VerticalDottedLinePainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double dashHeight;
  final double dashSpace;

  VerticalDottedLinePainter({
    required this.color,
    this.strokeWidth = 1.0,
    this.dashHeight = 3.0,
    this.dashSpace = 3.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    double startY = 0;
    final x = size.width / 2;

    while (startY < size.height) {
      canvas.drawLine(
        Offset(x, startY),
        Offset(x, startY + dashHeight),
        paint,
      );
      startY += dashHeight + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
