import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/routes/app_pages.dart';
import '../../../controllers/auth_controller.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    Timer(const Duration(seconds: 3), () {
      _checkAuthAndNavigate();
    });
  }

  void _checkAuthAndNavigate() async {
    try {
      final AuthController authController = Get.find<AuthController>();
      final BiometricService biometricService = BiometricService.instance;

      // Check if user is already logged in
      if (authController.isLoggedIn) {
        debugPrint('🔄 User already logged in, checking biometric authentication');

        // Check if biometric authentication is available and enabled
        debugPrint('🔐 Initializing biometric service...');
        await biometricService.initialize();

        debugPrint('🔐 Getting biometric info...');
        final biometricInfo = await biometricService.getBiometricInfo();

        final isBiometricAvailable = biometricInfo['isAvailable'] ?? false;
        final isBiometricEnabled = biometricInfo['isEnabled'] ?? false;

        debugPrint('🔐 Biometric Available: $isBiometricAvailable');
        debugPrint('🔐 Biometric Enabled: $isBiometricEnabled');
        debugPrint('🔐 Full biometric info: $biometricInfo');

        // Check both regStatus and approvalStatus before proceeding
        final user = authController.currentUser;
        if (user != null && user.regStatus == 3 && user.approvalStatus == 1) {
          debugPrint('🔐 User is verified (regStatus: ${user.regStatus}, approvalStatus: ${user.approvalStatus})');

          if (isBiometricAvailable && isBiometricEnabled) {
            debugPrint('🔐 Biometric available and enabled, requesting authentication');

            // Show biometric authentication dialog
            _showBiometricAuthDialog();
          } else {
            debugPrint('🔄 Biometric not enabled or not available, navigating directly to dashboard');
            // Navigate directly to dashboard since user is already authenticated and verified
            // Skip biometric setup popup - user can enable it manually in profile settings
            Get.offAllNamed(AppRoutes.dashboard);
          }
        } else if (user != null && user.regStatus == 3 && user.approvalStatus != 1) {
          debugPrint(
              '🔐 User registration complete but pending approval or rejected (regStatus: ${user.regStatus}, approvalStatus: ${user.approvalStatus})');
          // Navigate to welcome screen for approval status 0 (not verified) or 2 (rejected)
          Get.offAllNamed(AppRoutes.deliveryPartnerWelcome);
        } else if (user != null) {
          debugPrint(
              '🔐 User registration incomplete (regStatus: ${user.regStatus}, approvalStatus: ${user.approvalStatus})');
          // Navigate based on registration status using AuthController method
          if (user.regStatus == 0) {
            Get.offAllNamed(AppRoutes.createProfile, arguments: {'initialStep': 0});
          } else if (user.regStatus == 1) {
            Get.offAllNamed(AppRoutes.createProfile, arguments: {'initialStep': 6});
          } else if (user.regStatus == 2) {
            Get.offAllNamed(AppRoutes.createProfile, arguments: {'initialStep': 7});
          } else {
            Get.offAllNamed(AppRoutes.createProfile, arguments: {'initialStep': 0});
          }
        } else {
          debugPrint('🔐 User data not found, redirecting to login');
          Get.offAllNamed(AppRoutes.login);
        }
      } else {
        // User not logged in, go directly to login
        debugPrint('🔄 User not logged in, going to login');
        Get.offAllNamed(AppRoutes.login);
      }
    } catch (e) {
      debugPrint('🚨 Error checking auth status: $e');
      // Fallback to login screen for safety
      Get.offAllNamed(AppRoutes.login);
    }
  }

  /// Show biometric authentication bottom sheet
  void _showBiometricAuthDialog() {
    Get.bottomSheet(
      _buildBiometricBottomSheet(),
      isDismissible: false,
      enableDrag: false,
      isScrollControlled: true,
    );
  }

  /// Build biometric authentication bottom sheet
  Widget _buildBiometricBottomSheet() {
    return Container(
      height: Get.height * 0.4,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          const SizedBox(height: 32),

          // Fingerprint icon with animation
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: AppColors.green.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.fingerprint,
              size: 48,
              color: AppColors.green,
            ),
          ),

          const SizedBox(height: 24),

          // Title
          Text(
            'Biometric Authentication',
            style: AppTextTheme.cardTitle.copyWith(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),

          const SizedBox(height: 12),

          // Description
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              'Please authenticate with your fingerprint or face ID for security verification.',
              textAlign: TextAlign.center,
              style: AppTextTheme.cardSubtitle.copyWith(
                fontSize: 14,
                color: AppColors.textSecondary,
                height: 1.4,
              ),
            ),
          ),

          const Spacer(),

          // Authenticate button
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: SizedBox(
              width: double.infinity,
              height: 52,
              child: ElevatedButton(
                onPressed: () async {
                  Get.back();
                  await _performBiometricAuth();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.green,
                  foregroundColor: Colors.white,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.fingerprint, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      'Authenticate',
                      style: AppTextTheme.cardTitle.copyWith(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  /// Perform biometric authentication for security
  Future<void> _performBiometricAuth() async {
    try {
      final BiometricService biometricService = BiometricService.instance;

      debugPrint('🔐 Starting biometric authentication for security...');

      // Simple biometric authentication (no credential retrieval)
      final bool isAuthenticated = await biometricService.authenticateForSecurity();

      if (isAuthenticated) {
        debugPrint('✅ Biometric authentication successful');

        // Navigate to dashboard
        Get.offAllNamed(AppRoutes.dashboard);
      } else {
        debugPrint('❌ Biometric authentication failed');

        // Show option to try again or skip
        _showBiometricFailedDialog();
      }
    } catch (e) {
      debugPrint('🚨 Biometric authentication error: $e');
      _showBiometricFailedDialog();
    }
  }

  /// Show biometric authentication failed bottom sheet
  void _showBiometricFailedDialog() {
    Get.bottomSheet(
      _buildBiometricFailedBottomSheet(),
      isDismissible: false,
      enableDrag: false,
      isScrollControlled: true,
    );
  }

  /// Build biometric authentication failed bottom sheet
  Widget _buildBiometricFailedBottomSheet() {
    return Container(
      height: Get.height * 0.35,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          const SizedBox(height: 32),

          // Error icon
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.red,
            ),
          ),

          const SizedBox(height: 24),

          // Title
          Text(
            'Authentication Failed',
            style: AppTextTheme.cardTitle.copyWith(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),

          const SizedBox(height: 12),

          // Description
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              'Biometric authentication failed. Please try again.',
              textAlign: TextAlign.center,
              style: AppTextTheme.cardSubtitle.copyWith(
                fontSize: 14,
                color: AppColors.textSecondary,
                height: 1.4,
              ),
            ),
          ),

          const Spacer(),

          // Try Again button (no skip button)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: SizedBox(
              width: double.infinity,
              height: 52,
              child: ElevatedButton(
                onPressed: () async {
                  Get.back();
                  await _performBiometricAuth();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.green,
                  foregroundColor: Colors.white,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.refresh, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      'Try Again',
                      style: AppTextTheme.cardTitle.copyWith(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF4CAF50), // Green
              Color(0xFF2E7D32), // Darker green
            ],
          ),
        ),
        child: SafeArea(
          child: SizedBox(
            width: double.infinity,
            child: Column(
              children: [
                // Top spacer
                const Expanded(flex: 2, child: SizedBox()),

                // Logo and branding section
                Expanded(
                  flex: 3,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Logo with subtle animation
                      Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.1),
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 20,
                              offset: const Offset(0, 10),
                            ),
                          ],
                        ),
                        child: const Image(
                          image: AssetImage('assets/images/kk_rider_logo.png'),
                          width: 120,
                          height: 120,
                          fit: BoxFit.contain,
                        ),
                      ),

                      const SizedBox(height: 24),

                      // App name
                      const Text(
                        'KisanKonnect',
                        style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          letterSpacing: 1.2,
                        ),
                      ),

                      const SizedBox(height: 8),

                      // Tagline
                      const Text(
                        'Rider Partner',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.white70,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),

                // Loading section
                Expanded(
                  flex: 2,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Custom loading indicator
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: const CircularProgressIndicator(
                          strokeWidth: 3,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      ),

                      const SizedBox(height: 16),

                      const Text(
                        'Loading...',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),

                // Bottom section with version or company info
                Padding(
                  padding: const EdgeInsets.only(bottom: 32),
                  child: Column(
                    children: [
                      Container(
                        height: 1,
                        width: 60,
                        color: Colors.white.withOpacity(0.3),
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Powered by KisanKonnect',
                        style: TextStyle(
                          color: Colors.white60,
                          fontSize: 12,
                          fontWeight: FontWeight.w300,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
