import 'package:flutter/foundation.dart';
import 'package:kisankonnect_rider/config/env_config.dart';

/// Service for Google Maps related functionality
class MapsService {
  /// Singleton instance
  static final MapsService _instance = MapsService._internal();
  
  /// Factory constructor
  factory MapsService() => _instance;
  
  /// Private constructor
  MapsService._internal();
  
  /// Get instance
  static MapsService get instance => _instance;
  
  /// Get Google Maps API key from environment variables
  String get apiKey => EnvConfig.googleMapsApiKey;
  
  /// Initialize maps service
  Future<void> initialize() async {
    try {
      debugPrint('🗺️ Initializing Maps Service');
      debugPrint('🗺️ Google Maps API Key: ${apiKey.substring(0, 8)}...');
      
      // Add any additional initialization here
      
      debugPrint('✅ Maps Service initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing Maps Service: $e');
    }
  }
  
  /// Check if Google Maps API key is valid
  bool get isApiKeyValid => apiKey.isNotEmpty && apiKey != 'your_google_maps_api_key';
  
  /// Get Google Maps API key for use in web
  String get webApiKey => apiKey;
  
  /// Get Google Maps API key for use in Android
  String get androidApiKey => apiKey;
  
  /// Get Google Maps API key for use in iOS
  String get iosApiKey => apiKey;
}
