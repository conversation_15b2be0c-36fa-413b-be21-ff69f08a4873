import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import '../../../widgets/dialogs/family_information_dialog.dart';

class StepMaritalStatus extends StatefulWidget {
  final Function(Map<String, dynamic>)? onContinue;
  final VoidCallback? onBack;
  const StepMaritalStatus({super.key, this.onContinue, this.onBack});

  @override
  State<StepMaritalStatus> createState() => _StepMaritalStatusState();
}

class _StepMaritalStatusState extends State<StepMaritalStatus> {
  String? _selectedStatus;
  final Map<String, String> _familyData = {};

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FA),
      resizeToAvoidBottomInset: true,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 16),
              Text('Select your marital status', style: AppTextTheme.cardTitle.copyWith(fontSize: 18)),
              const SizedBox(height: 4),
              Text('Please choose your marital status', style: AppTextTheme.cardSubtitle),
              const SizedBox(height: 24),
              _buildRadio('Single'),
              const SizedBox(height: 16),
              _buildRadio('Married'),
              const SizedBox(height: 32),

              // Continue Button
              if (_selectedStatus != null)
                SizedBox(
                  width: double.infinity,
                  height: 48,
                  child: ElevatedButton(
                    onPressed: _onContinue,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.green,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(32),
                      ),
                    ),
                    child: const Text(
                      'Continue',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// Handle continue button press
  Future<void> _onContinue() async {
    if (_selectedStatus == null) return;

    if (_selectedStatus == 'Married') {
      // Show family information dialog for married users
      final familyData = await FamilyInformationDialog.show(
        context: context,
      );

      if (familyData != null) {
        // Include family data with marital status
        final completeData = {
          'status': _selectedStatus!,
          ...familyData,
        };
        widget.onContinue?.call(completeData);
      }
    } else {
      // For single users, just pass the status
      widget.onContinue?.call({
        'status': _selectedStatus!,
      });
    }
  }

  Widget _buildRadio(String value) {
    return InkWell(
      onTap: () => setState(() => _selectedStatus = value),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: _selectedStatus == value ? AppColors.green : Colors.grey.shade300,
            width: 2,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: _selectedStatus == value ? AppColors.green : Colors.grey.shade400,
                  width: 2,
                ),
              ),
              child: _selectedStatus == value
                  ? Center(
                      child: Container(
                        width: 12,
                        height: 12,
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                          color: AppColors.green,
                        ),
                      ),
                    )
                  : null,
            ),
            const SizedBox(width: 16),
            Text(
              value,
              style: AppTextTheme.cardTitle,
            ),
          ],
        ),
      ),
    );
  }
}
