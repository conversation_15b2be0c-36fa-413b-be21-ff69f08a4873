import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/utils/responsive_utils.dart';
import '../scanner/qr_scanner_screen.dart';

class PickupOrderScreen extends StatelessWidget {
  final int totalOrders;
  final List<OrderItem> orders;

  const PickupOrderScreen({
    super.key,
    this.totalOrders = 45,
    this.orders = const [],
  });

  @override
  Widget build(BuildContext context) {
    // Default orders if none provided
    final orderList = orders.isNotEmpty ? orders : _getDefaultOrders();

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(80),
        child: Container(
          color: Colors.white,
          child: SafeArea(
            child: Container(
              padding: EdgeInsets.all(16),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Shopping bag icon in rounded container
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Color(0xFFE8F5E9), // Light green background
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Icon(
                        Icons.shopping_bag_outlined,
                        color: AppColors.green,
                        size: 20,
                      ),
                    ),
                  ),
                  SizedBox(width: 12),
                  // Title and subtitle
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Pickup up Order',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                            height: 1.25,
                          ),
                        ),
                        SizedBox(height: 2),
                        Text(
                          'Your order is ready to pickup!',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: Colors.grey.shade600,
                            height: 1.33,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Green circular badge with number
                  Container(
                    width: 36,
                    height: 36,
                    decoration: BoxDecoration(
                      color: AppColors.green,
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        totalOrders.toString(),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      body: Column(
        children: [
          // Orders list
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.all(16),
              itemCount: orderList.length,
              itemBuilder: (context, index) {
                final order = orderList[index];
                return Container(
                  margin: EdgeInsets.only(bottom: 16),
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 8,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Order ID
                      Text(
                        'Order Id - #${order.orderId}',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                        ),
                      ),
                      SizedBox(height: 12),

                      // Order details row
                      Row(
                        children: [
                          // Racks
                          Expanded(
                            child: Column(
                              children: [
                                Text(
                                  'Racks',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                                SizedBox(height: 8),
                                Container(
                                  width: 48,
                                  height: 48,
                                  decoration: BoxDecoration(
                                    color: Colors.red,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Center(
                                    child: Text(
                                      order.racks.toString(),
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // Saddle bag
                          Expanded(
                            child: Column(
                              children: [
                                Text(
                                  'Saddle bag',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                                SizedBox(height: 8),
                                Text(
                                  order.saddleBag.toString().padLeft(2, '0'),
                                  style: TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.black,
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // Silver bag
                          Expanded(
                            child: Column(
                              children: [
                                Text(
                                  'Silver bag',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                                SizedBox(height: 8),
                                Text(
                                  order.silverBag.toString().padLeft(2, '0'),
                                  style: TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.black,
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // Milk pouch
                          Expanded(
                            child: Column(
                              children: [
                                Text(
                                  'Milk pouch',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                                SizedBox(height: 8),
                                Text(
                                  order.milkPouch.toString().padLeft(2, '0'),
                                  style: TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.black,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              },
            ),
          ),

          // Scan order button
          Container(
            padding: EdgeInsets.all(16),
            child: SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: () {
                  // Navigate to QR Scanner
                  Get.to(() => QRScannerScreen(
                        scanType: 'Saddle bag',
                        orderId: orderList.isNotEmpty ? orderList.first.orderId : '',
                      ));
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.green,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(28),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.qr_code_scanner,
                      color: Colors.white,
                      size: 20,
                    ),
                    SizedBox(width: 8),
                    Text(
                      'Scan order',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<OrderItem> _getDefaultOrders() {
    return [
      OrderItem(orderId: 'E76592', racks: 12, saddleBag: 1, silverBag: 1, milkPouch: 2),
      OrderItem(orderId: 'E76593', racks: 24, saddleBag: 1, silverBag: 1, milkPouch: 0),
      OrderItem(orderId: 'E76594', racks: 25, saddleBag: 1, silverBag: 1, milkPouch: 1),
      OrderItem(orderId: 'E76595', racks: 32, saddleBag: 1, silverBag: 1, milkPouch: 2),
      OrderItem(orderId: 'E76596', racks: 39, saddleBag: 1, silverBag: 1, milkPouch: 4),
      OrderItem(orderId: 'E76597', racks: 15, saddleBag: 1, silverBag: 1, milkPouch: 1),
    ];
  }
}

class OrderItem {
  final String orderId;
  final int racks;
  final int saddleBag;
  final int silverBag;
  final int milkPouch;

  const OrderItem({
    required this.orderId,
    required this.racks,
    required this.saddleBag,
    required this.silverBag,
    required this.milkPouch,
  });
}
