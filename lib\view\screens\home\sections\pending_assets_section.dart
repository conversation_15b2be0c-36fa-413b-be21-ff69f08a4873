import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

class PendingAssetsSection extends StatelessWidget {
  const PendingAssetsSection({super.key});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    // Responsive sizing
    final horizontalPadding = screenWidth * 0.04; // 4% of screen width
    final containerMargin = screenWidth * 0.04; // 4% of screen width
    final containerPadding = screenWidth * 0.04; // 4% of screen width
    final containerRadius = screenWidth * 0.04; // 4% of screen width
    final cardSpacing = screenWidth * 0.03; // 3% of screen width
    final cardHeight = screenHeight * 0.12; // 12% of screen height
    final imageSize = screenWidth * 0.12; // 12% of screen width
    final badgeSize = screenWidth * 0.06; // 6% of screen width

    // Sample data - you can replace this with dynamic data
    final List<Map<String, dynamic>> assetItems = [
      {
        'label': 'Saddle Bag',
        'count': 15,
        'imagePath': 'assets/images/pending_assets/saddle_bag.png',
      },
      {
        'label': 'Silver Bag',
        'count': 10,
        'imagePath': 'assets/images/pending_assets/ice_box.png',
      },
      {
        'label': 'Chill Bag',
        'count': 5,
        'imagePath': 'assets/images/pending_assets/cold.png',
      },
      // Add more items to test scrolling
      {
        'label': 'Thermal Bag',
        'count': 8,
        'imagePath': 'assets/images/pending_assets/saddle_bag.png',
      },
      {
        'label': 'Delivery Box',
        'count': 3,
        'imagePath': 'assets/images/pending_assets/ice_box.png',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header outside the container
        Padding(
          padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
          child: Row(
            children: [
              Text(
                'Pending Assets',
                style: AppTextTheme.cardTitle,
              ),
              const Spacer(),
            ],
          ),
        ),

        SizedBox(height: screenHeight * 0.015),

        // Container with scrollable assets
        Container(
          width: screenWidth - (containerMargin * 2),
          margin: EdgeInsets.symmetric(horizontal: containerMargin),
          padding: EdgeInsets.all(containerPadding),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(containerRadius),
            boxShadow: [
              BoxShadow(
                color: AppColors.shadowLight,
                blurRadius: screenWidth * 0.02,
                offset: Offset(0, screenHeight * 0.002),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Scrollable assets list
              SizedBox(
                height: cardHeight + (containerPadding * 0.5), // Add some extra space
                child: ListView.separated(
                  scrollDirection: Axis.horizontal,
                  itemCount: assetItems.length,
                  separatorBuilder: (context, index) => SizedBox(width: cardSpacing),
                  itemBuilder: (context, index) {
                    final item = assetItems[index];
                    return SizedBox(
                      width: screenWidth * 0.25, // Fixed width for each card
                      child: AssetCard(
                        label: item['label'],
                        count: item['count'],
                        imagePath: item['imagePath'],
                        cardHeight: cardHeight,
                        imageSize: imageSize,
                        badgeSize: badgeSize,
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class AssetCard extends StatelessWidget {
  final String label;
  final int count;
  final String imagePath;
  final double cardHeight;
  final double imageSize;
  final double badgeSize;

  const AssetCard({
    required this.label,
    required this.count,
    required this.imagePath,
    required this.cardHeight,
    required this.imageSize,
    required this.badgeSize,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final cardRadius = screenWidth * 0.03; // 3% of screen width
    final cardPadding = screenWidth * 0.03; // 3% of screen width
    final iconSize = screenWidth * 0.06; // 6% of screen width
    final badgeOffset = screenWidth * 0.02; // 2% of screen width

    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          height: cardHeight,
          padding: EdgeInsets.all(cardPadding),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: AppColors.green, width: 1.5),
            borderRadius: BorderRadius.circular(cardRadius),
            boxShadow: [
              BoxShadow(
                color: AppColors.shadowLight,
                blurRadius: screenWidth * 0.015,
                offset: Offset(0, screenHeight * 0.002),
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Responsive image container with badge
              Stack(
                clipBehavior: Clip.none,
                children: [
                  SizedBox(
                    width: imageSize,
                    height: imageSize,
                    child: Image.asset(
                      imagePath,
                      fit: BoxFit.contain,
                      // Fallback for when image is not available
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: imageSize,
                          height: imageSize,
                          decoration: BoxDecoration(
                            color: _getImageColor(label),
                            borderRadius: BorderRadius.circular(cardRadius * 0.6),
                          ),
                          child: Icon(
                            _getImageIcon(label),
                            color: Colors.white,
                            size: iconSize,
                          ),
                        );
                      },
                    ),
                  ),
                  // Badge positioned at top-right corner of the image
                  Positioned(
                    top: -badgeOffset,
                    right: -badgeOffset,
                    child: Container(
                      width: badgeSize,
                      height: badgeSize,
                      decoration: const BoxDecoration(
                        color: AppColors.green,
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Text(
                          '$count',
                          style: AppTextTheme.buttonSmall.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: screenHeight * 0.01),
              // Responsive label
              Text(
                label,
                style: AppTextTheme.cardCaption.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.green,
                  decoration: TextDecoration.none,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Color _getImageColor(String label) {
    switch (label.toLowerCase()) {
      case 'saddle bag':
        return Colors.green.shade400;
      case 'silver bag':
        return Colors.grey.shade400;
      case 'chill bag':
        return Colors.blue.shade400;
      default:
        return AppColors.green;
    }
  }

  IconData _getImageIcon(String label) {
    switch (label.toLowerCase()) {
      case 'saddle bag':
        return Icons.inventory_2;
      case 'silver bag':
        return Icons.inventory;
      case 'chill bag':
        return Icons.ac_unit;
      default:
        return Icons.inventory;
    }
  }
}
