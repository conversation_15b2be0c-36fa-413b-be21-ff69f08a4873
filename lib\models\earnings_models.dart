/// Model for weekly earnings API response
class WeeklyEarningsResponse {
  final String status;
  final String msg;
  final List<RiderEarning> earnings;
  final List<RiderIncentive> incentives;

  WeeklyEarningsResponse({
    required this.status,
    required this.msg,
    required this.earnings,
    required this.incentives,
  });

  factory WeeklyEarningsResponse.fromJson(Map<String, dynamic> json) {
    return WeeklyEarningsResponse(
      status: json['status']?.toString() ?? '',
      msg: json['msg']?.toString() ?? '',
      earnings: (json['fE_RiderAllEarningForDayNew'] as List<dynamic>?)
          ?.map((e) => RiderEarning.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      incentives: (json['fE_RiderDayIncentiveModelNew'] as List<dynamic>?)
          ?.map((e) => RiderIncentive.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
    );
  }

  bool get isSuccess => status == '200';

  /// Calculate total earnings for the week
  double get totalWeeklyEarnings {
    return earnings.fold(0.0, (sum, earning) => sum + earning.totalEarning);
  }

  /// Calculate total incentives for the week
  double get totalWeeklyIncentives {
    return incentives.fold(0.0, (sum, incentive) => sum + incentive.totalIncentive);
  }

  /// Calculate total orders for the week
  int get totalWeeklyOrders {
    return earnings.fold(0, (sum, earning) => sum + earning.totalOrders);
  }

  /// Get incentive by type
  double getIncentiveByType(String type) {
    return incentives
        .where((incentive) => incentive.incentiveType.toLowerCase().contains(type.toLowerCase()))
        .fold(0.0, (sum, incentive) => sum + incentive.totalIncentive);
  }

  @override
  String toString() {
    return 'WeeklyEarningsResponse(status: $status, msg: $msg, earnings: ${earnings.length}, incentives: ${incentives.length})';
  }
}

/// Model for individual rider earning
class RiderEarning {
  final DateTime? date;
  final int srid;
  final double totalEarning;
  final int totalOrders;
  final double orderEarning;
  final double rainSurgeEarning;

  RiderEarning({
    this.date,
    required this.srid,
    required this.totalEarning,
    required this.totalOrders,
    this.orderEarning = 0.0,
    this.rainSurgeEarning = 0.0,
  });

  factory RiderEarning.fromJson(Map<String, dynamic> json) {
    return RiderEarning(
      date: json['fDate'] != null ? DateTime.tryParse(json['fDate'].toString()) : null,
      srid: json['srid'] as int? ?? 0,
      totalEarning: (json['totalEarning'] as num?)?.toDouble() ?? 0.0,
      totalOrders: json['totalOrders'] as int? ?? 0,
      orderEarning: (json['orderEarning'] as num?)?.toDouble() ?? 0.0,
      rainSurgeEarning: (json['rainSurgeEarning'] as num?)?.toDouble() ?? 0.0,
    );
  }

  @override
  String toString() {
    return 'RiderEarning(date: $date, srid: $srid, totalEarning: $totalEarning, totalOrders: $totalOrders)';
  }
}

/// Model for rider incentive
class RiderIncentive {
  final DateTime? date;
  final int srid;
  final String incentiveType;
  final double totalIncentive;

  RiderIncentive({
    this.date,
    required this.srid,
    required this.incentiveType,
    required this.totalIncentive,
  });

  factory RiderIncentive.fromJson(Map<String, dynamic> json) {
    return RiderIncentive(
      date: json['fDate'] != null ? DateTime.tryParse(json['fDate'].toString()) : null,
      srid: json['srid'] as int? ?? 0,
      incentiveType: json['incentiveType']?.toString() ?? '',
      totalIncentive: (json['totalincentive'] as num?)?.toDouble() ?? 0.0,
    );
  }

  @override
  String toString() {
    return 'RiderIncentive(date: $date, srid: $srid, incentiveType: $incentiveType, totalIncentive: $totalIncentive)';
  }
}

/// Enum for earnings period types
enum EarningsPeriod {
  today,
  thisWeek,
  thisMonth,
}

/// Extension to get display text for earnings period
extension EarningsPeriodExtension on EarningsPeriod {
  String get displayText {
    switch (this) {
      case EarningsPeriod.today:
        return 'Today';
      case EarningsPeriod.thisWeek:
        return 'This Week';
      case EarningsPeriod.thisMonth:
        return 'This Month';
    }
  }
}
