import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qr_code_scanner_plus/qr_code_scanner_plus.dart';
import '../orders/order_picked_up_screen.dart';
import '../../../mixins/analytics_mixin.dart';
import '../../../services/all_services.dart';
import '../../widgets/common/common_app_bar.dart';

class QRScannerScreen extends StatefulWidget {
  final String scanType;
  final String orderId;

  const QRScannerScreen({
    super.key,
    this.scanType = 'Saddle bag',
    this.orderId = '',
  });

  @override
  State<QRScannerScreen> createState() => _QRScannerScreenState();
}

class _QRScannerScreenState extends State<QRScannerScreen> with StatefulAnalyticsMixin {
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  QRViewController? controller;
  bool flashOn = false;

  @override
  String get screenName => 'QRScannerScreen';

  void _onQRViewCreated(QRViewController controller) {
    this.controller = controller;
    controller.scannedDataStream.listen((scanData) {
      // Handle scanned QR code
      if (scanData.code != null) {
        controller.pauseCamera();
        _handleScannedCode(scanData.code!);
      }
    });
  }

  void _handleScannedCode(String code) {
    // Track successful QR scan
    trackQRScanEvent(widget.scanType, true, orderId: widget.orderId);

    // Show success dialog or navigate to next screen
    Get.to(() => OrderPickedUpScreen());
  }

  void _toggleFlash() {
    setState(() {
      flashOn = !flashOn;
    });
    controller?.toggleFlash();

    // Track flash toggle
    trackUserAction('flash_toggled', properties: {
      'flash_on': flashOn,
      'scan_type': widget.scanType,
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: CommonAppBar(
        titleKey: 'scanQRCode',
        backgroundColor: Colors.black.withValues(alpha: 0.8),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Stack(
        children: [
          // QR Scanner Camera View
          QRView(
            key: qrKey,
            onQRViewCreated: _onQRViewCreated,
            overlay: QrScannerOverlayShape(
              borderColor: Colors.white,
              borderRadius: 12,
              borderLength: 30,
              borderWidth: 4,
              cutOutSize: 250,
            ),
          ),

          // Bottom buttons container with proper alignment
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: ResponsiveUtils.spacingL(context),
                vertical: ResponsiveUtils.spacingXL(context),
              ),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withValues(alpha: 0.7),
                  ],
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Can't Scan Button
                  Expanded(
                    child: Container(
                      margin: EdgeInsets.only(right: ResponsiveUtils.spacingS(context)),
                      child: ElevatedButton.icon(
                        onPressed: () {
                          // Track manual skip of QR scan
                          trackQRScanEvent(widget.scanType, false, orderId: widget.orderId);
                          trackUserAction('cant_scan_button_pressed', properties: {
                            'scan_type': widget.scanType,
                            'order_id': widget.orderId,
                          });
                          Get.to(() => OrderPickedUpScreen());
                        },
                        icon: Icon(
                          Icons.qr_code_2,
                          size: ResponsiveUtils.iconSize(context, IconSizeType.small),
                          color: AppColors.textPrimary,
                        ),
                        label: Text(
                          AppStrings.get('cantScan'),
                          style: AppTextTheme.responsive(context, AppTextTheme.buttonMedium).copyWith(
                            color: AppColors.textPrimary,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: AppColors.textPrimary,
                          padding: EdgeInsets.symmetric(
                            vertical: ResponsiveUtils.spacingS(context),
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(ResponsiveUtils.scale(context, 25)),
                          ),
                        ),
                      ),
                    ),
                  ),

                  // Flash Toggle Button
                  Container(
                    width: ResponsiveUtils.scale(context, 56),
                    height: ResponsiveUtils.scale(context, 56),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      onPressed: _toggleFlash,
                      icon: Icon(
                        flashOn ? Icons.flash_on : Icons.flash_off,
                        color: AppColors.textPrimary,
                        size: ResponsiveUtils.iconSize(context, IconSizeType.medium),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
