import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qr_code_scanner_plus/qr_code_scanner_plus.dart';
import '../orders/order_picked_up_screen.dart';
import '../../../mixins/analytics_mixin.dart';

class QRScannerScreen extends StatefulWidget {
  final String scanType;
  final String orderId;

  const QRScannerScreen({
    super.key,
    this.scanType = 'Saddle bag',
    this.orderId = '',
  });

  @override
  State<QRScannerScreen> createState() => _QRScannerScreenState();
}

class _QRScannerScreenState extends State<QRScannerScreen> with StatefulAnalyticsMixin {
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  QRViewController? controller;
  bool flashOn = false;

  @override
  String get screenName => 'QRScannerScreen';

  void _onQRViewCreated(QRViewController controller) {
    this.controller = controller;
    controller.scannedDataStream.listen((scanData) {
      // Handle scanned QR code
      if (scanData.code != null) {
        controller.pauseCamera();
        _handleScannedCode(scanData.code!);
      }
    });
  }

  void _handleScannedCode(String code) {
    // Track successful QR scan
    trackQRScanEvent(widget.scanType, true, orderId: widget.orderId);

    // Show success dialog or navigate to next screen
    Get.to(() => OrderPickedUpScreen());
  }

  void _toggleFlash() {
    setState(() {
      flashOn = !flashOn;
    });
    controller?.toggleFlash();

    // Track flash toggle
    trackUserAction('flash_toggled', properties: {
      'flash_on': flashOn,
      'scan_type': widget.scanType,
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black.withValues(alpha: 0.8),
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Get.back(),
        ),
        title: Text(
          'Scan ${widget.scanType} QR Code',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        centerTitle: false,
      ),
      body: Stack(
        children: [
          // QR Scanner Camera View
          QRView(
            key: qrKey,
            onQRViewCreated: _onQRViewCreated,
            overlay: QrScannerOverlayShape(
              borderColor: Colors.white,
              borderRadius: 12,
              borderLength: 30,
              borderWidth: 4,
              cutOutSize: 250,
            ),
          ),

          // Bottom buttons container with proper alignment
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 20, vertical: 40),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withValues(alpha: 0.7),
                  ],
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Can't Scan Button
                  Expanded(
                    child: Container(
                      margin: EdgeInsets.only(right: 10),
                      child: ElevatedButton.icon(
                        onPressed: () {
                          // Track manual skip of QR scan
                          trackQRScanEvent(widget.scanType, false, orderId: widget.orderId);
                          trackUserAction('cant_scan_button_pressed', properties: {
                            'scan_type': widget.scanType,
                            'order_id': widget.orderId,
                          });
                          Get.to(() => OrderPickedUpScreen());
                        },
                        icon: Icon(
                          Icons.qr_code_2,
                          size: 18,
                          color: Colors.black,
                        ),
                        label: Text(
                          "Can't Scan?",
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.black,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: Colors.black,
                          padding: EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                        ),
                      ),
                    ),
                  ),

                  // Flash Toggle Button
                  Container(
                    width: 56,
                    height: 56,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      onPressed: _toggleFlash,
                      icon: Icon(
                        flashOn ? Icons.flash_on : Icons.flash_off,
                        color: Colors.black,
                        size: 24,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
