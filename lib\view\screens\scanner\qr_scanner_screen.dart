import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

class QRScannerScreen extends StatefulWidget {
  final String scanType;
  final String orderId;

  const QRScannerScreen({
    super.key,
    this.scanType = 'Saddle bag',
    this.orderId = '',
  });

  @override
  State<QRScannerScreen> createState() => _QRScannerScreenState();
}

class _QRScannerScreenState extends State<QRScannerScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade400,
      appBar: AppBar(
        backgroundColor: Colors.black.withValues(alpha: 0.8),
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Get.back(),
        ),
        title: Text(
          'Scan ${widget.scanType} QR Code',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        centerTitle: false,
      ),
      body: Stack(
        children: [
          // Background
          Container(
            width: double.infinity,
            height: double.infinity,
            color: Colors.grey.shade400,
          ),
          
          // 3D Delivery Bag with QR Scanner
          Center(
            child: Container(
              width: 280,
              height: 400,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // 3D Delivery Bag Image
                  Container(
                    width: 280,
                    height: 400,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: CustomPaint(
                      painter: DeliveryBagPainter(),
                    ),
                  ),
                  
                  // QR Scanner Overlay
                  Positioned(
                    top: 120,
                    child: Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: AppColors.green.withValues(alpha: 0.9),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.white,
                          width: 3,
                        ),
                      ),
                      child: Container(
                        margin: EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Center(
                          child: Container(
                            width: 80,
                            height: 80,
                            child: CustomPaint(
                              painter: QRCodePainter(),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  
                  // Scanner Animation Overlay
                  Positioned(
                    top: 120,
                    child: Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: AppColors.green,
                          width: 2,
                        ),
                      ),
                      child: Stack(
                        children: [
                          // Corner brackets
                          Positioned(
                            top: 8,
                            left: 8,
                            child: Container(
                              width: 20,
                              height: 20,
                              decoration: BoxDecoration(
                                border: Border(
                                  top: BorderSide(color: AppColors.green, width: 3),
                                  left: BorderSide(color: AppColors.green, width: 3),
                                ),
                              ),
                            ),
                          ),
                          Positioned(
                            top: 8,
                            right: 8,
                            child: Container(
                              width: 20,
                              height: 20,
                              decoration: BoxDecoration(
                                border: Border(
                                  top: BorderSide(color: AppColors.green, width: 3),
                                  right: BorderSide(color: AppColors.green, width: 3),
                                ),
                              ),
                            ),
                          ),
                          Positioned(
                            bottom: 8,
                            left: 8,
                            child: Container(
                              width: 20,
                              height: 20,
                              decoration: BoxDecoration(
                                border: Border(
                                  bottom: BorderSide(color: AppColors.green, width: 3),
                                  left: BorderSide(color: AppColors.green, width: 3),
                                ),
                              ),
                            ),
                          ),
                          Positioned(
                            bottom: 8,
                            right: 8,
                            child: Container(
                              width: 20,
                              height: 20,
                              decoration: BoxDecoration(
                                border: Border(
                                  bottom: BorderSide(color: AppColors.green, width: 3),
                                  right: BorderSide(color: AppColors.green, width: 3),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Can't Scan Button
          Positioned(
            bottom: 100,
            left: 20,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.9),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.qr_code_2,
                    size: 16,
                    color: Colors.black,
                  ),
                  SizedBox(width: 6),
                  Text(
                    "Can't Scan?",
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Flash/Settings Button
          Positioned(
            bottom: 100,
            right: 20,
            child: Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.9),
                shape: BoxShape.circle,
              ),
              child: IconButton(
                onPressed: () {
                  // Handle flash toggle
                },
                icon: Icon(
                  Icons.flash_off,
                  color: Colors.black,
                  size: 24,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Custom painter for the 3D delivery bag
class DeliveryBagPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;
    
    // Main bag body - green
    paint.color = AppColors.green;
    final bagRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(20, 60, size.width - 40, size.height - 120),
      Radius.circular(8),
    );
    canvas.drawRRect(bagRect, paint);
    
    // Bag straps - black
    paint.color = Colors.black;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(30, 40, 15, 80),
        Radius.circular(4),
      ),
      paint,
    );
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.width - 45, 40, 15, 80),
        Radius.circular(4),
      ),
      paint,
    );
    
    // Bag zipper area - darker green
    paint.color = AppColors.green.withValues(alpha: 0.8);
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(30, 80, size.width - 60, 20),
        Radius.circular(4),
      ),
      paint,
    );
    
    // Side mesh pocket
    paint.color = AppColors.green.withValues(alpha: 0.6);
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.width - 50, size.height - 120, 25, 60),
        Radius.circular(4),
      ),
      paint,
    );
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Custom painter for QR code pattern
class QRCodePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.fill;
    
    final blockSize = size.width / 8;
    
    // Simple QR code pattern
    final pattern = [
      [1, 1, 1, 0, 1, 1, 1, 0],
      [1, 0, 1, 0, 1, 0, 1, 0],
      [1, 1, 1, 0, 1, 1, 1, 0],
      [0, 0, 0, 0, 0, 0, 0, 0],
      [1, 0, 1, 1, 0, 1, 0, 1],
      [0, 1, 0, 1, 1, 0, 1, 0],
      [1, 1, 1, 0, 1, 1, 1, 0],
      [0, 0, 0, 0, 0, 0, 0, 0],
    ];
    
    for (int i = 0; i < pattern.length; i++) {
      for (int j = 0; j < pattern[i].length; j++) {
        if (pattern[i][j] == 1) {
          canvas.drawRect(
            Rect.fromLTWH(
              j * blockSize,
              i * blockSize,
              blockSize,
              blockSize,
            ),
            paint,
          );
        }
      }
    }
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
