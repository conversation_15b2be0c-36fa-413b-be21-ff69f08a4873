import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:pinput/pinput.dart';
import 'package:otp_autofill/otp_autofill.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

class PinputOtpWidget extends StatefulWidget {
  final Function(String) onCompleted;
  final Function(String) onChanged;
  final int length;
  final bool autoFocus;
  final String? hintText;
  final TextStyle? textStyle;
  final Color? fillColor;
  final Color? borderColor;
  final Color? focusedBorderColor;
  final Color? submittedBorderColor;
  final double borderWidth;
  final BorderRadius? borderRadius;
  final double fieldWidth;
  final double fieldHeight;
  final EdgeInsets? margin;

  const PinputOtpWidget({
    super.key,
    required this.onCompleted,
    required this.onChanged,
    this.length = 6,
    this.autoFocus = true,
    this.hintText,
    this.textStyle,
    this.fillColor,
    this.borderColor,
    this.focusedBorderColor,
    this.submittedBorderColor,
    this.borderWidth = 1.0,
    this.borderRadius,
    this.fieldWidth = 48.0,
    this.fieldHeight = 48.0,
    this.margin,
  });

  @override
  State<PinputOtpWidget> createState() => _PinputOtpWidgetState();
}

class _PinputOtpWidgetState extends State<PinputOtpWidget> {
  late final FocusNode _focusNode;
  late final TextEditingController _pinputController;
  late final OTPTextEditController _otpController;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    _pinputController = TextEditingController();
    _initOtpController();
  }

  void _initOtpController() {
    try {
      // Initialize OTP controller for SMS reading
      _otpController = OTPTextEditController(
        codeLength: widget.length,
        onCodeReceive: (code) {
          debugPrint('📱 Your Application receive code - $code');
          if (code.isNotEmpty) {
            // Set the code in the Pinput controller
            _pinputController.text = code;
            widget.onChanged(code);
            widget.onCompleted(code);
          }
        },
      );

      // Start listening for OTP with user consent
      _otpController.startListenUserConsent(
        (code) {
          debugPrint('📱 Raw SMS received: $code');

          // Extract OTP using multiple patterns
          String extractedCode = '';

          // Try exact length pattern first
          final exactPattern = RegExp(r'(\d{' + widget.length.toString() + r'})');
          final exactMatch = exactPattern.firstMatch(code ?? '');
          if (exactMatch != null) {
            extractedCode = exactMatch.group(1) ?? '';
          }

          // Fallback to common OTP patterns
          if (extractedCode.isEmpty) {
            final patterns = [
              RegExp(r'(\d{6})'), // 6 digits
              RegExp(r'OTP[:\s]*(\d+)', caseSensitive: false),
              RegExp(r'code[:\s]*(\d+)', caseSensitive: false),
            ];

            for (final pattern in patterns) {
              final match = pattern.firstMatch(code ?? '');
              if (match != null) {
                final foundCode = match.group(1) ?? '';
                if (foundCode.length == widget.length) {
                  extractedCode = foundCode;
                  break;
                }
              }
            }
          }

          debugPrint('📱 Extracted OTP code: $extractedCode');

          if (extractedCode.isNotEmpty && extractedCode.length == widget.length) {
            // Update the Pinput controller with extracted code
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                _pinputController.text = extractedCode;
                widget.onChanged(extractedCode);
                widget.onCompleted(extractedCode);
              }
            });
          }

          return extractedCode;
        },
      );

      _isInitialized = true;
      debugPrint('📱 OTP controller initialized successfully');

      // Get app signature asynchronously
      _getAppSignature();
    } catch (e) {
      debugPrint('❌ Error initializing OTP controller: $e');
    }
  }

  void _getAppSignature() async {
    try {
      final otpInteractor = OTPInteractor();
      final appSignature = await otpInteractor.getAppSignature();
      debugPrint('📱 Your app signature: $appSignature');
    } catch (e) {
      debugPrint('❌ Error getting app signature: $e');
    }
  }

  @override
  void dispose() {
    if (_isInitialized) {
      _otpController.stopListen();
    }
    _pinputController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final defaultPinTheme = PinTheme(
      width: widget.fieldWidth,
      height: widget.fieldHeight,
      textStyle: widget.textStyle ?? AppTextTheme.otpInput,
      decoration: BoxDecoration(
        color: widget.fillColor ?? Colors.white,
        borderRadius: widget.borderRadius ?? BorderRadius.circular(4),
        border: Border.all(
          color: widget.borderColor ?? Colors.grey[400]!,
          width: widget.borderWidth,
        ),
        // Add subtle shadow for better visibility
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            offset: const Offset(0, 1),
            blurRadius: 2,
            spreadRadius: 0,
          ),
        ],
      ),
      margin: widget.margin ?? const EdgeInsets.symmetric(horizontal: 4),
      padding: const EdgeInsets.fromLTRB(8, 4, 8, 4), // padding: left 8, top 4, right 8, bottom 4
    );

    final focusedPinTheme = defaultPinTheme.copyWith(
      decoration: defaultPinTheme.decoration!.copyWith(
        color: widget.fillColor ?? Colors.white,
        border: Border.all(
          color: widget.focusedBorderColor ?? Theme.of(context).primaryColor,
          width: widget.borderWidth + 0.5, // Slightly thicker border when focused
        ),
        boxShadow: [
          BoxShadow(
            color: (widget.focusedBorderColor ?? Theme.of(context).primaryColor).withValues(alpha: 0.2),
            offset: const Offset(0, 1),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
    );

    final submittedPinTheme = defaultPinTheme.copyWith(
      decoration: defaultPinTheme.decoration!.copyWith(
        color: widget.fillColor ?? Colors.white,
        border: Border.all(
          color: widget.submittedBorderColor ?? Theme.of(context).primaryColor,
          width: widget.borderWidth,
        ),
        boxShadow: [
          BoxShadow(
            color: (widget.submittedBorderColor ?? Theme.of(context).primaryColor).withValues(alpha: 0.1),
            offset: const Offset(0, 1),
            blurRadius: 3,
            spreadRadius: 0,
          ),
        ],
      ),
    );

    return Pinput(
      controller: _pinputController,
      focusNode: _focusNode,
      length: widget.length,
      autofocus: widget.autoFocus,
      defaultPinTheme: defaultPinTheme,
      focusedPinTheme: focusedPinTheme,
      submittedPinTheme: submittedPinTheme,
      keyboardType: TextInputType.number,
      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
      onChanged: widget.onChanged,
      onCompleted: widget.onCompleted,
      // Simple autofill - works with keyboard suggestions and copy/paste
      autofillHints: const [AutofillHints.oneTimeCode],
      // Basic separator between boxes
      separatorBuilder: (index) => const SizedBox(width: 12),
      // Close keyboard when complete
      closeKeyboardWhenCompleted: true,
    );
  }
}
