import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import '../../constants/api_endpoints.dart';

/// Simple Profile Service
class ProfileService {
  static ProfileService? _instance;
  static ProfileService get instance => _instance ??= ProfileService._();

  late ApiHelper _apiHelper;

  ProfileService._() {
    _apiHelper = ApiHelper.instance;
  }

  Future<ApiResponse<dynamic>> registerProfile({
    required Map<String, dynamic> request,
    String? passbookImagePath,
  }) async {
    return await _apiHelper.post<dynamic>(
      ApiEndpoints.registerProfile,
      data: request,
    );
  }

  Future<ApiResponse<dynamic>> uploadDocuments({
    required String mobileNo,
    String? panCardPhotoPath,
    String? dlPhotoPath,
    String? rcPhotoPath,
    String? profilePhotoPath,
    String? selfImagePath,
  }) async {
    return await _apiHelper.post<dynamic>(
      '/Rider/IN_RiderDocumentUpload',
      data: {
        'MobileNo': mobileNo,
        'PanCardPhoto': panCardPhotoPath,
        'DLPhoto': dlPhotoPath,
        'RCPhoto': rcPhotoPath,
        'ProfilePhoto': profilePhotoPath,
        'SelfImage': selfImagePath,
      },
    );
  }

  Future<ApiResponse<dynamic>> registerWorkDetails({
    required Map<String, dynamic> request,
  }) async {
    return await _apiHelper.post<dynamic>(
      '/Rider/IN_RiderWorkDetails',
      data: request,
    );
  }

  /// Register rider profile with multipart form data (including passbook image)
  Future<ApiResponse<dynamic>> riderRegisterationInsertProfile({
    required String firstName,
    required String lastName,
    required String mobileNo,
    required String emailID,
    required String dob,
    required int genderType,
    required int maritalStatus,
    required String societyName,
    required String roomFlatNo,
    required String fullAddress,
    required String landmark,
    required String bankName,
    required String accountNumber,
    required String ifscCode,
    String? spouseName,
    String? sDob,
    String? childName,
    String? cDob,
    String? passbookImagePath,
  }) async {
    try {
      // Create query parameters
      final queryParams = <String, dynamic>{
        'FirstName': firstName,
        'LastName': lastName,
        'MobileNo': mobileNo,
        'EmailID': emailID,
        'DOB': dob,
        'GenderType': genderType,
        'MaritalStatus': maritalStatus,
        'SocietyName': societyName,
        'RoomFlatNo': roomFlatNo,
        'FullAddress': fullAddress,
        'Landmark': landmark,
        'BankName': bankName,
        'AccountNumber': accountNumber,
        'IFSCCode': ifscCode,
      };

      // Add optional fields if provided
      if (spouseName != null && spouseName.isNotEmpty) {
        queryParams['SpouseName'] = spouseName;
      }
      if (sDob != null && sDob.isNotEmpty) {
        queryParams['SDob'] = sDob;
      }
      if (childName != null && childName.isNotEmpty) {
        queryParams['ChildName'] = childName;
      }
      if (cDob != null && cDob.isNotEmpty) {
        queryParams['CDob'] = cDob;
      }

      // Create form data for multipart request
      final formData = FormData();

      // Add passbook image if provided
      if (passbookImagePath != null && passbookImagePath.isNotEmpty) {
        final file = File(passbookImagePath);
        if (await file.exists()) {
          formData.files.add(MapEntry(
            'PassbookImg',
            await MultipartFile.fromFile(
              passbookImagePath,
              filename: 'passbook_${DateTime.now().millisecondsSinceEpoch}.jpg',
            ),
          ));
        }
      } else {
        // Add empty file if no passbook image provided (API might require this field)
        formData.files.add(MapEntry(
          'PassbookImg',
          MultipartFile.fromString('', filename: 'empty.txt'),
        ));
      }

      debugPrint('📤 Uploading with query params: $queryParams');
      debugPrint('📤 Form data files: ${formData.files.length}');

      return await _apiHelper.uploadFile<dynamic>(
        '/Rider/RiderRegisterationInsertProfile',
        formData: formData,
        queryParameters: queryParams,
      );
    } catch (e) {
      debugPrint('🚨 Error in riderRegisterationInsertProfile: $e');
      return ApiResponse.error('Profile registration failed: $e');
    }
  }

  /// Register rider documents with multipart form data (PAN, DL, Selfie)
  Future<ApiResponse<dynamic>> riderRegisterationInsertDocument({
    required String mobileNo,
    required String panNo,
    required String dlNo,
    String? panCardPhotoPath,
    String? dlPhotoPath,
    String? selfImgPath,
  }) async {
    try {
      debugPrint('📄 Starting document registration for mobile: $mobileNo');

      // Create query parameters
      final queryParams = <String, dynamic>{
        'MobileNo': mobileNo,
        'PanNo': panNo,
        'DLNo': dlNo,
      };

      // Create form data for multipart request
      final formData = FormData();

      // Add PAN card photo if provided
      if (panCardPhotoPath != null && panCardPhotoPath.isNotEmpty) {
        final file = File(panCardPhotoPath);
        if (await file.exists()) {
          formData.files.add(MapEntry(
            'PanCardPhoto',
            await MultipartFile.fromFile(
              panCardPhotoPath,
              filename: 'pan_card_${DateTime.now().millisecondsSinceEpoch}.jpg',
            ),
          ));
        }
      } else {
        // Add empty file if no PAN card photo provided
        formData.files.add(MapEntry(
          'PanCardPhoto',
          MultipartFile.fromString('', filename: 'empty_pan.txt'),
        ));
      }

      // Add DL photo if provided
      if (dlPhotoPath != null && dlPhotoPath.isNotEmpty) {
        final file = File(dlPhotoPath);
        if (await file.exists()) {
          formData.files.add(MapEntry(
            'DLPhoto',
            await MultipartFile.fromFile(
              dlPhotoPath,
              filename: 'dl_photo_${DateTime.now().millisecondsSinceEpoch}.jpg',
            ),
          ));
        }
      } else {
        // Add empty file if no DL photo provided
        formData.files.add(MapEntry(
          'DLPhoto',
          MultipartFile.fromString('', filename: 'empty_dl.txt'),
        ));
      }

      // Add selfie image if provided
      if (selfImgPath != null && selfImgPath.isNotEmpty) {
        final file = File(selfImgPath);
        if (await file.exists()) {
          formData.files.add(MapEntry(
            'SelfImg',
            await MultipartFile.fromFile(
              selfImgPath,
              filename: 'selfie_${DateTime.now().millisecondsSinceEpoch}.jpg',
            ),
          ));
        }
      } else {
        // Add empty file if no selfie provided
        formData.files.add(MapEntry(
          'SelfImg',
          MultipartFile.fromString('', filename: 'empty_selfie.txt'),
        ));
      }

      debugPrint('📤 Uploading documents with query params: $queryParams');
      debugPrint('📤 Form data files: ${formData.files.length}');

      return await _apiHelper.uploadFile<dynamic>(
        '/Rider/RiderRegisterationInsertDocument',
        formData: formData,
        queryParameters: queryParams,
      );
    } catch (e) {
      debugPrint('🚨 Error in riderRegisterationInsertDocument: $e');
      return ApiResponse.error('Document registration failed: $e');
    }
  }

  /// Register rider work details
  Future<ApiResponse<dynamic>> riderRegisterationInsertWork({
    required String mobileNo,
    required String referBy,
    required int shiftID,
    required int vehicleType,
    required int slotName,
    String? subSlotName,
    String? weekoffDays,
    String? dcid, // Changed to String as API expects string
  }) async {
    try {
      debugPrint('💼 Starting work details registration for mobile: $mobileNo');

      // Create request body directly (no insertModel wrapper)
      final requestBody = {
        'mobileNo': mobileNo,
        'referBy': referBy,
        'shiftID': shiftID,
        'vehicleType': vehicleType,
        'slotName': slotName,
        'subSlotName': subSlotName ?? '',
        'weekoffDays': weekoffDays ?? '',
        'dcid': dcid ?? '1', // Default to '1' as string if not provided
      };

      debugPrint('📤 Work details request body: $requestBody');

      return await _apiHelper.post<dynamic>(
        '/Rider/RiderRegisterationInsertWork',
        data: requestBody,
      );
    } catch (e) {
      debugPrint('🚨 Error in riderRegisterationInsertWork: $e');
      return ApiResponse.error('Work details registration failed: $e');
    }
  }

  /// Get rider details by mobile number
  Future<ApiResponse<dynamic>> getRiderDetails({
    required String mobileNo,
  }) async {
    try {
      debugPrint('📥 Getting rider details for mobile: $mobileNo');

      return await _apiHelper.get<dynamic>(
        '/Rider/FE_GetRiderDetails',
        queryParameters: {
          'mob': mobileNo,
        },
      );
    } catch (e) {
      debugPrint('🚨 Error in getRiderDetails: $e');
      return ApiResponse.error('Failed to get rider details: $e');
    }
  }
}
