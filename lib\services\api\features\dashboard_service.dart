import 'package:kisankonnect_rider/services/all_services.dart';

/// Simple Dashboard Service
class DashboardService {
  static DashboardService? _instance;
  static DashboardService get instance => _instance ??= DashboardService._();

  late ApiHelper _apiHelper;

  DashboardService._() {
    _apiHelper = ApiHelper.instance;
  }

  Future<ApiResponse<dynamic>> getDashboardStoryBanner({
    required String id,
    required String kfhId,
    required String storyType,
  }) async {
    return await _apiHelper.get<dynamic>(
      ApiEndpoints.dashboardStoryBanner,
      queryParameters: {
        'ID': id,
        'KfhID': kfhId,
        'StoryType': storyType,
      },
    );
  }

  Future<ApiResponse<dynamic>> getCurrentRiderProgress({
    required String riderId,
    required String kfhId,
    required String cdcType,
  }) async {
    return await _apiHelper.get<dynamic>(
      ApiEndpoints.currentRiderProgress,
      queryParameters: {
        'RiderID': riderId,
        'KFHID': kfhId,
        'CDCType': cdcType,
      },
    );
  }
}
