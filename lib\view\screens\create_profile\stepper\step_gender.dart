import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

class StepGender extends StatefulWidget {
  final Function(Map<String, dynamic>)? onContinue;
  final VoidCallback? onBack;
  const StepGender({super.key, this.onContinue, this.onBack});

  @override
  State<StepGender> createState() => _StepGenderState();
}

class _StepGenderState extends State<StepGender> {
  String? _selectedGender;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xFFF7F8FA),
      child: Column(
        children: [
          Expanded(
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 16),
                    Text('Select your gender', style: AppTextTheme.cardTitle.copyWith(fontSize: 18)),
                    const SizedBox(height: 4),
                    Text('Please choose your gender', style: AppTextTheme.cardSubtitle),
                    const SizedBox(height: 24),
                    _buildRadio('Male'),
                    const SizedBox(height: 16),
                    _buildRadio('Female'),
                    const SizedBox(height: 16),
                    _buildRadio('Other'),
                  ],
                ),
              ),
            ),
          ),
          // Continue button
          Container(
            padding: EdgeInsets.only(
              left: 16,
              right: 16,
              bottom: MediaQuery.of(context).viewInsets.bottom + 16,
              top: 16,
            ),
            decoration: const BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black12,
                  blurRadius: 4,
                  offset: Offset(0, -2),
                ),
              ],
            ),
            child: SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: _selectedGender != null
                    ? () {
                        widget.onContinue?.call({'gender': _selectedGender});
                      }
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _selectedGender != null ? AppColors.green : Colors.grey,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(32),
                  ),
                ),
                child: const Text(
                  'Continue',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRadio(String value) {
    return InkWell(
      onTap: () => setState(() => _selectedGender = value),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: _selectedGender == value ? AppColors.green : Colors.grey.shade300,
            width: 2,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: _selectedGender == value ? AppColors.green : Colors.grey.shade400,
                  width: 2,
                ),
              ),
              child: _selectedGender == value
                  ? Center(
                      child: Container(
                        width: 12,
                        height: 12,
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                          color: AppColors.green,
                        ),
                      ),
                    )
                  : null,
            ),
            const SizedBox(width: 16),
            Text(
              value,
              style: AppTextTheme.cardTitle,
            ),
          ],
        ),
      ),
    );
  }
}
