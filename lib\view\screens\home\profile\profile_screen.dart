import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/view/widgets/common/common_app_bar.dart';
import 'package:kisankonnect_rider/view/widgets/common/language_toggle_widget.dart';

import 'package:kisankonnect_rider/controllers/auth_controller.dart';
import 'package:kisankonnect_rider/controllers/profile_controller.dart';
import 'package:kisankonnect_rider/controllers/cash_balance_controller.dart';

import 'package:get/get.dart';
import 'dart:io';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  bool _isLoading = true;
  final AuthController _authController = Get.find<AuthController>();
  final ProfileController _profileController = Get.find<ProfileController>();
  final CashBalanceController _cashBalanceController = Get.find<CashBalanceController>();
  final BiometricService _biometricService = BiometricService.instance;
  final RiderDetailsService _riderDetailsService = RiderDetailsService.instance;
  Map<String, String?> _storedLoginInfo = {};
  String? _profileImagePath;
  String? _riderName;
  String? _riderMobile;

  // Biometric settings state
  bool _isBiometricEnabled = false;
  bool _isBiometricAvailable = false;
  bool _isBiometricToggling = false;

  @override
  void initState() {
    super.initState();
    _loadProfileData();
    _initializeBiometricSettings();
    _loadCashBalance();
  }

  void _loadProfileData() async {
    // Load stored login information
    final loginInfo = await _authController.getStoredLoginInfo();

    // Try to get the selfie image path from profile controller
    final imagePaths = _profileController.imagePaths;
    final selfieImagePath = imagePaths['selfie'];

    // Load rider details from RiderDetailsService
    await _loadRiderDetailsFromService();

    setState(() {
      _storedLoginInfo = loginInfo;
      _profileImagePath = selfieImagePath;
      // Hide loading immediately after data is loaded
      _isLoading = false;
    });
  }

  /// Load rider details from RiderDetailsService
  Future<void> _loadRiderDetailsFromService() async {
    try {
      debugPrint('👤 Loading rider details from service');

      // Get mobile number from storage
      final mobileNumber = await _authController.getStoredMobileNumber();

      if (mobileNumber != null && mobileNumber.isNotEmpty) {
        // Try to get cached rider details first
        final riderDetails = await _riderDetailsService.getRiderDetails(mobileNumber);

        if (riderDetails != null && riderDetails.riderDetail != null) {
          final detail = riderDetails.riderDetail!;
          _riderName = detail.fullName;
          _riderMobile = detail.mobileNo;
          debugPrint('✅ Rider details loaded: $_riderName, $_riderMobile');
        } else {
          debugPrint('❌ No rider details found, using fallback');
          // Fallback to individual fields from storage
          _riderName = await _riderDetailsService.getRiderFullName();
          _riderMobile = await _riderDetailsService.getRiderDetailField('rider_mobile');
        }
      } else {
        debugPrint('❌ No mobile number found');
      }
    } catch (e) {
      debugPrint('🚨 Error loading rider details from service: $e');
      // Fallback to stored values
      _riderName = await _riderDetailsService.getRiderFullName();
      _riderMobile = await _riderDetailsService.getRiderDetailField('rider_mobile');
    }
  }

  /// Load cash balance when profile screen is accessed
  void _loadCashBalance() async {
    try {
      debugPrint('💰 Loading cash balance from profile screen');
      await _cashBalanceController.loadCashBalance();
    } catch (e) {
      debugPrint('🚨 Error loading cash balance in profile screen: $e');
    }
  }

  void _showLogoutDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          Obx(() => TextButton(
                onPressed: _authController.isLoading
                    ? null
                    : () {
                        Get.back();
                        _performLogout();
                      },
                child: _authController.isLoading
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.red),
                        ),
                      )
                    : const Text('Logout', style: TextStyle(color: Colors.red)),
              )),
        ],
      ),
    );
  }

  void _performLogout() async {
    try {
      // Show loading dialog
      Get.dialog(
        const AlertDialog(
          content: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Logging out...'),
            ],
          ),
        ),
        barrierDismissible: false,
      );

      // Perform logout
      await _authController.logout();

      // Close loading dialog if still open
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }
    } catch (e) {
      // Close loading dialog if still open
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      Get.snackbar(
        'Error',
        'Failed to logout properly. Please try again.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CommonAppBar(
        titleKey: 'profile',
        automaticallyImplyLeading: false,
      ),
      body: _isLoading ? const Center(child: CircularProgressIndicator()) : _buildProfileContent(context),
    );
  }

  Widget _buildProfileContent(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    // Responsive sizing
    final horizontalPadding = screenWidth * 0.04;
    final verticalPadding = screenHeight * 0.015;
    final borderRadius = screenWidth * 0.03;

    return SingleChildScrollView(
      child: Column(
        children: [
          // Profile Header Section
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: AppColors.cardBackground,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(borderRadius),
                bottomRight: Radius.circular(borderRadius),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  offset: const Offset(0, 2),
                  blurRadius: 8,
                  spreadRadius: 0,
                ),
              ],
            ),
            padding: EdgeInsets.all(horizontalPadding * 1.25),
            child: Row(
              children: [
                // Profile Image
                Container(
                  width: screenWidth * 0.2,
                  height: screenWidth * 0.2,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(borderRadius),
                    image: DecorationImage(
                      image: _profileImagePath != null && File(_profileImagePath!).existsSync()
                          ? FileImage(File(_profileImagePath!))
                          : const AssetImage('assets/images/rider_image.png') as ImageProvider,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                SizedBox(width: horizontalPadding),
                // Profile Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _riderName ?? _storedLoginInfo['rider_name'] ?? AppStrings.get('riderName'),
                        style: AppTextTheme.cardTitle.copyWith(
                          fontSize: AppTextTheme.getResponsiveFontSize(context, 20),
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      SizedBox(height: verticalPadding * 0.3),
                      Text(
                        _riderMobile ?? _storedLoginInfo['mobile_no'] ?? AppStrings.get('phoneNumber'),
                        style: AppTextTheme.cardSubtitle.copyWith(
                          fontSize: AppTextTheme.getResponsiveFontSize(context, 14),
                          color: AppColors.textSecondary,
                        ),
                      ),
                      if (_storedLoginInfo['user_name'] != null) ...[
                        SizedBox(height: verticalPadding * 0.2),
                        Text(
                          'ID: ${_storedLoginInfo['user_name'] ?? 'Loading...'}',
                          style: AppTextTheme.cardSubtitle.copyWith(
                            fontSize: AppTextTheme.getResponsiveFontSize(context, 12),
                            color: AppColors.textLight,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                // Edit Icon
                GestureDetector(
                  onTap: () {
                    Get.toNamed('/create-profile');
                  },
                  child: Container(
                    width: screenWidth * 0.08,
                    height: screenWidth * 0.08,
                    decoration: BoxDecoration(
                      color: AppColors.green,
                      borderRadius: BorderRadius.circular(borderRadius * 0.7),
                    ),
                    child: Icon(
                      Icons.edit,
                      color: AppColors.cardBackground,
                      size: AppTextTheme.getResponsiveFontSize(context, 18),
                    ),
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: verticalPadding * 1.1),

          // Menu Items
          _buildMenuItem(
            context: context,
            icon: Icons.badge_outlined,
            title: AppStrings.get('idCard'),
            horizontalPadding: horizontalPadding,
            verticalPadding: verticalPadding,
            borderRadius: borderRadius,
            onTap: () {
              // Navigate to ID Card screen
            },
          ),

          Obx(() => _buildMenuItem(
                context: context,
                icon: Icons.account_balance_wallet_outlined,
                title: AppStrings.get('cashBalance'),
                badge: _cashBalanceController.isLoading
                    ? 'Loading...'
                    : _cashBalanceController.hasError
                        ? 'Error'
                        : _cashBalanceController.formattedCashBalance,
                badgeColor: _cashBalanceController.hasError ? AppColors.error : AppColors.error,
                horizontalPadding: horizontalPadding,
                verticalPadding: verticalPadding,
                borderRadius: borderRadius,
                onTap: () {
                  // Refresh cash balance on tap
                  _cashBalanceController.refreshCashBalance();
                },
              )),

          _buildMenuItem(
            context: context,
            icon: Icons.access_time_outlined,
            title: AppStrings.get('myShift'),
            horizontalPadding: horizontalPadding,
            verticalPadding: verticalPadding,
            borderRadius: borderRadius,
            onTap: () {
              // Navigate to My Shift screen
            },
          ),

          _buildMenuItem(
            context: context,
            icon: Icons.location_on_outlined,
            title: AppStrings.get('myKFHLocation'),
            horizontalPadding: horizontalPadding,
            verticalPadding: verticalPadding,
            borderRadius: borderRadius,
            onTap: () {
              // Navigate to KFH Location screen
            },
          ),

          _buildMenuItem(
            context: context,
            icon: Icons.history_outlined,
            title: AppStrings.get('tripHistory'),
            horizontalPadding: horizontalPadding,
            verticalPadding: verticalPadding,
            borderRadius: borderRadius,
            onTap: () {
              // Navigate to Trip History screen
            },
          ),

          _buildMenuItem(
            context: context,
            icon: Icons.store_outlined,
            title: AppStrings.get('kisanStore'),
            badge: AppStrings.get('newTrends'),
            badgeColor: AppColors.warning,
            horizontalPadding: horizontalPadding,
            verticalPadding: verticalPadding,
            borderRadius: borderRadius,
            onTap: () {
              // Get.to(() => const KisanStoreScreen());
            },
          ),

          _buildMenuItem(
            context: context,
            icon: Icons.group_outlined,
            title: AppStrings.get('referAndEarn'),
            subtitle: AppStrings.get('referralBonusText'),
            horizontalPadding: horizontalPadding,
            verticalPadding: verticalPadding,
            borderRadius: borderRadius,
            onTap: () {
              // Navigate to Refer & Earn screen
            },
          ),

          _buildMenuItem(
            context: context,
            icon: Icons.help_outline,
            title: AppStrings.get('helpAndSupport'),
            horizontalPadding: horizontalPadding,
            verticalPadding: verticalPadding,
            borderRadius: borderRadius,
            onTap: () {
              // Navigate to Help & Support screen
            },
          ),

          _buildBiometricSettingsItem(
            context: context,
            horizontalPadding: horizontalPadding,
            verticalPadding: verticalPadding,
            borderRadius: borderRadius,
          ),

          _buildMenuItem(
            context: context,
            icon: Icons.logout,
            title: 'Logout',
            horizontalPadding: horizontalPadding,
            verticalPadding: verticalPadding,
            borderRadius: borderRadius,
            onTap: _showLogoutDialog,
          ),

          // SizedBox(height: verticaladding * 1.1),

          const LanguageToggleWidget(),

          SizedBox(height: verticalPadding * 1.3),

          // App Version
          Container(
            padding: EdgeInsets.all(horizontalPadding),
            child: Text(
              AppStrings.get('appVersion'),
              style: AppTextTheme.cardSubtitle.copyWith(
                fontSize: AppTextTheme.getResponsiveFontSize(context, 12),
                color: AppColors.textLight,
              ),
            ),
          ),

          SizedBox(height: verticalPadding * 1.3),
        ],
      ),
    );
  }

  Widget _buildDataRow(String label, String value, BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: AppTextTheme.cardSubtitle.copyWith(
                fontSize: AppTextTheme.getResponsiveFontSize(context, 12),
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextTheme.cardSubtitle.copyWith(
                fontSize: AppTextTheme.getResponsiveFontSize(context, 12),
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem({
    required BuildContext context,
    required IconData icon,
    required String title,
    String? subtitle,
    String? badge,
    Color? badgeColor,
    required double horizontalPadding,
    required double verticalPadding,
    required double borderRadius,
    required VoidCallback onTap,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: horizontalPadding, vertical: verticalPadding * 0.3),
      child: Material(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(borderRadius),
        elevation: 2,
        shadowColor: Colors.black.withValues(alpha: 0.1),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(borderRadius),
          child: Container(
            padding: EdgeInsets.all(horizontalPadding),
            child: Row(
              children: [
                // Icon Container
                Container(
                  width: screenWidth * 0.1,
                  height: screenWidth * 0.1,
                  decoration: BoxDecoration(
                    color: AppColors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(borderRadius * 0.8),
                  ),
                  child: Icon(
                    icon,
                    color: AppColors.green,
                    size: AppTextTheme.getResponsiveFontSize(context, 22),
                  ),
                ),

                SizedBox(width: horizontalPadding),

                // Title and Subtitle
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: AppTextTheme.cardTitle.copyWith(
                          fontSize: AppTextTheme.getResponsiveFontSize(context, 16),
                          fontWeight: FontWeight.w600,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      if (subtitle != null) ...[
                        SizedBox(height: verticalPadding * 0.3),
                        Text(
                          subtitle,
                          style: AppTextTheme.cardSubtitle.copyWith(
                            fontSize: AppTextTheme.getResponsiveFontSize(context, 12),
                            color: AppColors.green,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                // Badge
                if (badge != null) ...[
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: horizontalPadding * 0.5, vertical: verticalPadding * 0.3),
                    decoration: BoxDecoration(
                      color: badgeColor ?? AppColors.green,
                      borderRadius: BorderRadius.circular(borderRadius),
                    ),
                    child: Text(
                      badge,
                      style: AppTextTheme.cardSubtitle.copyWith(
                        color: AppColors.cardBackground,
                        fontSize: AppTextTheme.getResponsiveFontSize(context, 12),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  SizedBox(width: horizontalPadding * 0.5),
                ],

                // Arrow Icon
                Icon(
                  Icons.arrow_forward_ios,
                  size: AppTextTheme.getResponsiveFontSize(context, 16),
                  color: AppColors.textLight,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Initialize biometric settings
  void _initializeBiometricSettings() async {
    try {
      debugPrint('🔐 Initializing biometric settings...');

      // Initialize biometric service
      await _biometricService.initialize();

      // Check biometric availability and current status
      final biometricInfo = await _biometricService.getBiometricInfo();

      setState(() {
        _isBiometricAvailable = biometricInfo['isAvailable'] ?? false;
        _isBiometricEnabled = biometricInfo['isEnabled'] ?? false;
      });

      debugPrint('✅ Biometric settings initialized - Available: $_isBiometricAvailable, Enabled: $_isBiometricEnabled');
    } catch (e) {
      debugPrint('🚨 Error initializing biometric settings: $e');
      setState(() {
        _isBiometricAvailable = false;
        _isBiometricEnabled = false;
      });
    }
  }

  /// Build biometric settings menu item
  Widget _buildBiometricSettingsItem({
    required BuildContext context,
    required double horizontalPadding,
    required double verticalPadding,
    required double borderRadius,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: horizontalPadding, vertical: verticalPadding * 0.3),
      child: Material(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(borderRadius),
        elevation: 2,
        shadowColor: Colors.black.withValues(alpha: 0.1),
        child: Container(
          padding: EdgeInsets.all(horizontalPadding),
          child: Row(
            children: [
              // Icon Container
              Container(
                width: screenWidth * 0.1,
                height: screenWidth * 0.1,
                decoration: BoxDecoration(
                  color: AppColors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(borderRadius * 0.8),
                ),
                child: Icon(
                  Icons.fingerprint,
                  color: AppColors.green,
                  size: AppTextTheme.getResponsiveFontSize(context, 22),
                ),
              ),

              SizedBox(width: horizontalPadding),

              // Title and Subtitle
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Biometric Authentication',
                      style: AppTextTheme.cardTitle.copyWith(
                        fontSize: AppTextTheme.getResponsiveFontSize(context, 16),
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    SizedBox(height: verticalPadding * 0.3),
                    Text(
                      _isBiometricAvailable
                          ? 'Use fingerprint or face ID for security verification'
                          : 'Biometric authentication not available',
                      style: AppTextTheme.cardSubtitle.copyWith(
                        fontSize: AppTextTheme.getResponsiveFontSize(context, 12),
                        color: AppColors.green,
                      ),
                    ),
                  ],
                ),
              ),

              // Switch or Block Icon
              _isBiometricAvailable
                  ? _isBiometricToggling
                      ? SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(AppColors.green),
                          ),
                        )
                      : Switch.adaptive(
                          value: _isBiometricEnabled,
                          onChanged: _toggleBiometricSetting,
                          activeColor: AppColors.green,
                          inactiveThumbColor: Colors.grey[400],
                          inactiveTrackColor: Colors.grey[300],
                          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        )
                  : Icon(
                      Icons.block,
                      size: AppTextTheme.getResponsiveFontSize(context, 16),
                      color: AppColors.textLight,
                    ),
            ],
          ),
        ),
      ),
    );
  }

  /// Toggle biometric setting
  void _toggleBiometricSetting(bool value) async {
    debugPrint('🔐 Toggle biometric called with value: $value');
    debugPrint('🔐 Biometric available: $_isBiometricAvailable');
    debugPrint('🔐 Biometric enabled: $_isBiometricEnabled');

    if (!_isBiometricAvailable) {
      debugPrint('🔐 Biometric not available, showing snackbar');
      Get.snackbar(
        'Biometric Unavailable',
        'Biometric authentication is not available on this device',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.warning,
        colorText: Colors.white,
      );
      return;
    }

    // Prevent toggle if already in the same state
    if (_isBiometricEnabled == value) {
      debugPrint('🔐 Biometric already in desired state: $value');
      return;
    }

    // Prevent multiple simultaneous toggles
    if (_isBiometricToggling) {
      debugPrint('🔐 Biometric toggle already in progress');
      return;
    }

    // Set loading state
    setState(() {
      _isBiometricToggling = true;
    });

    try {
      debugPrint('🔐 Toggling biometric setting to: $value');

      bool success = false;

      if (value) {
        // Enable biometric authentication
        success = await _biometricService.enableBiometric();

        if (success) {
          // Store current user credentials for biometric login
          final mobileNumber = _storedLoginInfo['mobileNumber'];
          if (mobileNumber != null) {
            await _biometricService.storeUserCredentials(
              mobileNumber: mobileNumber,
              userId: _storedLoginInfo['userId'],
            );
          }
        }
      } else {
        // Disable biometric authentication
        success = await _biometricService.disableBiometric();
      }

      if (success) {
        // Update state and refresh UI
        setState(() {
          _isBiometricEnabled = value;
        });

        // Verify the state was actually updated
        await _refreshBiometricState();

        // Show confirmation message
        Get.snackbar(
          value ? 'Biometric Enabled' : 'Biometric Disabled',
          value
              ? 'Biometric security verification is now enabled'
              : 'Biometric security verification has been disabled',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: value ? AppColors.success : AppColors.green,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );

        debugPrint('✅ Biometric setting updated successfully to: $_isBiometricEnabled');
      } else {
        debugPrint('❌ Failed to update biometric setting');

        // Refresh state to ensure UI is consistent
        await _refreshBiometricState();

        // Show error message
        Get.snackbar(
          'Error',
          value
              ? 'Failed to enable biometric authentication. Please try again.'
              : 'Failed to disable biometric authentication. Please try again.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.error,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      debugPrint('🚨 Error toggling biometric setting: $e');

      // Refresh state to ensure UI is consistent
      await _refreshBiometricState();

      Get.snackbar(
        'Error',
        'Failed to update biometric setting. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: Colors.white,
      );
    } finally {
      // Always reset loading state
      setState(() {
        _isBiometricToggling = false;
      });
    }
  }

  /// Refresh biometric state from storage
  Future<void> _refreshBiometricState() async {
    try {
      final biometricInfo = await _biometricService.getBiometricInfo();
      setState(() {
        _isBiometricAvailable = biometricInfo['isAvailable'] ?? false;
        _isBiometricEnabled = biometricInfo['isEnabled'] ?? false;
      });
      debugPrint('🔄 Biometric state refreshed - Available: $_isBiometricAvailable, Enabled: $_isBiometricEnabled');
    } catch (e) {
      debugPrint('🚨 Error refreshing biometric state: $e');
    }
  }
}
