import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

/// Widget for biometric login functionality
class BiometricLoginWidget extends StatefulWidget {
  final VoidCallback? onBiometricSuccess;
  final VoidCallback? onBiometricError;
  final bool showTitle;
  final String? customTitle;
  final String? customSubtitle;

  const BiometricLoginWidget({
    super.key,
    this.onBiometricSuccess,
    this.onBiometricError,
    this.showTitle = true,
    this.customTitle,
    this.customSubtitle,
  });

  @override
  State<BiometricLoginWidget> createState() => _BiometricLoginWidgetState();
}

class _BiometricLoginWidgetState extends State<BiometricLoginWidget> with SingleTickerProviderStateMixin {
  final BiometricService _biometricService = BiometricService.instance;
  bool _isLoading = false;
  bool _isBiometricAvailable = false;
  bool _isBiometricEnabled = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _checkBiometricStatus();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  Future<void> _checkBiometricStatus() async {
    try {
      await _biometricService.initialize();
      final biometricInfo = await _biometricService.getBiometricInfo();

      setState(() {
        _isBiometricAvailable = biometricInfo['isAvailable'] ?? false;
        _isBiometricEnabled = biometricInfo['isEnabled'] ?? false;
      });

      debugPrint('🔐 Biometric status - Available: $_isBiometricAvailable, Enabled: $_isBiometricEnabled');
    } catch (e) {
      debugPrint('🚨 Error checking biometric status: $e');
      setState(() {
        _isBiometricAvailable = false;
        _isBiometricEnabled = false;
      });
    }
  }

  Future<void> _performBiometricLogin() async {
    if (!_isBiometricAvailable || !_isBiometricEnabled) {
      Get.snackbar(
        'Biometric Unavailable',
        'Biometric authentication is not available or enabled.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      widget.onBiometricError?.call();
      return;
    }

    setState(() {
      _isLoading = true;
    });

    _animationController.forward();

    try {
      debugPrint('🔐 Starting biometric authentication...');

      // Retrieve user credentials with biometric authentication
      final credentials = await _biometricService.getUserCredentials();

      if (credentials != null) {
        debugPrint('✅ Biometric authentication successful');

        // Show success feedback
        Get.snackbar(
          'Authentication Successful',
          'Welcome back! You have been logged in successfully.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.success,
          colorText: Colors.white,
          duration: const Duration(seconds: 2),
        );

        widget.onBiometricSuccess?.call();
      } else {
        debugPrint('❌ Biometric authentication failed - no credentials');
        widget.onBiometricError?.call();
      }
    } catch (e) {
      debugPrint('🚨 Biometric authentication error: $e');
      widget.onBiometricError?.call();
    } finally {
      _animationController.reverse();
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Don't show the widget if biometric is not available or enabled
    if (!_isBiometricAvailable || !_isBiometricEnabled) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        if (widget.showTitle) ...[
          Text(
            widget.customTitle ?? 'Quick Login',
            style: AppTextTheme.cardTitle.copyWith(
              fontSize: AppTextTheme.getResponsiveFontSize(context, 18),
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            widget.customSubtitle ?? 'Use your fingerprint or face ID to login quickly',
            style: AppTextTheme.cardSubtitle.copyWith(
              fontSize: AppTextTheme.getResponsiveFontSize(context, 14),
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
        ],

        // Biometric Login Button
        AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: GestureDetector(
                onTap: _isLoading ? null : _performBiometricLogin,
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.green.withValues(alpha: 0.1),
                    border: Border.all(
                      color: AppColors.green,
                      width: 2,
                    ),
                  ),
                  child: _isLoading
                      ? const CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(AppColors.green),
                          strokeWidth: 2,
                        )
                      : Icon(
                          Icons.fingerprint,
                          size: 40,
                          color: AppColors.green,
                        ),
                ),
              ),
            );
          },
        ),

        const SizedBox(height: 16),

        // Tap to authenticate text
        Text(
          _isLoading ? 'Authenticating...' : 'Tap to authenticate',
          style: AppTextTheme.cardCaption.copyWith(
            fontSize: AppTextTheme.getResponsiveFontSize(context, 12),
            color: AppColors.textLight,
          ),
        ),
      ],
    );
  }
}

/// Compact biometric login button for use in forms
class CompactBiometricButton extends StatefulWidget {
  final VoidCallback? onBiometricSuccess;
  final VoidCallback? onBiometricError;

  const CompactBiometricButton({
    super.key,
    this.onBiometricSuccess,
    this.onBiometricError,
  });

  @override
  State<CompactBiometricButton> createState() => _CompactBiometricButtonState();
}

class _CompactBiometricButtonState extends State<CompactBiometricButton> {
  final BiometricService _biometricService = BiometricService.instance;
  bool _isLoading = false;
  bool _isBiometricAvailable = false;
  bool _isBiometricEnabled = false;

  @override
  void initState() {
    super.initState();
    _checkBiometricStatus();
  }

  Future<void> _checkBiometricStatus() async {
    try {
      await _biometricService.initialize();
      final biometricInfo = await _biometricService.getBiometricInfo();

      setState(() {
        _isBiometricAvailable = biometricInfo['isAvailable'] ?? false;
        _isBiometricEnabled = biometricInfo['isEnabled'] ?? false;
      });
    } catch (e) {
      debugPrint('🚨 Error checking biometric status: $e');
      setState(() {
        _isBiometricAvailable = false;
        _isBiometricEnabled = false;
      });
    }
  }

  Future<void> _performBiometricLogin() async {
    if (!_isBiometricAvailable || !_isBiometricEnabled) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final credentials = await _biometricService.getUserCredentials();

      if (credentials != null) {
        widget.onBiometricSuccess?.call();
      } else {
        widget.onBiometricError?.call();
      }
    } catch (e) {
      debugPrint('🚨 Biometric authentication error: $e');
      widget.onBiometricError?.call();
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Don't show the button if biometric is not available or enabled
    if (!_isBiometricAvailable || !_isBiometricEnabled) {
      return const SizedBox.shrink();
    }

    return IconButton(
      onPressed: _isLoading ? null : _performBiometricLogin,
      icon: _isLoading
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.green),
              ),
            )
          : const Icon(
              Icons.fingerprint,
              color: AppColors.green,
              size: 28,
            ),
      tooltip: 'Login with biometrics',
    );
  }
}
