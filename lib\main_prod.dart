import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/bindings/app_bindings.dart';
import 'package:kisankonnect_rider/l10n/app_localizations.dart';
import 'package:kisankonnect_rider/routes/app_pages.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'config/flavor_config.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize production environment
  await Environment.init(flavor: Flavor.production);

  // Initialize all dependencies using centralized bindings
  InitialBindings().dependencies();

  // Initialize Maps Service
  await MapsService.instance.initialize();

  // Set production-specific configurations
  await _setupProductionConfig();

  // Run the app
  runApp(const MyProductionApp());
}

Future<void> _setupProductionConfig() async {
  // Disable debug prints in production
  if (!Environment.enableLogging) {
    debugPrint = (String? message, {int? wrapWidth}) {};
  }

  // Set production-specific system UI
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.green, // Green for production
      statusBarIconBrightness: Brightness.light,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Production-specific initialization
  if (Environment.enableCrashlytics) {
    // Initialize Firebase Crashlytics here
    debugPrint('🔥 Crashlytics enabled for production');
  }
}

class MyProductionApp extends StatefulWidget {
  const MyProductionApp({super.key});

  @override
  State<MyProductionApp> createState() => _MyProductionAppState();
}

class _MyProductionAppState extends State<MyProductionApp> {
  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'KisanKonnect Rider',
      debugShowCheckedModeBanner: false,
      theme: appTheme,
      initialRoute: AppRoutes.splash,
      getPages: AppPages.pages,

      // Localization
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: LocalizationService.supportedLocales,
      fallbackLocale: LocalizationService.fallbackLocale,
      locale: Get.find<LocalizationService>().locale,
    );
  }
}
