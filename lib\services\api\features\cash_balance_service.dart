import '../api_helper.dart';

/// Simple Cash Balance Service
class CashBalanceService {
  static CashBalanceService? _instance;
  static CashBalanceService get instance => _instance ??= CashBalanceService._();

  late ApiHelper _apiHelper;

  CashBalanceService._() {
    _apiHelper = ApiHelper.instance;
  }

  Future<ApiResponse<dynamic>> getRiderCODLimit({
    required String userId,
  }) async {
    return await _apiHelper.get<dynamic>(
      '/Rider/FE_KFHRiderCODLimit',
      queryParameters: {
        'Userid': userId,
      },
    );
  }

  Future<ApiResponse<dynamic>> getCashBalance({
    required String userId,
  }) async {
    return await _apiHelper.get<dynamic>(
      '/Rider/FE_KFHRiderCODLimit',
      queryParameters: {
        'Userid': userId,
      },
    );
  }
}
