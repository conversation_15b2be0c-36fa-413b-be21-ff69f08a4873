import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/utils/responsive_utils.dart';

class BadgeStatusViewer extends StatefulWidget {
  final List<String> statusImages;
  final int initialIndex;
  final Function(int) onStatusViewed;

  const BadgeStatusViewer({
    super.key,
    required this.statusImages,
    required this.initialIndex,
    required this.onStatusViewed,
  });

  @override
  State<BadgeStatusViewer> createState() => _BadgeStatusViewerState();
}

class _BadgeStatusViewerState extends State<BadgeStatusViewer> with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _progressController;
  int _currentIndex = 0;
  bool _isPaused = false;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
    _progressController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );
    _startProgress();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  void _startProgress() {
    _progressController.reset();
    _progressController.forward().then((_) {
      if (!_isPaused && mounted) {
        _nextStatus();
      }
    });
  }

  void _nextStatus() {
    if (_currentIndex < widget.statusImages.length - 1) {
      setState(() {
        _currentIndex++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      _startProgress();
    } else {
      _closeViewer();
    }
  }

  void _previousStatus() {
    if (_currentIndex > 0) {
      setState(() {
        _currentIndex--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      _startProgress();
    }
  }

  void _closeViewer() {
    widget.onStatusViewed(_currentIndex);
    Navigator.of(context).pop();
  }

  void _pauseProgress() {
    setState(() {
      _isPaused = true;
    });
    _progressController.stop();
  }

  void _resumeProgress() {
    setState(() {
      _isPaused = false;
    });
    _progressController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTapDown: (_) => _pauseProgress(),
        onTapUp: (_) => _resumeProgress(),
        onTapCancel: () => _resumeProgress(),
        onLongPressStart: (_) => _pauseProgress(),
        onLongPressEnd: (_) => _resumeProgress(),
        child: Stack(
          children: [
            // Status Images
            PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
                _startProgress();
              },
              itemCount: widget.statusImages.length,
              itemBuilder: (context, index) {
                return Container(
                  width: double.infinity,
                  height: double.infinity,
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: NetworkImage(widget.statusImages[index]),
                      fit: BoxFit.cover,
                    ),
                  ),
                );
              },
            ),

            // Progress Indicators
            Positioned(
              top: ResponsiveUtils.height(context, 6),
              left: ResponsiveUtils.spacingS(context),
              right: ResponsiveUtils.spacingS(context),
              child: Row(
                children: List.generate(
                  widget.statusImages.length,
                  (index) => Expanded(
                    child: Container(
                      height: ResponsiveUtils.height(context, 0.3),
                      margin: EdgeInsets.symmetric(
                        horizontal: ResponsiveUtils.spacingXS(context) / 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(2),
                      ),
                      child: index == _currentIndex
                          ? AnimatedBuilder(
                              animation: _progressController,
                              builder: (context, child) {
                                return LinearProgressIndicator(
                                  value: _progressController.value,
                                  backgroundColor: Colors.transparent,
                                  valueColor: const AlwaysStoppedAnimation<Color>(
                                    Colors.white,
                                  ),
                                  borderRadius: BorderRadius.circular(2),
                                );
                              },
                            )
                          : Container(
                              decoration: BoxDecoration(
                                color: index < _currentIndex ? Colors.white : Colors.transparent,
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                    ),
                  ),
                ),
              ),
            ),

            // Close Button
            Positioned(
              top: ResponsiveUtils.height(context, 5),
              right: ResponsiveUtils.spacingS(context),
              child: SafeArea(
                child: IconButton(
                  onPressed: _closeViewer,
                  icon: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 30,
                  ),
                ),
              ),
            ),

            // Navigation Areas
            Positioned(
              left: 0,
              top: 0,
              bottom: 0,
              width: ResponsiveUtils.width(context, 30),
              child: GestureDetector(
                onTap: _previousStatus,
                child: Container(color: Colors.transparent),
              ),
            ),
            Positioned(
              right: 0,
              top: 0,
              bottom: 0,
              width: ResponsiveUtils.width(context, 30),
              child: GestureDetector(
                onTap: _nextStatus,
                child: Container(color: Colors.transparent),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
