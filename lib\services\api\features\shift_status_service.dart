import '../api_helper.dart';

/// Simple Shift Status Service
class ShiftStatusService {
  static ShiftStatusService? _instance;
  static ShiftStatusService get instance => _instance ??= ShiftStatusService._();

  late ApiHelper _apiHelper;

  ShiftStatusService._() {
    _apiHelper = ApiHelper.instance;
  }

  Future<ApiResponse<dynamic>> getCurrentRiderStatus({
    required String userId,
  }) async {
    return await _apiHelper.get<dynamic>(
      '/Rider/FE_CurrentRiderStatus',
      queryParameters: {'SRID': userId},
    );
  }

  Future<ApiResponse<dynamic>> updateRiderStatus({
    required String deliveryDate,
    required String userid,
    required String remark,
    required String dcid,
    required String cdcType,
    required String status,
  }) async {
    return await _apiHelper.post<dynamic>(
      '/Rider/IN_KHFRiderAvailableStatus',
      data: {
        'deliveryDate': deliveryDate,
        'userid': int.tryParse(userid) ?? 0,
        'remark': remark,
        'dcid': int.tryParse(dcid) ?? 0,
        'cdcType': int.tryParse(cdcType) ?? 0,
        'status': int.tryParse(status) ?? 0,
      },
    );
  }

  Future<ApiResponse<dynamic>> submitBreakTime({
    required String userId,
    required String breakType,
    required String breakDuration,
  }) async {
    return await _apiHelper.post<dynamic>(
      '/Rider/IN_RiderBreakTime',
      data: {
        'userId': userId,
        'breakType': breakType,
        'breakDuration': breakDuration,
      },
    );
  }
}
