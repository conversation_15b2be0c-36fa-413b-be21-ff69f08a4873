import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import '../../../widgets/forms/common_text_field.dart';
import '../../../widgets/common/common_elevated_button.dart';
import '../../../widgets/dialogs/time_slot_selection_dialog.dart';
import '../../../widgets/dialogs/konnector_id_dialog.dart';
import '../../../../controllers/common_data_controller.dart';
import '../../../../models/common_data_models.dart';
import 'package:get/get.dart';

class StepWorkDetails extends StatefulWidget {
  final Function(Map<String, dynamic>)? onContinue;
  final VoidCallback? onSkip;
  final VoidCallback? onBack;
  const StepWorkDetails({super.key, this.onContinue, this.onSkip, this.onBack});

  @override
  State<StepWorkDetails> createState() => _StepWorkDetailsState();
}

class _StepWorkDetailsState extends State<StepWorkDetails> {
  late final Function(Map<String, dynamic>) onContinue;
  late final VoidCallback onSkip;
  late final VoidCallback? onBack;
  final _referralController = TextEditingController();
  final _konnektorIdController = TextEditingController();
  final _vehicleNumberController = TextEditingController();

  // Track referral code input state
  bool _hasReferralCode = false;

  final _scrollController = ScrollController();
  final _referralFocus = FocusNode();
  final _konnektorIdFocus = FocusNode();
  final _vehicleNumberFocus = FocusNode();

  // Common data controller for dynamic data
  final CommonDataController _commonDataController = Get.find<CommonDataController>();

  String? _deliveryType;
  String? _shift;
  String? _vehicle;
  String? _weekOff;
  String? _depot;

  // Collected work details data
  final Map<String, dynamic> _workDetailsData = {};

  // Dynamic delivery type options from API (rider types)
  List<Map<String, String>> get _deliveryTypeOptions {
    final riderTypes = _commonDataController.riderTypes;

    return riderTypes.map((riderType) {
      String subtitle = '';

      // Add subtitles based on rider type
      switch (riderType.riderType.toLowerCase()) {
        case 'regular':
          subtitle = 'Full time delivery partner';
          break;
        case 'weekend':
          subtitle = 'Weekend only deliveries';
          break;
        case 'parttime':
          subtitle = 'Flexible working hours';
          break;
        default:
          subtitle = '';
      }

      return {
        'id': riderType.rid.toString(),
        'title': riderType.riderType,
        'subtitle': subtitle,
      };
    }).toList();
  }

  // Dynamic shifts from API
  List<Map<String, dynamic>> get _shifts {
    final shiftMasters = _commonDataController.shiftMasters;

    return shiftMasters.map((shift) {
      String icon;
      Color color;

      // Determine icon and color based on shift time
      final startTime = shift.startTime.toLowerCase();
      if (startTime.contains('7') && startTime.contains('am')) {
        icon = '☀️';
        color = const Color(0xFFFFA726);
      } else if (startTime.contains('3') && startTime.contains('pm')) {
        icon = '🌙';
        color = const Color(0xFF5C6BC0);
      } else if (startTime.contains('10') && startTime.contains('pm')) {
        icon = '🌙';
        color = const Color(0xFF5C6BC0);
      } else {
        icon = '⏰';
        color = const Color(0xFF9E9E9E);
      }

      return {
        'id': shift.id,
        'title': '${shift.shiftName} (${shift.startTime} - ${shift.endTime})',
        'subtitle': shift.noofDaysWeek,
        'earnings': shift.weeklyEarnings,
        'icon': icon,
        'color': color,
      };
    }).toList();
  }

  // Dynamic vehicle options from API
  List<Map<String, dynamic>> get _vehicleOptions {
    final vehicleTypes = _commonDataController.vehicleTypes;

    // Map API vehicle types to UI format with icons and colors
    return vehicleTypes.map((vehicleType) {
      String icon;
      Color color;

      // Map vehicle types to appropriate icons and colors
      switch (vehicleType.vehicleType.toLowerCase()) {
        case 'bike':
          icon = '🏍️';
          color = const Color(0xFFE57373);
          break;
        case 'auto':
          icon = '🛺';
          color = const Color(0xFFFFCA28);
          break;
        case 'scooter':
          icon = '🛵';
          color = const Color(0xFFFF7043);
          break;
        case 'cycle':
          icon = '🚲';
          color = const Color(0xFF42A5F5);
          break;
        case 'electric auto':
          icon = '🛺';
          color = const Color(0xFF66BB6A);
          break;
        case 'electric cycle':
          icon = '🚲';
          color = const Color(0xFF66BB6A);
          break;
        default:
          icon = '🚗';
          color = const Color(0xFF9E9E9E);
      }

      return {
        'id': vehicleType.id,
        'name': vehicleType.vehicleType,
        'icon': icon,
        'color': color,
      };
    }).toList();
  }

  final List<String> _weekOffs = ['Tuesday', 'Wednesday', 'Thursday', 'Friday', 'None'];

  // Dynamic depot options from API (KFH locations)
  List<Map<String, dynamic>> get _depos {
    final kfhLocations = _commonDataController.kfhLocations;

    return kfhLocations.map((location) {
      return {
        'id': location.cdcID,
        'name': location.distributorName,
        'distance': '${location.distance.toStringAsFixed(1)} kms',
        'address': location.address,
      };
    }).toList();
  }

  int _step = 0;

  @override
  void initState() {
    super.initState();
    final args = Get.arguments;
    onContinue = args?['onContinue'] ?? widget.onContinue ?? (data) {};
    onSkip = args?['onSkip'] ?? widget.onSkip ?? () {};
    onBack = args?['onBack'] ?? widget.onBack;
    _referralFocus.addListener(() => _scrollToField(_referralFocus));
    _konnektorIdFocus.addListener(() => _scrollToField(_konnektorIdFocus));
    _vehicleNumberFocus.addListener(() => _scrollToField(_vehicleNumberFocus));

    // Add listener for vehicle number validation
    _vehicleNumberController.addListener(() {
      setState(() {}); // Refresh UI when vehicle number changes
    });

    // Listen to referral code changes
    _referralController.addListener(() {
      setState(() {
        _hasReferralCode = _referralController.text.trim().isNotEmpty;
      });
    });
  }

  @override
  void dispose() {
    _referralController.dispose();
    _konnektorIdController.dispose();
    _vehicleNumberController.dispose();
    _scrollController.dispose();
    _referralFocus.dispose();
    _konnektorIdFocus.dispose();
    _vehicleNumberFocus.dispose();
    super.dispose();
  }

  void _scrollToField(FocusNode focusNode) {
    if (focusNode.hasFocus) {
      Future.delayed(const Duration(milliseconds: 300), () {
        final context = focusNode.context;
        if (context != null && mounted) {
          Scrollable.ensureVisible(context, duration: const Duration(milliseconds: 300), curve: Curves.easeIn);
        }
      });
    }
  }

  void _nextStep() {
    setState(() => _step++);
  }

  void _prevStep() {
    if (_step > 0) setState(() => _step--);
  }

  bool _canContinueFromVehicleStep() {
    if (_vehicle == null) return false;

    // Company EV Scooter doesn't need vehicle number
    if (_vehicle == 'Company EV Scooter') return true;

    // All other vehicles need valid vehicle number
    return _isValidVehicleNumber(_vehicleNumberController.text.trim());
  }

  /// Validate vehicle number format
  bool _isValidVehicleNumber(String vehicleNumber) {
    if (vehicleNumber.isEmpty) return false;

    // Basic vehicle number validation (Indian format)
    // Format: XX00XX0000 or XX-00-XX-0000 or XX 00 XX 0000
    final cleanNumber = vehicleNumber.replaceAll(RegExp(r'[-\s]'), '').toUpperCase();

    // Should be 10 characters: 2 letters + 2 digits + 2 letters + 4 digits
    final vehicleRegex = RegExp(r'^[A-Z]{2}[0-9]{2}[A-Z]{2}[0-9]{4}$');
    return vehicleRegex.hasMatch(cleanNumber);
  }

  /// Show vehicle number validation error
  void _showVehicleNumberError() {
    Get.dialog(
      AlertDialog(
        title: const Text('Invalid Vehicle Number'),
        content: const Text(
          'Please enter a valid vehicle number.\n\nFormat: XX00XX0000\nExample: MH12AB1234',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  Future<void> _navigateToWorkDetailsFlow() async {
    // Store delivery type
    _workDetailsData['deliveryType'] = _deliveryType;

    // Handle different delivery types
    if (_deliveryType == 'Konnector (Fixed salary)') {
      // Show Konnector ID input dialog
      String? konnectorId;

      await showDialog<String>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return KonnectorIdDialog(
            onKonnectorIdEntered: (id) {
              konnectorId = id;
            },
          );
        },
      );

      if (konnectorId != null) {
        _workDetailsData['konnectorId'] = konnectorId;
        // Move to next step after Konnector ID entry
        if (mounted) {
          setState(() => _step++);
        }
      }
      // If dialog was cancelled, stay on current step
    } else if (_deliveryType == 'Rider (Order wise salary)') {
      // For Rider, directly move to next step
      setState(() => _step++);
    } else {
      // For any other type, directly move to next step
      setState(() => _step++);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              controller: _scrollController,
              padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8),
              child: _buildStepContent(),
            ),
          ),
          // Responsive bottom bar with height constraints for small screens
          SafeArea(
            child: Container(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.12, // Max 12% of screen height
                minHeight: 70, // Minimum height for usability
              ),
              padding: EdgeInsets.only(
                left: 16,
                right: 16,
                top: 8,
                bottom: MediaQuery.of(context).viewInsets.bottom > 0 ? 8 : 16,
              ),
              decoration: const BoxDecoration(
                color: Colors.white,
                boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 4, offset: Offset(0, -2))],
              ),
              child: _buildBottomBar(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepContent() {
    if (_step == 0) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Referral Code', style: AppTextTheme.cardTitle),
          const SizedBox(height: 4),
          Text('Do you have any referral code, please enter it to earn referral amount.',
              style: AppTextTheme.cardSubtitle),
          const SizedBox(height: 16),
          CommonTextField(
            controller: _referralController,
            focusNode: _referralFocus,
            hint: 'Enter Referral Code (Optional)',
          ),
        ],
      );
    } else if (_step == 1) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Choose the preferable delivery', style: TextStyle(fontWeight: FontWeight.bold)),
          const SizedBox(height: 4),
          const Text('Your selection will be help us provide the best work', style: TextStyle(color: Colors.grey)),
          const SizedBox(height: 16),
          ..._deliveryTypeOptions.map((type) => _buildDeliveryTypeOption(type)),
        ],
      );
    } else if (_step == 2) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Choose preferable shift', style: TextStyle(fontSize: 20, fontWeight: FontWeight.w600)),
          const SizedBox(height: 8),
          const Text('Your selection will be your working timings', style: TextStyle(color: Colors.grey)),
          const SizedBox(height: 24),
          ..._shifts.map((shift) => _buildShiftOption(shift)),
        ],
      );
    } else if (_step == 3) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Choose Your Vehicle', style: TextStyle(fontSize: 20, fontWeight: FontWeight.w600)),
          const SizedBox(height: 8),
          const Text('Select the vehicle you own and feel comfortable using for delivering orders.',
              style: TextStyle(color: Colors.grey)),
          const SizedBox(height: 24),
          ..._vehicleOptions.map((vehicle) => _buildVehicleOptionWithInput(vehicle)),
        ],
      );
    } else if (_step == 4) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Choose preferable week-off', style: TextStyle(fontSize: 20, fontWeight: FontWeight.w600)),
          const SizedBox(height: 8),
          const Text('Your selection will be your day off for you', style: TextStyle(color: Colors.grey)),
          const SizedBox(height: 24),
          ..._weekOffs.map((weekOff) => _buildRadio(weekOff, _weekOff, (val) => setState(() => _weekOff = val))),
        ],
      );
    } else if (_step == 5) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Select the depo area you want to work',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.w600)),
          const SizedBox(height: 8),
          const Text('Your selection should near to your home comfortable to work',
              style: TextStyle(color: Colors.grey)),
          const SizedBox(height: 24),
          ..._depos.map((depot) => _buildDepotOption(depot)),
        ],
      );
    } else {
      return const SizedBox.shrink();
    }
  }

  Widget _buildBottomBar() {
    // Get screen height to determine button size
    final screenHeight = MediaQuery.of(context).size.height;
    final isSmallScreen = screenHeight < 700; // Adjust threshold as needed
    final buttonHeight = isSmallScreen ? 48.0 : 56.0; // Smaller buttons for small screens

    List<Widget> actions = [];
    if (onBack != null) {
      actions.add(
        Expanded(
          child: SizedBox(
            height: buttonHeight,
            child: OutlinedButton(
              onPressed: _step > 0 ? _prevStep : onBack,
              style: OutlinedButton.styleFrom(
                side: const BorderSide(color: AppColors.green),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(32)),
              ),
              child: const Text('Back', style: TextStyle(color: AppColors.green)),
            ),
          ),
        ),
      );
      actions.add(const SizedBox(width: 12));
    }
    if (_step == 0) {
      // Single dynamic button for referral code step
      actions.add(
        Expanded(
          child: CommonElevatedButton(
            height: buttonHeight,
            onPressed: () {
              if (_hasReferralCode) {
                // Store referral code and continue to next step
                _workDetailsData['referBy'] = _referralController.text.trim();
                _nextStep();
              } else {
                // Skip referral code and continue to next step
                _workDetailsData['referBy'] = '';
                _nextStep();
              }
            },
            child: Text(
              _hasReferralCode ? 'Continue' : 'Skip & Continue',
              style: const TextStyle(fontSize: 18, color: Colors.white),
            ),
          ),
        ),
      );
    } else if (_step == 1) {
      actions.add(
        Expanded(
          child: CommonElevatedButton(
            height: buttonHeight,
            onPressed: _deliveryType != null
                ? () {
                    _navigateToWorkDetailsFlow();
                  }
                : null,
            child: const Text('Continue', style: TextStyle(fontSize: 18, color: Colors.white)),
          ),
        ),
      );
    } else if (_step == 2) {
      actions.add(
        Expanded(
          child: CommonElevatedButton(
            height: buttonHeight,
            onPressed: _shift != null
                ? () {
                    // Find the shift ID from the selected shift title
                    final selectedShift = _shifts.firstWhere(
                      (shift) => shift['title'] == _shift,
                      orElse: () => <String, Object>{'id': 1}, // Default fallback
                    );
                    _workDetailsData['shiftID'] = selectedShift['id'];
                    setState(() => _step++);
                  }
                : null,
            child: const Text('Continue', style: TextStyle(fontSize: 18, color: Colors.white)),
          ),
        ),
      );
    } else if (_step == 3) {
      actions.add(
        Expanded(
          child: CommonElevatedButton(
            height: buttonHeight,
            onPressed: _canContinueFromVehicleStep()
                ? () {
                    // Find the vehicle ID from the selected vehicle name
                    final selectedVehicle = _vehicleOptions.firstWhere(
                      (vehicle) => vehicle['name'] == _vehicle,
                      orElse: () => <String, Object>{'id': 1}, // Default fallback
                    );
                    _workDetailsData['vehicleType'] = selectedVehicle['id'];
                    _workDetailsData['subSlotName'] = _vehicleNumberController.text;
                    setState(() => _step++);
                  }
                : _vehicle != null && _vehicle != 'Company EV Scooter'
                    ? () {
                        // Show validation error for invalid vehicle number
                        _showVehicleNumberError();
                      }
                    : null,
            child: const Text('Continue', style: TextStyle(fontSize: 18, color: Colors.white)),
          ),
        ),
      );
    } else if (_step == 4) {
      actions.add(
        Expanded(
          child: CommonElevatedButton(
            height: buttonHeight,
            onPressed: _weekOff != null
                ? () {
                    _workDetailsData['weekoffDays'] = _weekOff;
                    setState(() => _step++);
                  }
                : null,
            child: const Text('Continue', style: TextStyle(fontSize: 18, color: Colors.white)),
          ),
        ),
      );
    } else if (_step == 5) {
      actions.add(
        Expanded(
          child: CommonElevatedButton(
            height: buttonHeight,
            onPressed: _depot != null
                ? () {
                    // Find the depot ID from the selected depot name
                    final selectedDepot = _depos.firstWhere(
                      (depot) => depot['name'] == _depot,
                      orElse: () => <String, Object>{'id': 1}, // Default fallback
                    );
                    _workDetailsData['dcid'] = selectedDepot['id'];

                    // Add default slotName if not set
                    if (!_workDetailsData.containsKey('slotName')) {
                      _workDetailsData['slotName'] = 1; // Default slot
                    }

                    debugPrint('💼 Work Details Data being sent: $_workDetailsData');
                    onContinue(_workDetailsData);
                  }
                : null,
            child: const Text('Submit', style: TextStyle(fontSize: 18, color: Colors.white)),
          ),
        ),
      );
    }
    return Row(children: actions);
  }

  Widget _buildDeliveryTypeOption(Map<String, String> deliveryType) {
    final String title = deliveryType['title']!;
    final String subtitle = deliveryType['subtitle']!;
    final bool isSelected = _deliveryType == title;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => setState(() => _deliveryType = title),
        borderRadius: BorderRadius.circular(16),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: isSelected ? AppColors.green : Colors.grey.shade300,
              width: isSelected ? 2 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected ? AppColors.green : Colors.grey.shade400,
                    width: 2,
                  ),
                ),
                child: isSelected
                    ? Center(
                        child: Container(
                          width: 12,
                          height: 12,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: AppColors.green,
                          ),
                        ),
                      )
                    : null,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                      ),
                    ),
                    if (subtitle.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRadio(String value, String? groupValue, ValueChanged<String> onChanged) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => onChanged(value),
        borderRadius: BorderRadius.circular(16),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: groupValue == value ? AppColors.green : Colors.grey.shade300,
              width: groupValue == value ? 2 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: groupValue == value ? AppColors.green : Colors.grey.shade400,
                    width: 2,
                  ),
                ),
                child: groupValue == value
                    ? Center(
                        child: Container(
                          width: 12,
                          height: 12,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: AppColors.green,
                          ),
                        ),
                      )
                    : null,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  value,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildShiftOption(Map<String, dynamic> shift) {
    final isSelected = _shift == shift['title'];
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          setState(() {
            _shift = shift['title'];
          });
        },
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: isSelected ? AppColors.green : Colors.grey.shade300,
              width: isSelected ? 2 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected ? AppColors.green : Colors.grey.shade400,
                    width: 2,
                  ),
                ),
                child: isSelected
                    ? Center(
                        child: Container(
                          width: 12,
                          height: 12,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: AppColors.green,
                          ),
                        ),
                      )
                    : null,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      shift['title'],
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      shift['subtitle'],
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Text('💰', style: TextStyle(fontSize: 16)),
                        const SizedBox(width: 8),
                        Text(
                          shift['earnings'],
                          style: const TextStyle(
                            fontSize: 14,
                            color: AppColors.green,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: shift['color'].withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Center(
                  child: Text(
                    shift['icon'],
                    style: const TextStyle(fontSize: 24),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVehicleOptionWithInput(Map<String, dynamic> vehicle) {
    final isSelected = _vehicle == vehicle['name'];
    final needsVehicleNumber = vehicle['name'] != 'Company EV Scooter';

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        children: [
          // Vehicle option
          InkWell(
            onTap: () {
              setState(() {
                _vehicle = vehicle['name'];

                // Clear vehicle number if Company EV Scooter is selected
                if (vehicle['name'] == 'Company EV Scooter') {
                  _vehicleNumberController.clear();
                }
              });
            },
            borderRadius: BorderRadius.circular(16),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: isSelected ? AppColors.green : Colors.grey.shade300,
                  width: isSelected ? 2 : 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: isSelected ? AppColors.green : Colors.grey.shade400,
                        width: 2,
                      ),
                    ),
                    child: isSelected
                        ? Center(
                            child: Container(
                              width: 12,
                              height: 12,
                              decoration: const BoxDecoration(
                                shape: BoxShape.circle,
                                color: AppColors.green,
                              ),
                            ),
                          )
                        : null,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      vehicle['name'],
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: vehicle['color'].withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Center(
                      child: Text(
                        vehicle['icon'],
                        style: const TextStyle(fontSize: 24),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Vehicle number input (appears directly below selected vehicle)
          if (isSelected && needsVehicleNumber) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: AppColors.green, width: 2),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: CommonTextField(
                      controller: _vehicleNumberController,
                      focusNode: _vehicleNumberFocus,
                      hint: 'MH67-9898-6837-48',
                      keyboardType: TextInputType.text,
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      _vehicleNumberController.clear();
                      setState(() {}); // Update UI state when clearing
                    },
                    icon: const Icon(Icons.close, color: Colors.red, size: 20),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDepotOption(Map<String, dynamic> depot) {
    final isSelected = _depot == depot['name'];
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          setState(() {
            _depot = depot['name'];
          });
        },
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: isSelected ? AppColors.green : Colors.grey.shade300,
              width: isSelected ? 2 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected ? AppColors.green : Colors.grey.shade400,
                    width: 2,
                  ),
                ),
                child: isSelected
                    ? Center(
                        child: Container(
                          width: 12,
                          height: 12,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: AppColors.green,
                          ),
                        ),
                      )
                    : null,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      depot['name'],
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Row(
                      children: [
                        Text('💰', style: TextStyle(fontSize: 16)),
                        SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Upto ₹10,000 weekly earnings',
                            style: TextStyle(
                              fontSize: 14,
                              color: AppColors.green,
                              fontWeight: FontWeight.w500,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Row(
                children: [
                  const Icon(Icons.navigation, color: Colors.grey, size: 16),
                  const SizedBox(width: 4),
                  Text(
                    depot['distance'],
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
