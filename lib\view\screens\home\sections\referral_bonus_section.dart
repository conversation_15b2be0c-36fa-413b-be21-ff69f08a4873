import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

class ReferralBonusSection extends StatelessWidget {
  const ReferralBonusSection({super.key});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    // Responsive sizing
    final horizontalPadding = screenWidth * 0.04; // 4% of screen width
    final containerMargin = screenWidth * 0.04; // 4% of screen width
    final containerPadding = screenWidth * 0.04; // 4% of screen width
    final containerRadius = screenWidth * 0.04; // 4% of screen width
    final cardSpacing = screenWidth * 0.02; // 2% of screen width
    final cardHeight = screenHeight * 0.14; // 14% of screen height
    final cardWidth = screenWidth * 0.22; // 22% of screen width

    // Sample data - you can replace this with dynamic data
    final List<Map<String, dynamic>> bonusItems = [
      {
        'label': 'Order 5',
        'amount': '₹1,000',
        'active': true,
      },
      {
        'label': 'Order 10',
        'amount': '₹2,500',
        'active': true,
      },
      {
        'label': 'Order 50',
        'amount': '₹5,000',
        'active': false,
      },
      {
        'label': 'Order 100',
        'amount': '₹7,500',
        'active': false,
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header outside the container
        Padding(
          padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
          child: Row(
            children: [
              Text(
                'Joining bonus',
                style: AppTextTheme.cardTitle,
              ),
              const Spacer(),
            ],
          ),
        ),

        SizedBox(height: screenHeight * 0.015),

        // Container with scrollable bonus cards
        Container(
          width: screenWidth - (containerMargin * 2),
          margin: EdgeInsets.symmetric(horizontal: containerMargin),
          padding: EdgeInsets.all(containerPadding),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(containerRadius),
            boxShadow: [
              BoxShadow(
                color: AppColors.shadowLight,
                blurRadius: screenWidth * 0.02,
                offset: Offset(0, screenHeight * 0.002),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Scrollable bonus cards list
              SizedBox(
                height: cardHeight,
                child: ListView.separated(
                  scrollDirection: Axis.horizontal,
                  itemCount: bonusItems.length,
                  separatorBuilder: (context, index) => SizedBox(width: cardSpacing),
                  itemBuilder: (context, index) {
                    final item = bonusItems[index];
                    return SizedBox(
                      width: cardWidth,
                      child: BonusCard(
                        label: item['label'],
                        amount: item['amount'],
                        active: item['active'],
                        cardHeight: cardHeight,
                        cardWidth: cardWidth,
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class BonusCard extends StatelessWidget {
  final String label;
  final String amount;
  final bool active;
  final double cardHeight;
  final double cardWidth;

  const BonusCard({
    required this.label,
    required this.amount,
    required this.active,
    required this.cardHeight,
    required this.cardWidth,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    // Responsive sizing
    final cardPadding = screenWidth * 0.02; // 2% of screen width
    final cardRadius = screenWidth * 0.03; // 3% of screen width
    final iconSize = screenWidth * 0.055; // 5.5% of screen width
    final buttonHeight = screenHeight * 0.03; // 3% of screen height
    final spacing = screenHeight * 0.005; // 0.5% of screen height

    return Container(
      height: cardHeight,
      padding: EdgeInsets.all(cardPadding),
      decoration: BoxDecoration(
        color: active
            ? const Color(0xFFFFF4D9) // Golden yellow background for active cards
            : const Color(0xFFF5F5F5), // Light grey for inactive cards
        borderRadius: BorderRadius.circular(cardRadius),
        border: Border.all(
          color: active ? const Color(0xFFFFC107) : AppColors.borderLight, // Golden border for active
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Label
          Text(
            label,
            style: AppTextTheme.cardCaption.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),

          SizedBox(height: spacing),

          // Icon
          active
              ? SizedBox(
                  width: iconSize,
                  height: iconSize,
                  child: Image.asset(
                    'assets/images/rupee.png',
                    width: iconSize,
                    height: iconSize,
                    fit: BoxFit.contain,
                    // Fallback if image is not available
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: iconSize,
                        height: iconSize,
                        decoration: BoxDecoration(
                          color: const Color(0xFFFFC107), // Golden color
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.currency_rupee,
                          color: Colors.white,
                          size: iconSize * 0.6,
                        ),
                      );
                    },
                  ),
                )
              : Icon(
                  Icons.emoji_events_outlined,
                  color: const Color(0xFFBDBDBD), // Light grey for inactive
                  size: iconSize,
                ),

          SizedBox(height: spacing),

          // Amount
          Text(
            amount,
            style: AppTextTheme.cardCaption.copyWith(
              color: active ? AppColors.green : AppColors.textSecondary,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),

          SizedBox(height: spacing),

          // Button
          SizedBox(
            width: double.infinity,
            height: buttonHeight,
            child: ElevatedButton(
              onPressed: active ? () {} : null,
              style: ElevatedButton.styleFrom(
                backgroundColor:
                    active ? AppColors.green : const Color(0xFFE0E0E0), // Golden for active, grey for inactive
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(buttonHeight / 2),
                ),
                padding: EdgeInsets.zero,
                elevation: 0,
              ),
              child: active
                  ? Text(
                      'Claim',
                      style: AppTextTheme.buttonSmall.copyWith(
                        color: Colors.white,
                      ),
                    )
                  : Icon(
                      Icons.lock_outline,
                      color: const Color(0xFF9E9E9E), // Grey color for lock icon
                      size: iconSize * 0.5,
                    ),
            ),
          ),
        ],
      ),
    );
  }
}
