import 'package:get/get.dart';
import 'package:flutter/foundation.dart';
import '../models/common_data_models.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import '../constants/storage_keys.dart';

class CommonDataController extends GetxController {
  // Observable variables
  final _isLoading = false.obs;
  final _commonData = Rxn<CommonDataResponse>();
  final _errorMessage = ''.obs;

  // Storage service for retrieving stored coordinates
  final SecureStorageService _storage = SecureStorageService.instance;

  // Getters
  bool get isLoading => _isLoading.value;
  CommonDataResponse? get commonData => _commonData.value;
  String get errorMessage => _errorMessage.value;

  // Specific data getters
  List<VehicleType> get vehicleTypes => commonData?.combinedDetails?.vehicleTypeList ?? [];
  List<RiderType> get riderTypes => commonData?.combinedDetails?.riderTypeList ?? [];
  List<BankInfo> get banks => commonData?.combinedDetails?.bankList ?? [];
  List<SlotName> get slotNames => commonData?.combinedDetails?.slotNameList ?? [];
  List<ShiftMaster> get shiftMasters => commonData?.combinedDetails?.shiftMasterList ?? [];
  List<SubShift> get subShifts => commonData?.combinedDetails?.subShiftList ?? [];
  List<KfhLocation> get kfhLocations => commonData?.combinedDetails?.kfhList ?? [];

  @override
  void onInit() {
    super.onInit();
    // Load common data on initialization
    loadCommonData();
  }

  /// Load common data from API
  Future<void> loadCommonData({
    double? latitude,
    double? longitude,
  }) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Get coordinates from storage or use provided/default ones
      double lat, lng;

      if (latitude != null && longitude != null) {
        // Use provided coordinates
        lat = latitude;
        lng = longitude;
        debugPrint('🌍 Using provided coordinates: $lat, $lng');
      } else {
        // Try to get stored coordinates from address filling
        final storedLat = await _storage.read(StorageKeys.latitude);
        final storedLng = await _storage.read(StorageKeys.longitude);

        if (storedLat != null && storedLng != null) {
          lat = double.tryParse(storedLat) ?? 0.0;
          lng = double.tryParse(storedLng) ?? 0.0;
          debugPrint('📍 Using stored coordinates from address: $lat, $lng');
        } else {
          // Fall back to default coordinates
          final coords = CommonDataService.getDefaultCoordinates();
          lat = coords['latitude']!;
          lng = coords['longitude']!;
          debugPrint('🏠 Using default coordinates (Mumbai): $lat, $lng');
        }
      }

      debugPrint('🔄 Loading common data with final coordinates: $lat, $lng');

      final response = await CommonDataService.fetchCommonData(
        latitude: lat,
        longitude: lng,
      );

      if (response != null) {
        _commonData.value = response;
        debugPrint('✅ Common data loaded successfully');
        debugPrint('📊 Vehicle types: ${vehicleTypes.length}');
        debugPrint('📊 Rider types: ${riderTypes.length}');
        debugPrint('📊 Banks: ${banks.length}');
        debugPrint('📊 KFH locations: ${kfhLocations.length}');
      } else {
        _errorMessage.value = 'Failed to load common data';
        debugPrint('❌ Failed to load common data');
      }
    } catch (e) {
      _errorMessage.value = 'Error loading common data: $e';
      debugPrint('❌ Exception in loadCommonData: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Refresh common data
  Future<void> refreshCommonData({
    double? latitude,
    double? longitude,
  }) async {
    await loadCommonData(latitude: latitude, longitude: longitude);
  }

  /// Reload common data when address is updated
  Future<void> reloadWithStoredCoordinates() async {
    debugPrint('🔄 Reloading common data with updated stored coordinates');
    await loadCommonData();
  }

  /// Get vehicle type by ID
  VehicleType? getVehicleTypeById(int id) {
    try {
      return vehicleTypes.firstWhere((vehicle) => vehicle.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get rider type by ID
  RiderType? getRiderTypeById(int id) {
    try {
      return riderTypes.firstWhere((rider) => rider.rid == id);
    } catch (e) {
      return null;
    }
  }

  /// Get bank by ID
  BankInfo? getBankById(int id) {
    try {
      return banks.firstWhere((bank) => bank.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get active banks only
  List<BankInfo> get activeBanks {
    return banks.where((bank) => bank.isActive == 1).toList();
  }

  /// Get shifts by slot ID
  List<ShiftMaster> getShiftsBySlotId(int slotId) {
    return shiftMasters.where((shift) => shift.srDeliverySlotID == slotId).toList();
  }

  /// Get sub shifts by shift ID
  List<SubShift> getSubShiftsByShiftId(int shiftId) {
    return subShifts.where((subShift) => subShift.srShiftID == shiftId).toList();
  }

  /// Check if data is loaded
  bool get hasData => commonData != null;

  /// Check if there's an error
  bool get hasError => errorMessage.isNotEmpty;
}
