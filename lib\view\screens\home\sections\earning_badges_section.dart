import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/index.dart';
import 'package:story_view/story_view.dart';

import 'package:kisankonnect_rider/utils/responsive_utils.dart';
import 'package:kisankonnect_rider/controllers/dashboard_story_controller.dart';
import '../../../widgets/loading/elegant_shimmer.dart';

class EarningBadgesSection extends StatefulWidget {
  const EarningBadgesSection({super.key});

  @override
  State<EarningBadgesSection> createState() => _EarningBadgesSectionState();
}

class _EarningBadgesSectionState extends State<EarningBadgesSection> {
  late final DashboardStoryController _controller;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _controller = Get.find<DashboardStoryController>();
    _listenToController();
  }

  void _listenToController() {
    // Listen to controller loading state for dynamic shimmer
    ever(_controller.isLoading, (bool isLoading) {
      if (mounted) {
        setState(() {
          _isLoading = isLoading;
        });
      }
    });

    // Set initial loading state
    _isLoading = _controller.isLoading.value;
  }

  /// Helper method to detect if URL is a video
  bool _isVideoUrl(String url) {
    final videoExtensions = ['.mp4', '.mov', '.avi', '.mkv', '.webm', '.m4v'];
    final lowerUrl = url.toLowerCase();
    return videoExtensions.any((ext) => lowerUrl.contains(ext));
  }

  void _openStoryViewer(int index) {
    debugPrint('🎯 Opening story viewer for index: $index');
    debugPrint('📊 Total story banners: ${_controller.sortedStoryBanners.length}');

    final sortedBanners = _controller.sortedStoryBanners;
    if (index >= sortedBanners.length) {
      debugPrint('❌ Index $index is out of bounds. Max index: ${sortedBanners.length - 1}');
      return;
    }

    if (sortedBanners.isEmpty) {
      debugPrint('❌ No story banners available');
      return;
    }

    // Create story items for the story_view package
    final List<StoryItem> storyItems = [];
    final StoryController storyController = StoryController();

    for (int i = 0; i < sortedBanners.length; i++) {
      final banner = sortedBanners[i];
      if (banner.thumbnailImageURL.isNotEmpty) {
        final mediaUrl = banner.thumbnailImageURL;
        final isVideo = _isVideoUrl(mediaUrl);
        final title = banner.storyTitle.isNotEmpty ? banner.storyTitle : 'Story ${i + 1}';

        debugPrint('🔗 Creating story item $i: $mediaUrl (isVideo: $isVideo)');

        try {
          if (isVideo) {
            // Add video story item - full screen
            storyItems.add(
              StoryItem.pageVideo(
                mediaUrl,
                controller: storyController,
                caption: Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                duration: const Duration(seconds: 10),
              ),
            );
          } else {
            // Add image story item - full screen with proper fit
            storyItems.add(
              StoryItem.pageImage(
                url: mediaUrl,
                controller: storyController,
                caption: Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                duration: const Duration(seconds: 5),
                imageFit: BoxFit.cover, // Changed from BoxFit.contain to BoxFit.cover for full screen
              ),
            );
          }
        } catch (e) {
          debugPrint('❌ Error creating story item $i: $e');
          // Add a fallback error story item
          storyItems.add(
            StoryItem.text(
              title: 'Error Loading Story',
              backgroundColor: Colors.red.shade400,
              textStyle: const TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
          );
        }
      }
    }

    if (storyItems.isEmpty) {
      debugPrint('❌ No valid story items created');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No stories available')),
      );
      return;
    }

    debugPrint('✅ Opening story viewer with ${storyItems.length} stories, starting at index $index');

    // Navigate to full screen story view
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => StoryViewScreen(
          storyItems: storyItems,
          initialIndex: index,
          onStoryViewed: (viewedIndex) {
            debugPrint('📖 Story viewed at index: $viewedIndex');
            _controller.markStoryAsViewed(viewedIndex);
          },
        ),
        fullscreenDialog: true, // This ensures full screen presentation
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final horizontalPadding = ResponsiveUtils.spacingM(context);
    final separatorWidth = ResponsiveUtils.spacingS(context);
    final totalSeparatorWidth = separatorWidth * 3;
    final totalPaddingWidth = horizontalPadding * 2;
    final screenWidth = ResponsiveUtils.width(context, 100);
    final availableWidth = screenWidth - totalPaddingWidth - totalSeparatorWidth;
    final calculatedBadgeSize = availableWidth / 4;
    // Ensure minimum size of 80px as requested
    final badgeSize = calculatedBadgeSize < 80 ? 80.0 : calculatedBadgeSize;
    final containerHeight = badgeSize + ResponsiveUtils.height(context, 2);
    final borderWidth = ResponsiveUtils.width(context, 0.8);
    final imageSize = badgeSize * 0.85;

    return Obx(() {
      final storyBanners = _controller.sortedStoryBanners;
      final itemCount = _isLoading ? 6 : storyBanners.length;

      return ElegantShimmer(
        enabled: _isLoading,
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 0.0,
            vertical: 8.0,
          ),
          child: SizedBox(
            height: containerHeight,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
              itemCount: itemCount,
              separatorBuilder: (_, __) => SizedBox(width: separatorWidth),
              itemBuilder: (context, i) {
                final isViewed = !_isLoading && i < storyBanners.length ? _controller.isStoryViewed(i) : false;

                debugPrint('🔍 Building badge $i - isViewed: $isViewed');

                return GestureDetector(
                  onTap: _isLoading ? null : () => _openStoryViewer(i),
                  child: Container(
                    width: badgeSize,
                    height: badgeSize,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: _isLoading
                            ? Colors.grey.withValues(alpha: 0.3)
                            : isViewed
                                ? Colors.grey.withValues(alpha: 0.5) // Viewed state - grey border
                                : AppColors.green, // Unviewed state - green border
                        width: borderWidth,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.10),
                          blurRadius: ResponsiveUtils.width(context, 1.5),
                          offset: Offset(0, ResponsiveUtils.height(context, 0.2)),
                        ),
                      ],
                    ),
                    child: Container(
                      width: imageSize,
                      height: imageSize,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: _isLoading ? Colors.grey.shade300 : null,
                        image: _isLoading
                            ? null
                            : i < storyBanners.length
                                ? DecorationImage(
                                    image: NetworkImage(
                                      storyBanners[i].thumbnailImageURL,
                                      headers: const {
                                        'User-Agent': 'Mozilla/5.0 (compatible; Flutter)',
                                        'Accept': 'image/*',
                                      },
                                    ),
                                    fit: BoxFit.cover,
                                    filterQuality: FilterQuality.medium,
                                    onError: (error, stackTrace) {
                                      debugPrint('❌ Error loading image: $error');
                                    },
                                  )
                                : null,
                      ),
                      // Add loading indicator for images
                      child: _isLoading
                          ? null
                          : i < storyBanners.length && storyBanners[i].thumbnailImageURL.isNotEmpty
                              ? null
                              : const Center(
                                  child: Icon(
                                    Icons.image_not_supported,
                                    color: Colors.grey,
                                    size: 24,
                                  ),
                                ),
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      );
    });
  }
}

/// Story View Screen using the story_view package
class StoryViewScreen extends StatefulWidget {
  final List<StoryItem> storyItems;
  final int initialIndex;
  final Function(int) onStoryViewed;

  const StoryViewScreen({
    super.key,
    required this.storyItems,
    required this.initialIndex,
    required this.onStoryViewed,
  });

  @override
  State<StoryViewScreen> createState() => _StoryViewScreenState();
}

class _StoryViewScreenState extends State<StoryViewScreen> {
  late final StoryController _storyController;
  int _currentIndex = 0;
  final bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _storyController = StoryController();

    debugPrint(
        '🎬 StoryViewScreen initialized with ${widget.storyItems.length} stories, starting at index ${widget.initialIndex}');

    // Mark the initial story as viewed
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.onStoryViewed(_currentIndex);
    });
  }

  @override
  void dispose() {
    _storyController.dispose();
    super.dispose();
  }

  void _updateCurrentIndex(int index) {
    // Use addPostFrameCallback to defer setState until after the current build completes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _currentIndex = index;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            StoryView(
              storyItems: widget.storyItems,
              controller: _storyController,
              onComplete: () {
                debugPrint('✅ All stories completed');
                Navigator.of(context).pop();
              },
              onStoryShow: (storyItem, index) {
                debugPrint('📖 Story shown at index: $index');
                // Use the new method to safely update the index
                _updateCurrentIndex(index);
                widget.onStoryViewed(index);
              },
              onVerticalSwipeComplete: (direction) {
                if (direction == Direction.down) {
                  debugPrint('👆 Swiped down - closing story viewer');
                  Navigator.of(context).pop();
                }
              },
              // Story view styling
              indicatorColor: Colors.white.withOpacity(0.3),
              indicatorForegroundColor: AppColors.green,
              indicatorHeight: IndicatorHeight.small,
              repeat: false,
              inline: false,
              progressPosition: ProgressPosition.top,
            ),
            // Close button
            Positioned(
              top: 16,
              right: 16,
              child: GestureDetector(
                onTap: () {
                  Navigator.of(context).pop();
                },
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.5),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ),
            // Story counter
            Positioned(
              top: 16,
              left: 16,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '${_currentIndex + 1} / ${widget.storyItems.length}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
