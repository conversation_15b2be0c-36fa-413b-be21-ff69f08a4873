import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:kisankonnect_rider/config/flavor_config.dart';

/// Centralized API Helper for all KisanKonnect API calls
/// This class provides generic methods for all API endpoints
class ApiHelper {
  static ApiHelper? _instance;
  static ApiHelper get instance => _instance ??= ApiHelper._();

  late Dio _dio;
  String? _deviceId;

  ApiHelper._() {
    _initializeDio();
    _getDeviceId();
  }

  void _initializeDio() {
    _dio = Dio(BaseOptions(
      baseUrl: _getBaseUrl(),
      connectTimeout: Duration(seconds: Environment.enableLogging ? 30 : 15),
      receiveTimeout: Duration(seconds: Environment.enableLogging ? 30 : 15),
      headers: {
        'Content-Type': 'application/json',
        'Accept': '*/*',
        'User-Agent': 'KisanKonnect-Rider-Flutter/${Environment.apiVersion}',
      },
    ));

    // Add interceptors for logging in development
    if (Environment.enableLogging) {
      _dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        requestHeader: true,
        responseHeader: false,
        error: true,
        logPrint: (obj) => debugPrint('🌐 API: $obj'),
      ));
    }

    // Add error handling interceptor
    _dio.interceptors.add(InterceptorsWrapper(
      onError: (error, handler) {
        debugPrint('🚨 API Error: ${error.message}');
        handler.next(error);
      },
    ));
  }

  String _getBaseUrl() {
    // Use flavor-based base URL from Environment configuration
    return Environment.apiBaseUrl;
  }

  Future<void> _getDeviceId() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        _deviceId = androidInfo.id;
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        _deviceId = iosInfo.identifierForVendor;
      } else {
        _deviceId = 'unknown';
      }
    } catch (e) {
      _deviceId = 'unknown';
      debugPrint('Error getting device ID: $e');
    }
  }

  // ==================== GENERIC API METHODS ====================

  /// Generic GET request method
  Future<ApiResponse<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      debugPrint('🔄 GET Request: $path');
      if (queryParameters != null) {
        debugPrint('🔄 Query Params: $queryParameters');
      }

      final response = await _dio.get(
        path,
        queryParameters: queryParameters,
        options: options,
      );

      debugPrint('✅ GET Response: ${response.statusCode}');

      if (response.statusCode == 200) {
        if (fromJson != null) {
          final data = fromJson(response.data);
          return ApiResponse.success(data);
        } else {
          return ApiResponse.success(response.data as T);
        }
      } else {
        return ApiResponse.error('Request failed: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      debugPrint('🚨 GET Error: ${e.message}');
      return ApiResponse.error(_handleDioError(e));
    } catch (e) {
      debugPrint('🚨 Unexpected GET Error: $e');
      return ApiResponse.error('An unexpected error occurred');
    }
  }

  /// Generic POST request method
  Future<ApiResponse<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      debugPrint('🔄 POST Request: $path');
      if (data != null) {
        debugPrint('🔄 POST Data: $data');
      }

      final response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );

      debugPrint('✅ POST Response: ${response.statusCode}');

      if (response.statusCode == 200) {
        if (fromJson != null) {
          final responseData = fromJson(response.data);
          return ApiResponse.success(responseData);
        } else {
          return ApiResponse.success(response.data as T);
        }
      } else {
        return ApiResponse.error('Request failed: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      debugPrint('🚨 POST Error: ${e.message}');
      return ApiResponse.error(_handleDioError(e));
    } catch (e) {
      debugPrint('🚨 Unexpected POST Error: $e');
      return ApiResponse.error('An unexpected error occurred');
    }
  }

  /// Generic PUT request method
  Future<ApiResponse<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      debugPrint('🔄 PUT Request: $path');

      final response = await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );

      debugPrint('✅ PUT Response: ${response.statusCode}');

      if (response.statusCode == 200) {
        if (fromJson != null) {
          final responseData = fromJson(response.data);
          return ApiResponse.success(responseData);
        } else {
          return ApiResponse.success(response.data as T);
        }
      } else {
        return ApiResponse.error('Request failed: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      debugPrint('🚨 PUT Error: ${e.message}');
      return ApiResponse.error(_handleDioError(e));
    } catch (e) {
      debugPrint('🚨 Unexpected PUT Error: $e');
      return ApiResponse.error('An unexpected error occurred');
    }
  }

  /// Generic DELETE request method
  Future<ApiResponse<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      debugPrint('🔄 DELETE Request: $path');

      final response = await _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );

      debugPrint('✅ DELETE Response: ${response.statusCode}');

      if (response.statusCode == 200) {
        if (fromJson != null) {
          final responseData = fromJson(response.data);
          return ApiResponse.success(responseData);
        } else {
          return ApiResponse.success(response.data as T);
        }
      } else {
        return ApiResponse.error('Request failed: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      debugPrint('🚨 DELETE Error: ${e.message}');
      return ApiResponse.error(_handleDioError(e));
    } catch (e) {
      debugPrint('🚨 Unexpected DELETE Error: $e');
      return ApiResponse.error('An unexpected error occurred');
    }
  }

  /// Upload file with form data
  Future<ApiResponse<T>> uploadFile<T>(
    String path, {
    required FormData formData,
    Map<String, dynamic>? queryParameters,
    Options? options,
    T Function(dynamic)? fromJson,
    ProgressCallback? onSendProgress,
  }) async {
    try {
      debugPrint('📤 Upload Request: $path');

      // Extract query parameters from options if provided
      Map<String, dynamic>? finalQueryParams = queryParameters;
      if (options?.extra?['queryParameters'] != null) {
        finalQueryParams = options!.extra!['queryParameters'] as Map<String, dynamic>;
      }

      if (finalQueryParams != null) {
        debugPrint('📤 Query Parameters: $finalQueryParams');
      }

      final response = await _dio.post(
        path,
        data: formData,
        queryParameters: finalQueryParams,
        options: options,
        onSendProgress: onSendProgress,
      );

      debugPrint('✅ Upload Response: ${response.statusCode}');

      if (response.statusCode == 200) {
        if (fromJson != null) {
          final responseData = fromJson(response.data);
          return ApiResponse.success(responseData);
        } else {
          return ApiResponse.success(response.data as T);
        }
      } else {
        return ApiResponse.error('Upload failed: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      debugPrint('🚨 Upload Error: ${e.message}');
      return ApiResponse.error(_handleDioError(e));
    } catch (e) {
      debugPrint('🚨 Unexpected Upload Error: $e');
      return ApiResponse.error('An unexpected error occurred');
    }
  }

  String _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        return 'Connection timeout. Please check your internet connection.';
      case DioExceptionType.sendTimeout:
        return 'Request timeout. Please try again.';
      case DioExceptionType.receiveTimeout:
        return 'Server response timeout. Please try again.';
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        if (statusCode == 400) {
          return 'Invalid request. Please check your input.';
        } else if (statusCode == 401) {
          return 'Unauthorized. Please login again.';
        } else if (statusCode == 404) {
          return 'Service not found. Please try again later.';
        } else if (statusCode == 500) {
          return 'Server error. Please try again later.';
        }
        return 'Server error ($statusCode). Please try again.';
      case DioExceptionType.cancel:
        return 'Request was cancelled.';
      case DioExceptionType.connectionError:
        return 'No internet connection. Please check your network.';
      default:
        return 'Network error. Please try again.';
    }
  }

  /// Get current environment info for debugging
  Map<String, dynamic> getEnvironmentInfo() {
    return {
      'baseUrl': _getBaseUrl(),
      'flavor': FlavorConfig.name,
      'deviceId': _deviceId,
      'loggingEnabled': Environment.enableLogging,
    };
  }

  /// Get device ID
  String? get deviceId => _deviceId;
}

/// Generic API Response wrapper
class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? error;

  ApiResponse.success(this.data)
      : success = true,
        error = null;
  ApiResponse.error(this.error)
      : success = false,
        data = null;

  bool get isSuccess => success;
  bool get isError => !success;
}
