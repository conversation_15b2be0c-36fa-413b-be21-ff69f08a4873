import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/utils/responsive_utils.dart';

class OrderPickedUpScreen extends StatelessWidget {
  final String timeSlot;
  final List<DeliveryOrder> orders;

  const OrderPickedUpScreen({
    super.key,
    this.timeSlot = '06 pm - 08 pm',
    this.orders = const [],
  });

  @override
  Widget build(BuildContext context) {
    // Default orders if none provided
    final orderList = orders.isNotEmpty ? orders : _getDefaultOrders();

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(80),
        child: Container(
          color: Colors.white,
          child: SafeArea(
            child: Container(
              padding: EdgeInsets.all(16),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Shopping bag icon in rounded container
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Color(0xFFE8F5E9), // Light green background
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Icon(
                        Icons.shopping_bag_outlined,
                        color: AppColors.green,
                        size: 20,
                      ),
                    ),
                  ),
                  SizedBox(width: 12),
                  // Title
                  Expanded(
                    child: Text(
                      'Order picked up',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                        height: 1.25,
                      ),
                    ),
                  ),
                  // Time slot badge
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      timeSlot,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      body: Column(
        children: [
          // Orders list
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.all(16),
              itemCount: orderList.length,
              itemBuilder: (context, index) {
                final order = orderList[index];
                return Container(
                  margin: EdgeInsets.only(bottom: 16),
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 8,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Order number circle
                      Container(
                        width: 28,
                        height: 28,
                        decoration: BoxDecoration(
                          color: Colors.black,
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Center(
                          child: Text(
                            (index + 1).toString().padLeft(2, '0'),
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 12),

                      // Order details
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Order ID section with background
                            Container(
                              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                              decoration: BoxDecoration(
                                color: Color(0xFFF5F5F5), // Light grey background
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      'Order Id - #${order.orderId}',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.black,
                                      ),
                                    ),
                                  ),
                                  // Action buttons
                                  Row(
                                    children: [
                                      // Green button
                                      Container(
                                        width: 28,
                                        height: 28,
                                        decoration: BoxDecoration(
                                          color: AppColors.green,
                                          borderRadius: BorderRadius.circular(6),
                                        ),
                                        child: IconButton(
                                          onPressed: () {
                                            // Handle green action
                                          },
                                          padding: EdgeInsets.zero,
                                          icon: Icon(
                                            Icons.check,
                                            color: Colors.white,
                                            size: 14,
                                          ),
                                        ),
                                      ),
                                      SizedBox(width: 6),
                                      // Blue button
                                      Container(
                                        width: 28,
                                        height: 28,
                                        decoration: BoxDecoration(
                                          color: Colors.blue,
                                          borderRadius: BorderRadius.circular(6),
                                        ),
                                        child: IconButton(
                                          onPressed: () {
                                            // Handle blue action
                                          },
                                          padding: EdgeInsets.zero,
                                          icon: Icon(
                                            Icons.navigation_outlined,
                                            color: Colors.white,
                                            size: 14,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(height: 12),

                            // Customer address
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  margin: EdgeInsets.only(top: 2),
                                  child: Icon(
                                    Icons.location_on_outlined,
                                    size: 14,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                                SizedBox(width: 6),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Customer address',
                                        style: TextStyle(
                                          fontSize: 11,
                                          fontWeight: FontWeight.w400,
                                          color: Colors.grey.shade600,
                                        ),
                                      ),
                                      SizedBox(height: 2),
                                      Text(
                                        order.address,
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w400,
                                          color: Colors.black,
                                          height: 1.3,
                                        ),
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 10),

                            // Distance & Landmark
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  margin: EdgeInsets.only(top: 2),
                                  child: Icon(
                                    Icons.route_outlined,
                                    size: 14,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                                SizedBox(width: 6),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Distance & Landmark',
                                        style: TextStyle(
                                          fontSize: 11,
                                          fontWeight: FontWeight.w400,
                                          color: Colors.grey.shade600,
                                        ),
                                      ),
                                      SizedBox(height: 2),
                                      Text(
                                        'Distance - ${order.distance}',
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w400,
                                          color: Colors.black,
                                        ),
                                      ),
                                      Text(
                                        order.landmark,
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w400,
                                          color: Colors.black,
                                          height: 1.3,
                                        ),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),

          // Bottom buttons
          Container(
            padding: EdgeInsets.all(16),
            child: Row(
              children: [
                // Self assign button
                Expanded(
                  child: SizedBox(
                    height: 48,
                    child: OutlinedButton(
                      onPressed: () {
                        // Handle self assign
                        Get.snackbar(
                          'Self Assign',
                          'Orders self assigned',
                          backgroundColor: Colors.grey.shade100,
                          colorText: Colors.black,
                        );
                      },
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: Colors.grey.shade300),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(24),
                        ),
                      ),
                      child: Text(
                        'Self assign',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 12),
                // Start delivery button
                Expanded(
                  child: SizedBox(
                    height: 48,
                    child: ElevatedButton(
                      onPressed: () {
                        // Handle start delivery
                        Get.snackbar(
                          'Start Delivery',
                          'Starting delivery process...',
                          backgroundColor: AppColors.green,
                          colorText: Colors.white,
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.green,
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(24),
                        ),
                      ),
                      child: Text(
                        'Start delivery',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<DeliveryOrder> _getDefaultOrders() {
    return [
      DeliveryOrder(
        orderId: 'E76592',
        address: 'NLT-B, room no 36, Sector 20, Opposite to Dreamland society,Nerul (w), Navi Mumbai...',
        distance: '2 km',
        landmark: 'Opposite to Dreamland near...',
      ),
      DeliveryOrder(
        orderId: 'E76593',
        address: 'NLT-B, room no 36, Sector 20, Opposite to Dreamland society,Nerul (w), Navi Mumbai...',
        distance: '3.5 km',
        landmark: 'Opposite to Dreamland near...',
      ),
      DeliveryOrder(
        orderId: 'E76594',
        address: 'NLT-B, room no 36, Sector 20, Opposite to Dreamland society,Nerul (w), Navi Mumbai...',
        distance: '4 km',
        landmark: 'Opposite to Dreamland near...',
      ),
      DeliveryOrder(
        orderId: 'E76595',
        address: 'NLT-B, room no 36, Sector 20, Opposite to Dreamland society,Nerul (w), Navi Mumbai...',
        distance: '5 km',
        landmark: 'Opposite to Dreamland near...',
      ),
      DeliveryOrder(
        orderId: 'E76597',
        address: 'NLT-B, room no 36, Sector 20, Opposite to Dreamland society,Nerul (w), Navi Mumbai...',
        distance: '7 km',
        landmark: 'Opposite to Dreamland near...',
      ),
      DeliveryOrder(
        orderId: 'E76597',
        address: 'NLT-B, room no 36, Sector 20, Opposite to Dreamland society,Nerul (w), Navi Mumbai...',
        distance: '10 km',
        landmark: 'Opposite to Dreamland near...',
      ),
    ];
  }
}

class DeliveryOrder {
  final String orderId;
  final String address;
  final String distance;
  final String landmark;

  const DeliveryOrder({
    required this.orderId,
    required this.address,
    required this.distance,
    required this.landmark,
  });
}
