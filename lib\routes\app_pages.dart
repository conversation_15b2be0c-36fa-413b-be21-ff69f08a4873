import 'package:get/get.dart';
import 'package:kisankonnect_rider/view/screens/splash/splash_screen.dart';
import 'package:kisankonnect_rider/view/screens/login/login.dart';
import 'package:kisankonnect_rider/view/screens/login/enter_number_screen.dart';
import 'package:kisankonnect_rider/view/screens/create_profile/stepper/create_profile_screen.dart';
import 'package:kisankonnect_rider/view/screens/delivery_partner/delivery_partner_welcome_screen.dart';
import 'package:kisankonnect_rider/view/screens/home/<USER>';
import 'package:kisankonnect_rider/view/screens/create_profile/stepper/profile_summary_screen.dart';
import 'package:kisankonnect_rider/view/screens/wallet/wallet_screen.dart';
import 'package:kisankonnect_rider/view/screens/all_earnings/all_earnings_screen.dart';
import 'package:kisankonnect_rider/screens/flavor_info_screen.dart';
import 'package:kisankonnect_rider/bindings/app_bindings.dart';

/// App Routes - Registration Focus Only
class AppRoutes {
  // Core App Flow
  static const splash = '/splash';
  static const login = '/login';
  static const enterNumber = '/enter-number';

  // Registration Flow
  static const createProfile = '/create-profile';
  static const deliveryPartnerWelcome = '/delivery-partner-welcome';

  // Post Registration
  static const profileSummary = '/profile-summary';
  static const dashboard = '/dashboard';
  static const wallet = '/wallet';
  static const allEarnings = '/all-earnings';

  // Development
  static const flavorInfo = '/flavor-info';
}

class AppPages {
  static final pages = [
    // Core App Flow
    GetPage(name: AppRoutes.splash, page: () => const SplashScreen()),
    GetPage(name: AppRoutes.enterNumber, page: () => const EnterNumberScreen()),
    GetPage(name: AppRoutes.login, page: () => const LoginScreen()),

    // Registration Flow
    GetPage(
      name: AppRoutes.createProfile,
      page: () => const CreateProfileScreen(),
      binding: ProfileBindings(),
    ),
    GetPage(
      name: AppRoutes.deliveryPartnerWelcome,
      page: () => const DeliveryPartnerWelcomeScreen(),
    ),

    // Post Registration
    GetPage(
      name: AppRoutes.profileSummary,
      page: () => const ProfileSummaryScreen(),
    ),
    GetPage(
      name: AppRoutes.dashboard,
      page: () => const DashboardScreen(),
      binding: AppBindings(),
    ),
    GetPage(name: AppRoutes.wallet, page: () => const WalletScreen()),
    GetPage(name: AppRoutes.allEarnings, page: () => const AllEarningsScreen()),

    // Development
    GetPage(name: AppRoutes.flavorInfo, page: () => const FlavorInfoScreen()),
  ];
}
