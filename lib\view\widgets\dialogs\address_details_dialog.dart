import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

/// Address Details Dialog for saving complete address information
class AddressDetailsDialog extends StatefulWidget {
  final String selectedLocation;
  final String selectedAddress;
  final Function(Map<String, String>)? onAddressSaved;

  const AddressDetailsDialog({
    super.key,
    required this.selectedLocation,
    required this.selectedAddress,
    this.onAddressSaved,
  });

  @override
  State<AddressDetailsDialog> createState() => _AddressDetailsDialogState();

  /// Show the address details dialog
  static Future<Map<String, String>?> show({
    required BuildContext context,
    required String selectedLocation,
    required String selectedAddress,
    Function(Map<String, String>)? onAddressSaved,
  }) async {
    return await showDialog<Map<String, String>>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AddressDetailsDialog(
          selectedLocation: selectedLocation,
          selectedAddress: selectedAddress,
          onAddressSaved: onAddressSaved,
        );
      },
    );
  }
}

class _AddressDetailsDialogState extends State<AddressDetailsDialog> {
  final _formKey = GlobalKey<FormState>();
  final _societyController = TextEditingController();
  final _flatController = TextEditingController();
  final _landmarkController = TextEditingController();

  String _selectedArea = '';

  // Mock nearby areas
  final List<String> _nearbyAreas = [
    'Koparkhairne',
    'Nerul',
    'Vashi',
    'Sanpada',
    'Juinagar',
    'Belapur',
  ];

  @override
  void initState() {
    super.initState();
    // Set default area based on location
    if (widget.selectedLocation.toLowerCase().contains('nerul')) {
      _selectedArea = 'Nerul';
    } else if (widget.selectedLocation.toLowerCase().contains('kopar')) {
      _selectedArea = 'Koparkhairne';
    } else {
      _selectedArea = _nearbyAreas.first;
    }
  }

  @override
  void dispose() {
    _societyController.dispose();
    _flatController.dispose();
    _landmarkController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        constraints: const BoxConstraints(maxHeight: 600),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with location info
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.location_on,
                        color: AppColors.green,
                        size: 24,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          widget.selectedLocation,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text(
                          'Change',
                          style: TextStyle(
                            color: AppColors.green,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    widget.selectedAddress,
                    style: const TextStyle(
                      color: Colors.grey,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),

            // Form Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Society Name
                      const Text(
                        'Society name*',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      _buildTextField(
                        controller: _societyController,
                        hintText: 'Sai Paradise',
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter society name';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 20),

                      // House/Flat/Wing/Block
                      const Text(
                        'House/Flat/Wing/Block*',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      _buildTextField(
                        controller: _flatController,
                        hintText: 'Flat no 66, Wing C',
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter flat/house details';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 20),

                      // Select nearest area
                      const Text(
                        'Select nearest KFH*',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      _buildDropdown(),
                      const SizedBox(height: 20),

                      // Landmark (optional)
                      const Text(
                        'Landmark (Optional)',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      _buildTextField(
                        controller: _landmarkController,
                        hintText: 'Near Metro Station',
                      ),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ),

            // Save Address Button
            Padding(
              padding: const EdgeInsets.all(20),
              child: SizedBox(
                width: double.infinity,
                height: 52,
                child: ElevatedButton(
                  onPressed: _saveAddress,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.green,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'Save address',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String hintText,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
        color: Colors.grey.shade50,
      ),
      child: TextFormField(
        controller: controller,
        validator: validator,
        style: const TextStyle(
          fontSize: 16,
          color: Colors.black87,
        ),
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade500,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        ),
      ),
    );
  }

  Widget _buildDropdown() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
        color: Colors.grey.shade50,
      ),
      child: DropdownButtonFormField<String>(
        value: _selectedArea,
        decoration: const InputDecoration(
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        ),
        style: const TextStyle(
          fontSize: 16,
          color: Colors.black87,
        ),
        items: _nearbyAreas.map((String area) {
          return DropdownMenuItem<String>(
            value: area,
            child: Text(area),
          );
        }).toList(),
        onChanged: (String? newValue) {
          if (newValue != null) {
            setState(() {
              _selectedArea = newValue;
            });
          }
        },
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'Please select nearest area';
          }
          return null;
        },
      ),
    );
  }

  void _saveAddress() {
    if (_formKey.currentState!.validate()) {
      final addressData = {
        'location': widget.selectedLocation,
        'fullAddress': widget.selectedAddress,
        'societyName': _societyController.text.trim(),
        'flatDetails': _flatController.text.trim(),
        'nearestArea': _selectedArea,
        'landmark': _landmarkController.text.trim(),
      };

      Navigator.of(context).pop(addressData);
      widget.onAddressSaved?.call(addressData);
    }
  }
}
