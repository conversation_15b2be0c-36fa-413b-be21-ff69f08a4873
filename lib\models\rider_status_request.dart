class RiderStatusRequest {
  final String deliveryDate;
  final int userid;
  final String remark;
  final int dcid;
  final int cdcType;
  final int status; // 1 for online, 0 for offline

  RiderStatusRequest({
    required this.deliveryDate,
    required this.userid,
    required this.remark,
    required this.dcid,
    required this.cdcType,
    required this.status,
  });

  Map<String, dynamic> toJson() {
    return {
      'deliveryDate': deliveryDate,
      'userid': userid,
      'remark': remark,
      'dcid': dcid,
      'cdcType': cdcType,
      'status': status,
    };
  }

  factory RiderStatusRequest.fromJson(Map<String, dynamic> json) {
    return RiderStatusRequest(
      deliveryDate: json['deliveryDate'] ?? '',
      userid: json['userid'] ?? 0,
      remark: json['remark'] ?? '',
      dcid: json['dcid'] ?? 0,
      cdcType: json['cdcType'] ?? 0,
      status: json['status'] ?? 0,
    );
  }

  /// Create request for going online
  factory RiderStatusRequest.goOnline({
    required int userid,
    required int dcid,
    required int cdcType,
    String? customRemark,
  }) {
    return RiderStatusRequest(
      deliveryDate: DateTime.now().toIso8601String().split('T')[0], // Current date in YYYY-MM-DD format
      userid: userid,
      remark: customRemark ?? 'Going online for delivery',
      dcid: dcid,
      cdcType: cdcType,
      status: 1,
    );
  }

  /// Create request for going offline
  factory RiderStatusRequest.goOffline({
    required int userid,
    required int dcid,
    required int cdcType,
    String? customRemark,
  }) {
    return RiderStatusRequest(
      deliveryDate: DateTime.now().toIso8601String().split('T')[0], // Current date in YYYY-MM-DD format
      userid: userid,
      remark: customRemark ?? 'Going offline',
      dcid: dcid,
      cdcType: cdcType,
      status: 0,
    );
  }

  @override
  String toString() {
    return 'RiderStatusRequest(deliveryDate: $deliveryDate, userid: $userid, remark: $remark, dcid: $dcid, cdcType: $cdcType, status: $status)';
  }
}

class RiderStatusResponse {
  final String status;
  final String msg;

  RiderStatusResponse({
    required this.status,
    required this.msg,
  });

  factory RiderStatusResponse.fromJson(Map<String, dynamic> json) {
    return RiderStatusResponse(
      status: json['status'] ?? '',
      msg: json['msg'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'msg': msg,
    };
  }

  bool get isSuccess => status == '200';

  @override
  String toString() {
    return 'RiderStatusResponse(status: $status, msg: $msg)';
  }
}
