import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/utils/responsive_utils.dart';
import 'package:kisankonnect_rider/controllers/dashboard_story_controller.dart';
import 'package:skeletonizer/skeletonizer.dart';

class BannerCarouselSection extends StatefulWidget {
  const BannerCarouselSection({super.key});

  @override
  State<BannerCarouselSection> createState() => _BannerCarouselSectionState();
}

class _BannerCarouselSectionState extends State<BannerCarouselSection> {
  final CarouselSliderController _carouselController = CarouselSliderController();
  late final DashboardStoryController _controller;
  int _currentIndex = 0;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _controller = Get.find<DashboardStoryController>();
    _listenToController();
  }

  void _listenToController() {
    // Listen to controller loading state for dynamic shimmer
    ever(_controller.isLoading, (bool isLoading) {
      if (mounted) {
        setState(() {
          _isLoading = isLoading;
        });
      }
    });

    // Set initial loading state
    _isLoading = _controller.isLoading.value;
  }

  @override
  Widget build(BuildContext context) {
    // Using ResponsiveUtils for consistent sizing
    final bannerHeight = ResponsiveUtils.containerHeight(context, ContainerHeightType.extraLarge);
    final borderRadius = ResponsiveUtils.borderRadius(context, BorderRadiusType.medium);
    final spacingBetween = ResponsiveUtils.height(context, 1.5);

    return Obx(() {
      final topBanners = _controller.topBanners;
      final itemCount = _isLoading ? 2 : topBanners.length; // Show 2 skeleton items while loading

      return Skeletonizer(
        enabled: _isLoading,
        child: Column(
          children: [
            // Carousel Slider
            CarouselSlider.builder(
              carouselController: _carouselController,
              itemCount: itemCount,
              itemBuilder: (context, index, realIndex) {
                return Container(
                  width: double.infinity,
                  margin: EdgeInsets.symmetric(horizontal: ResponsiveUtils.spacingXS(context)),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(borderRadius),
                    color: _isLoading ? Colors.grey.shade300 : null,
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.shadowLight,
                        blurRadius: ResponsiveUtils.width(context, 2),
                        offset: Offset(0, ResponsiveUtils.height(context, 0.5)),
                      ),
                    ],
                    image: _isLoading
                        ? null
                        : index < topBanners.length
                            ? DecorationImage(
                                image: NetworkImage(topBanners[index].imageURL),
                                fit: BoxFit.cover,
                              )
                            : null,
                  ),
                );
              },
              options: CarouselOptions(
                height: bannerHeight,
                autoPlay: !_isLoading && topBanners.isNotEmpty,
                autoPlayInterval: const Duration(seconds: 4),
                autoPlayAnimationDuration: const Duration(milliseconds: 800),
                autoPlayCurve: Curves.fastOutSlowIn,
                enlargeCenterPage: true,
                enlargeFactor: 0.2,
                viewportFraction: 0.9,
                aspectRatio: 16 / 9,
                initialPage: 0,
                enableInfiniteScroll: topBanners.length > 1,
                reverse: false,
                scrollDirection: Axis.horizontal,
                onPageChanged: (index, reason) {
                  setState(() {
                    _currentIndex = index;
                  });
                },
              ),
            ),

            SizedBox(height: spacingBetween),

            if (itemCount > 1)
              Padding(
                padding: const EdgeInsets.only(bottom: 20.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: _currentIndex == 0 ? AppColors.green : AppColors.green.withValues(alpha: 0.3),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: ResponsiveUtils.spacingS(context),
                        vertical: ResponsiveUtils.spacingXS(context),
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.green,
                        borderRadius:
                            BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.large)),
                      ),
                      child: Text(
                        '${_currentIndex + 1}/$itemCount',
                        style: AppTextTheme.cardCaption.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'Figtree',
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color:
                            _currentIndex == itemCount - 1 ? AppColors.green : AppColors.green.withValues(alpha: 0.3),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      );
    });
  }
}
