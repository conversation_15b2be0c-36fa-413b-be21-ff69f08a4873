import 'package:get/get.dart';
import 'package:kisankonnect_rider/models/reports_models.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/constants/storage_keys.dart';

class ReportsController extends GetxController {
  // Use ApiServiceMain instance
  final ApiService _apiService = ApiService.instance;
  late SecureStorageService _storage;

  // Observable variables
  final RxBool isLoading = false.obs;
  final RxString error = ''.obs;
  final Rx<ServiceLevelReportData?> reportData = Rx<ServiceLevelReportData?>(null);
  final RxList<RiderData> riders = <RiderData>[].obs;

  @override
  void onInit() {
    super.onInit();
    _storage = Get.find<SecureStorageService>();
    fetchReportsData();
  }

  // Get current date for API
  String get date => DateTime.now().toIso8601String().split('T')[0];

  /// Get user data from storage for API parameters
  Future<Map<String, String>> _getUserData() async {
    final dcId = await _storage.read(StorageKeys.dcId) ?? '1';
    final cdcType = await _storage.read(StorageKeys.cdcType) ?? '1';
    final riderId = await _storage.read(StorageKeys.userName) ?? '1';

    return {
      'dcId': dcId,
      'cdcType': cdcType,
      'riderId': riderId,
    };
  }

  /// Fetch KFH Service Level Report data from API
  Future<void> fetchReportsData() async {
    try {
      isLoading.value = true;
      error.value = '';
      print('🔄 Fetching reports data...');

      // Get user data from storage
      final userData = await _getUserData();

      print(
          '📊 Using API params - Date: $date, DCID: ${userData['dcId']}, CDCType: ${userData['cdcType']}, RiderID: ${userData['riderId']}');

      // Call the rider-specific service level report API
      final response = await _apiService.earnings.getRiderServiceLevelReport(
        date: date,
        dcId: userData['dcId']!,
        cdcType: userData['cdcType']!,
        riderId: userData['riderId']!,
      );

      print('📡 API Response: ${response.isSuccess ? 'Success' : 'Failed'}');
      if (response.data != null) {
        print('📊 Response data: ${response.data}');
      }

      if (response.isSuccess && response.data != null) {
        // Handle the response based on the actual API structure
        // Since this is a rider-specific report, the structure might be different
        _handleRiderReportResponse(response.data, userData);
      } else {
        error.value = response.error ?? 'Failed to load reports data';
        print('❌ API call failed: ${response.error}');
      }
    } catch (e) {
      error.value = 'Error: ${e.toString()}';
      print('💥 Exception in fetchReportsData: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// Handle rider-specific report response
  void _handleRiderReportResponse(dynamic responseData, Map<String, String> userData) {
    try {
      // Check if the response has the expected structure
      if (responseData is Map<String, dynamic>) {
        final status = responseData['status']?.toString() ?? '';
        final msg = responseData['msg']?.toString() ?? '';

        print('📋 Rider report status: $status, message: $msg');

        if (status == '200') {
          // Extract the actual report data from fE_KFHRiderServiceLevelReport
          final reportDataMap = responseData['fE_KFHRiderServiceLevelReport'] as Map<String, dynamic>?;

          if (reportDataMap != null) {
            // Create ServiceLevelReportData from the actual API response
            final apiReportData = ServiceLevelReportData(
              depoName: reportDataMap['depoName']?.toString() ?? 'Unknown Depot',
              totalOrder: _extractIntValue(reportDataMap, 'totalOrder', 0),
              delivered: _extractIntValue(reportDataMap, 'delivered', 0),
              cancelled: _extractIntValue(reportDataMap, 'cancelled', 0),
              crtReturn: _extractIntValue(reportDataMap, 'crtReturn', 0),
              crtMissing: _extractIntValue(reportDataMap, 'crtMissing', 0),
              pending: _extractIntValue(reportDataMap, 'pending', 0),
              partiallyreturn: _extractIntValue(reportDataMap, 'partiallyreturn', 0),
              partiallymissing: _extractIntValue(reportDataMap, 'partiallymissing', 0),
              nonassignorder: _extractIntValue(reportDataMap, 'nonassignorder', 0),
              ontime: _extractIntValue(reportDataMap, 'ontime', 0),
              delay: _extractIntValue(reportDataMap, 'delay', 0),
              early: _extractIntValue(reportDataMap, 'early', 0),
              thirdPartyOrder: _extractIntValue(reportDataMap, '_3PlOrder', 0),
              inprogress: _extractIntValue(reportDataMap, 'inprogress', 0),
              orderAllocated: _extractIntValue(reportDataMap, 'orderAllocated', 0),
            );

            // Create rider data from the API response
            final apiRider = RiderData(
              riderId: userData['riderId'],
              riderName: reportDataMap['riderName']?.toString() ?? 'Current Rider',
            );

            reportData.value = apiReportData;
            riders.value = [apiRider];

            print('✅ Successfully processed rider report data');
            print('📊 Depot: ${apiReportData.depoName}, Rider: ${apiRider.riderName}');
            print('📈 Orders - Total: ${apiReportData.totalOrder}, Delivered: ${apiReportData.delivered}');
            print(
                '⏰ Timing - OnTime: ${apiReportData.ontime}, Delay: ${apiReportData.delay}, Early: ${apiReportData.early}');
          } else {
            error.value = 'Report data not found in response';
            print('❌ fE_KFHRiderServiceLevelReport not found in response');
          }
        } else {
          error.value = msg.isNotEmpty ? msg : 'Failed to load rider report';
          print('❌ Rider report API returned error: $msg');
        }
      } else {
        error.value = 'Invalid response format';
        print('❌ Invalid response format from rider report API');
      }
    } catch (e) {
      error.value = 'Error processing rider report: $e';
      print('❌ Exception processing rider report: $e');
    }
  }

  /// Extract integer value from response data with fallback
  int _extractIntValue(Map<String, dynamic> data, String key, int fallback) {
    final value = data[key];
    if (value is int) return value;
    if (value is String) return int.tryParse(value) ?? fallback;
    return fallback;
  }

  /// Refresh the reports data
  Future<void> refreshData() async {
    await fetchReportsData();
  }

  /// Get report data
  ServiceLevelReportData? get currentReportData => reportData.value;

  /// Get total number of riders
  int get totalRiders => riders.length;

  /// Check if reports data is available
  bool get hasReportData => reportData.value != null;

  /// Get sample data for fallback (when API fails or no data)
  List<Map<String, dynamic>> get sampleReportsData => [
        {
          'riderName': 'Rider Name - 100092',
          'onTime': 80,
          'early': 10,
          'delay': 80,
        },
        {
          'riderName': 'Rider Name - 100093',
          'onTime': 75,
          'early': 15,
          'delay': 85,
        },
        {
          'riderName': 'Rider Name - 100094',
          'onTime': 90,
          'early': 5,
          'delay': 70,
        },
        {
          'riderName': 'Rider Name - 100095',
          'onTime': 85,
          'early': 8,
          'delay': 75,
        },
      ];

  /// Convert ServiceLevelReportData to Map for UI compatibility
  Map<String, dynamic> reportDataToMap(ServiceLevelReportData data, String riderName) {
    return {
      'riderName': riderName,
      'onTime': data.ontimePercentage.round(),
      'early': data.earlyPercentage.round(),
      'delay': data.delayPercentage.round(),
    };
  }

  /// Get reports data for UI (either from API or sample data)
  List<Map<String, dynamic>> get reportsDataForUI {
    if (hasReportData && currentReportData != null) {
      // Create a single report card from the API data
      final riderName = riders.isNotEmpty
          ? '${riders.first.riderName ?? 'Rider'} - ${riders.first.riderId ?? 'Unknown'}'
          : 'Service Report';

      return [reportDataToMap(currentReportData!, riderName)];
    } else {
      // Return sample data if no API data available
      return sampleReportsData;
    }
  }
}
