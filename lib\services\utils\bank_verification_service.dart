import 'package:flutter/foundation.dart';
import 'ifsc_verification_service.dart';

/// Service for bank account verification
class BankVerificationService {
  
  /// Verify bank account details
  /// Returns BankVerificationResult with verification status and details
  static Future<BankVerificationResult> verifyBankAccount({
    required String accountNumber,
    required String ifscCode,
    required String selectedBankName,
  }) async {
    try {
      debugPrint('🏦 Starting bank account verification...');
      debugPrint('🏦 Account: $accountNumber');
      debugPrint('🏦 IFSC: $ifscCode');
      debugPrint('🏦 Selected Bank: $selectedBankName');

      // Step 1: Verify IFSC code first
      final ifscDetails = await IFSCVerificationService.verifyIFSC(ifscCode);
      
      if (ifscDetails == null) {
        debugPrint('❌ IFSC verification failed');
        return BankVerificationResult(
          isSuccess: false,
          errorMessage: 'Invalid IFSC code',
          verifiedBankName: selectedBankName,
          accountNumber: accountNumber,
          ifscCode: ifscCode,
        );
      }

      debugPrint('✅ IFSC verified: ${ifscDetails.bank}');

      // Step 2: Validate account number format (basic validation)
      if (!_isValidAccountNumber(accountNumber)) {
        debugPrint('❌ Invalid account number format');
        return BankVerificationResult(
          isSuccess: false,
          errorMessage: 'Invalid account number format',
          verifiedBankName: ifscDetails.bank,
          accountNumber: accountNumber,
          ifscCode: ifscCode,
          ifscDetails: ifscDetails,
        );
      }

      // Step 3: Check if selected bank matches IFSC bank
      final bankMatches = _doesBankNameMatch(selectedBankName, ifscDetails.bank);
      if (!bankMatches) {
        debugPrint('⚠️ Bank name mismatch: Selected "$selectedBankName" vs IFSC "$ifscDetails.bank"');
        // This is a warning, not a failure - we'll use the IFSC bank name
      }

      // Step 4: Simulate bank verification (in real app, this would call actual bank API)
      final verificationSuccess = await _simulateBankVerification(accountNumber, ifscCode);
      
      if (verificationSuccess) {
        debugPrint('✅ Bank account verification successful');
        return BankVerificationResult(
          isSuccess: true,
          verifiedBankName: ifscDetails.bank, // Use the verified bank name from IFSC
          accountNumber: accountNumber,
          ifscCode: ifscCode,
          ifscDetails: ifscDetails,
        );
      } else {
        debugPrint('❌ Bank account verification failed');
        return BankVerificationResult(
          isSuccess: false,
          errorMessage: 'Bank account verification failed. Please check your account details.',
          verifiedBankName: ifscDetails.bank,
          accountNumber: accountNumber,
          ifscCode: ifscCode,
          ifscDetails: ifscDetails,
        );
      }

    } catch (e) {
      debugPrint('🚨 Error during bank verification: $e');
      return BankVerificationResult(
        isSuccess: false,
        errorMessage: 'Verification failed due to technical error: $e',
        verifiedBankName: selectedBankName,
        accountNumber: accountNumber,
        ifscCode: ifscCode,
      );
    }
  }

  /// Validate account number format (basic validation)
  static bool _isValidAccountNumber(String accountNumber) {
    // Remove spaces and check length (typically 9-18 digits)
    final cleanNumber = accountNumber.replaceAll(' ', '');
    return cleanNumber.length >= 9 && 
           cleanNumber.length <= 18 && 
           RegExp(r'^[0-9]+$').hasMatch(cleanNumber);
  }

  /// Check if selected bank name matches IFSC bank name
  static bool _doesBankNameMatch(String selectedBank, String ifscBank) {
    // Normalize bank names for comparison
    final normalizedSelected = selectedBank.toLowerCase().replaceAll(' ', '');
    final normalizedIfsc = ifscBank.toLowerCase().replaceAll(' ', '');
    
    // Check for exact match or partial match
    return normalizedSelected.contains(normalizedIfsc) || 
           normalizedIfsc.contains(normalizedSelected);
  }

  /// Simulate bank verification (replace with actual bank API call)
  static Future<bool> _simulateBankVerification(String accountNumber, String ifscCode) async {
    // Simulate API delay
    await Future.delayed(const Duration(seconds: 2));
    
    // For demo purposes, we'll simulate success for most cases
    // In real implementation, this would call actual bank verification API
    
    // Simulate some failure cases for testing
    if (accountNumber.startsWith('000') || accountNumber.startsWith('999')) {
      return false; // Simulate invalid account
    }
    
    return true; // Simulate successful verification
  }
}

/// Result of bank verification
class BankVerificationResult {
  final bool isSuccess;
  final String? errorMessage;
  final String verifiedBankName;
  final String accountNumber;
  final String ifscCode;
  final IFSCDetails? ifscDetails;

  BankVerificationResult({
    required this.isSuccess,
    this.errorMessage,
    required this.verifiedBankName,
    required this.accountNumber,
    required this.ifscCode,
    this.ifscDetails,
  });

  @override
  String toString() {
    return 'BankVerificationResult(isSuccess: $isSuccess, bank: $verifiedBankName, account: $accountNumber, ifsc: $ifscCode)';
  }
}
