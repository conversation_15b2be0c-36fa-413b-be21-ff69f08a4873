# Environment Configuration Guide

## Overview

The KisanKonnect Rider app uses a comprehensive environment configuration system based on `.env` files. This allows for flexible configuration management across different environments (development, production) without hardcoding values.

## 📁 Environment Files

### 1. `.env` - Base Configuration
- **Purpose**: Default/fallback configuration
- **Usage**: Contains common settings and fallback values
- **Version Control**: ✅ Committed to repository

### 2. `.env.development` - Development Environment
- **Purpose**: Development-specific configuration
- **Features**: Debug tools enabled, relaxed security, extended timeouts
- **Usage**: Automatically loaded when using `Flavor.development`
- **Version Control**: ✅ Committed to repository

### 3. `.env.production` - Production Environment
- **Purpose**: Production-specific configuration
- **Features**: Security enabled, analytics enabled, optimized settings
- **Usage**: Automatically loaded when using `Flavor.production`
- **Version Control**: ✅ Committed to repository

### 4. `.env.local` - Local Overrides (Optional)
- **Purpose**: Local developer-specific overrides
- **Usage**: Override any setting for local development
- **Version Control**: ❌ Not committed (in .gitignore)

## 🔧 Configuration Categories

### App Configuration
```env
APP_NAME=KisanKonnect Rider
APP_VERSION=1.0.0
BUNDLE_ID=com.kisankonnect.rider
```

### API Configuration
```env
API_BASE_URL=http://knet.kisankonnect.com/SRIT3O/api
API_VERSION=
SOCKET_URL=wss://socket.kisankonnect.com
REQUEST_TIMEOUT=30
MAX_RETRIES=3
```

### Feature Flags
```env
ENABLE_LOGGING=true
ENABLE_CRASHLYTICS=false
ENABLE_ANALYTICS=false
ENABLE_PERFORMANCE_MONITORING=false
ENABLE_DEVICE_PREVIEW=false
ENABLE_FLAVOR_BANNER=true
```

### Cache & Storage
```env
CACHE_TIMEOUT_MINUTES=15
DATABASE_NAME=kisankonnect_rider.db
LOG_LEVEL=info
```

### External Services
```env
IFSC_API_URL=https://ifsc.razorpay.com
TWILIO_API_URL=https://api.twilio.com/2010-04-01
MESSAGEBIRD_API_URL=https://conversations.messagebird.com/v1
GUPSHUP_API_URL=https://api.gupshup.io/sm/api/v1
```

### Security Configuration
```env
ENABLE_SSL_PINNING=false
ENABLE_CERTIFICATE_VALIDATION=true
ENABLE_NETWORK_SECURITY=true
```

### Development Tools
```env
ENABLE_DEBUG_MENU=false
ENABLE_NETWORK_INSPECTOR=false
ENABLE_PERFORMANCE_OVERLAY=false
```

## 🚀 Usage

### 1. Accessing Configuration
```dart
import 'package:kisankonnect_rider/config/env_config.dart';

// App configuration
String appName = EnvConfig.appName;
String apiUrl = EnvConfig.apiBaseUrl;

// Feature flags
bool loggingEnabled = EnvConfig.enableLogging;
bool analyticsEnabled = EnvConfig.enableAnalytics;

// Timeouts and limits
Duration requestTimeout = EnvConfig.requestTimeoutDuration;
int maxRetries = EnvConfig.maxRetries;
```

### 2. Environment Initialization
```dart
// In main files
await Environment.init(flavor: Flavor.development);
// or
await Environment.init(flavor: Flavor.production);
```

### 3. Backward Compatibility
```dart
// Still works through Environment class
String baseUrl = Environment.baseUrl;
bool loggingEnabled = Environment.enableLogging;
```

## 🔄 Environment Switching

### Development
```bash
# Uses .env.development
flutter run -t lib/main_dev.dart
```

### Production
```bash
# Uses .env.production
flutter run -t lib/main_prod.dart --release
```

### Default (Development)
```bash
# Uses .env.development
flutter run
```

## 🛠️ Local Development Setup

### 1. Create Local Override (Optional)
```bash
# Create local override file
cp .env .env.local
```

### 2. Customize Local Settings
```env
# .env.local - Your local overrides
API_BASE_URL=http://localhost:3000/api
ENABLE_DEBUG_MENU=true
ENABLE_NETWORK_INSPECTOR=true
LOG_LEVEL=debug
```

### 3. Local File Priority
1. `.env.local` (highest priority)
2. `.env.development` or `.env.production`
3. `.env` (fallback)

## 🔒 Security Best Practices

### 1. Sensitive Data
- ❌ **Never commit** API keys, passwords, or secrets
- ✅ **Use** placeholder values in committed files
- ✅ **Override** with real values in `.env.local`

### 2. Production Security
```env
# Production settings
ENABLE_SSL_PINNING=true
ENABLE_CERTIFICATE_VALIDATION=true
ENABLE_NETWORK_SECURITY=true
ENABLE_ROOT_DETECTION=true
```

### 3. Development Flexibility
```env
# Development settings
ENABLE_SSL_PINNING=false
ENABLE_CERTIFICATE_VALIDATION=false
ENABLE_DEBUG_MENU=true
```

## 🐛 Troubleshooting

### 1. Environment Not Loading
```dart
// Check if environment is initialized
if (!EnvConfig.isInitialized) {
  await EnvConfig.init(flavor: Flavor.development);
}
```

### 2. Missing Variables
```dart
// Validate required variables
bool isValid = EnvConfig.validateEnvironment();
if (!isValid) {
  print('Missing required environment variables');
}
```

### 3. Debug Environment
```dart
// Get all environment variables
Map<String, String> allVars = EnvConfig.allEnvVars;
print('Environment variables: $allVars');

// Get environment summary
Map<String, dynamic> summary = EnvConfig.environmentSummary;
print('Environment summary: $summary');
```

## 📋 Migration from Hardcoded Values

### Before
```dart
// Hardcoded values
const String apiUrl = 'http://knet.kisankonnect.com/SRIT3O/api';
const bool enableLogging = true;
```

### After
```dart
// Environment-based values
String apiUrl = EnvConfig.apiBaseUrl;
bool enableLogging = EnvConfig.enableLogging;
```

## 🎯 Benefits

1. **🔧 Flexible Configuration**: Easy to change settings per environment
2. **🔒 Security**: Sensitive data not hardcoded
3. **🚀 Easy Deployment**: Different configs for different environments
4. **🐛 Better Debugging**: Environment-specific debug settings
5. **📱 Feature Flags**: Easy enable/disable of features
6. **🔄 Backward Compatible**: Existing code continues to work

## 📚 Related Files

- `lib/config/env_config.dart` - Environment configuration service
- `lib/config/flavor_config.dart` - Flavor management (updated)
- `lib/constants/api_endpoints.dart` - API endpoints (updated)
- `.env` - Base configuration
- `.env.development` - Development configuration
- `.env.production` - Production configuration
