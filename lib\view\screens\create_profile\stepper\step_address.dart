import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:get/get.dart';
import '../../../widgets/dialogs/google_maps_location_picker.dart';
import '../../../../controllers/common_data_controller.dart';
import '../../../../controllers/profile_registration_controller.dart';
import '../../../../config/env_config.dart';

class StepAddress extends StatefulWidget {
  final Function(Map<String, dynamic>)? onContinue;
  final VoidCallback? onBack;
  const StepAddress({super.key, this.onContinue, this.onBack});

  @override
  State<StepAddress> createState() => _StepAddressState();
}

class _StepAddressState extends State<StepAddress> {
  final _searchController = TextEditingController();
  String _searchQuery = '';
  bool _isLoading = false;
  String _selectedAddress = '';
  String _selectedCity = '';
  String _selectedState = '';
  String _selectedPincode = '';
  double? _selectedLat;
  double? _selectedLng;

  // Additional address details
  String _societyName = '';
  String _flatDetails = '';
  String _nearestArea = '';
  String _landmark = '';

  // Profile registration controller
  late ProfileRegistrationController _registrationController;

  // Mock location suggestions
  final List<Map<String, String>> _locationSuggestions = [
    {
      'name': 'Thane',
      'address': 'Thane, Maharashtra, India',
      'city': 'Thane',
      'state': 'Maharashtra',
      'pincode': '400601',
    },
    {
      'name': 'Thane West',
      'address': 'Thane West, Maharashtra, India',
      'city': 'Thane',
      'state': 'Maharashtra',
      'pincode': '400602',
    },
    {
      'name': 'Thane Station',
      'address': 'Thane Railway station, Maharashtra, India',
      'city': 'Thane',
      'state': 'Maharashtra',
      'pincode': '400601',
    },
    {
      'name': 'Thane East',
      'address': 'Thane East, Maharashtra, India',
      'city': 'Thane',
      'state': 'Maharashtra',
      'pincode': '400603',
    },
  ];

  @override
  void initState() {
    super.initState();
    _validateGoogleMapsSetup();
    _initializeController();
    _loadSavedData();
  }

  /// Initialize the registration controller
  void _initializeController() {
    try {
      _registrationController = Get.find<ProfileRegistrationController>();
      debugPrint('✅ Found ProfileRegistrationController');
    } catch (e) {
      debugPrint('❌ Error finding ProfileRegistrationController: $e');
      // Create a new controller if not found (shouldn't happen in normal flow)
      _registrationController = Get.put(ProfileRegistrationController());
    }
  }

  /// Load saved data from the controller
  void _loadSavedData() {
    try {
      final profileData = _registrationController.profileData;
      debugPrint('📋 Loading saved address data from controller');

      // Load address data if available
      if (profileData.containsKey('address')) {
        setState(() {
          _selectedAddress = profileData['address'] ?? '';
          _selectedCity = profileData['city'] ?? '';
          _selectedState = profileData['state'] ?? '';
          _selectedPincode = profileData['pincode'] ?? '';

          // Parse latitude and longitude
          if (profileData.containsKey('latitude') && profileData['latitude'] != null) {
            _selectedLat = double.tryParse(profileData['latitude'].toString());
          }
          if (profileData.containsKey('longitude') && profileData['longitude'] != null) {
            _selectedLng = double.tryParse(profileData['longitude'].toString());
          }

          // Load additional address details
          _societyName = profileData['society'] ?? '';
          _flatDetails = profileData['flat'] ?? '';
          _nearestArea = profileData['area'] ?? '';
          _landmark = profileData['landmark'] ?? '';

          // Update search controller
          _searchController.text = _societyName.isNotEmpty ? _societyName : _selectedAddress;
        });

        debugPrint('✅ Loaded saved address: $_selectedAddress');
        debugPrint('✅ Loaded society: $_societyName, flat: $_flatDetails');
      } else {
        debugPrint('ℹ️ No saved address data found');
      }
    } catch (e) {
      debugPrint('❌ Error loading saved data: $e');
    }
  }

  /// Validate Google Maps API key setup
  void _validateGoogleMapsSetup() {
    final mapsService = MapsService.instance;
    debugPrint('🗺️ === Google Maps API Key Validation ===');
    debugPrint('🗺️ API Key from EnvConfig: ${EnvConfig.googleMapsApiKey.substring(0, 8)}...');
    debugPrint('🗺️ API Key from MapsService: ${mapsService.apiKey.substring(0, 8)}...');
    debugPrint('🗺️ Is API Key Valid: ${mapsService.isApiKeyValid}');
    debugPrint('🗺️ Full API Key: ${mapsService.apiKey}');
    debugPrint('🗺️ =====================================');

    if (!mapsService.isApiKeyValid) {
      debugPrint('❌ Google Maps API Key is not valid!');
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Google Maps API Key is not configured properly'),
              backgroundColor: Colors.red,
            ),
          );
        }
      });
    } else {
      debugPrint('✅ Google Maps API Key is valid');
    }
  }

  /// Test Google Maps API configuration
  void _testGoogleMapsAPI() {
    final mapsService = MapsService.instance;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Google Maps API Test'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('API Key: ${mapsService.apiKey.substring(0, 8)}...'),
            Text('Valid: ${mapsService.isApiKeyValid}'),
            const SizedBox(height: 16),
            const Text('Required APIs to enable in Google Cloud Console:'),
            const Text('• Maps SDK for Android'),
            const Text('• Places API'),
            const Text('• Geocoding API'),
            const Text('• Geolocation API'),
            const SizedBox(height: 16),
            const Text('Check API key restrictions:'),
            const Text('• Application restrictions: Android apps'),
            const Text('• Add package name: com.kisankonnect.rider.dev'),
            const Text('• Add SHA-1 fingerprint'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Save data to controller for persistence when navigating back
  void _saveDataToController(Map<String, dynamic> data) {
    try {
      // Save each field to the controller
      data.forEach((key, value) {
        _registrationController.updateProfileData(key, value);
      });

      debugPrint('✅ Address data saved to controller for persistence');
      debugPrint('📋 Saved data: ${data.keys.join(', ')}');
    } catch (e) {
      debugPrint('❌ Error saving data to controller: $e');
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FA),
      resizeToAvoidBottomInset: true,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 16),
              Text(
                'Enter your address details',
                style: AppTextTheme.cardTitle.copyWith(fontSize: 18),
              ),
              const SizedBox(height: 4),
              Text(
                'Helps you get the accessories',
                style: AppTextTheme.cardSubtitle,
              ),
              const SizedBox(height: 24),

              // Search Location Field
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: TextField(
                  controller: _searchController,
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                  decoration: InputDecoration(
                    hintText: 'Search your location',
                    hintStyle: TextStyle(color: Colors.grey.shade500),
                    prefixIcon: const Icon(Icons.search, color: Colors.grey),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Current Location Option
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppColors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.my_location,
                        color: AppColors.green,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Current location',
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                          Text(
                            'Enable your current location for better services',
                            style: TextStyle(
                              color: Colors.grey,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                    ElevatedButton(
                      onPressed: _getCurrentLocation,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.green,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2,
                              ),
                            )
                          : const Text(
                              'Enable',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Select on Map Button
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppColors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.map,
                        color: AppColors.green,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Select on map',
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                          Text(
                            'Choose your exact location on map',
                            style: TextStyle(
                              color: Colors.grey,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Column(
                      children: [
                        ElevatedButton(
                          onPressed: _openLocationPicker,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.green,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          ),
                          child: const Text(
                            'Select',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Location Suggestions
              if (_searchQuery.isNotEmpty && _selectedAddress.isEmpty)
                Expanded(
                  child: _buildLocationSuggestions(),
                ),

              // Selected Address Details
              if (_selectedAddress.isNotEmpty)
                Expanded(
                  child: _buildSelectedAddressDetails(),
                ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: _selectedAddress.isNotEmpty
          ? Container(
              padding: EdgeInsets.only(
                left: 16,
                right: 16,
                bottom: MediaQuery.of(context).viewInsets.bottom + 16,
                top: 16,
              ),
              decoration: const BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black12,
                    blurRadius: 4,
                    offset: Offset(0, -2),
                  ),
                ],
              ),
              child: SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton(
                  onPressed: _onContinue,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.green,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(32),
                    ),
                  ),
                  child: const Text(
                    'Continue',
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            )
          : null,
    );
  }

  Widget _buildLocationSuggestions() {
    List<Map<String, String>> filteredSuggestions = _locationSuggestions;

    if (_searchQuery.isNotEmpty) {
      filteredSuggestions = _locationSuggestions
          .where((location) =>
              location['name']!.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              location['address']!.toLowerCase().contains(_searchQuery.toLowerCase()))
          .toList();
    }

    return ListView.builder(
      itemCount: filteredSuggestions.length,
      itemBuilder: (context, index) {
        final location = filteredSuggestions[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: const Icon(
              Icons.location_on,
              color: AppColors.green,
            ),
            title: Text(
              location['name']!,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
            subtitle: Text(
              location['address']!,
              style: const TextStyle(
                color: Colors.grey,
                fontSize: 12,
              ),
            ),
            onTap: () => _selectLocation(location),
            tileColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      },
    );
  }

  void _selectLocation(Map<String, String> location) {
    setState(() {
      _selectedAddress = location['address']!;
      _selectedCity = location['city']!;
      _selectedState = location['state']!;
      _selectedPincode = location['pincode']!;
      _searchController.text = location['name']!;
      _searchQuery = '';
    });
  }

  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Check location permission
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('Location permissions are denied');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('Location permissions are permanently denied');
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
        ),
      );

      // Get address from coordinates
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        String address = '${place.street}, ${place.locality}, ${place.administrativeArea}, ${place.country}';

        setState(() {
          _selectedAddress = address;
          _selectedCity = place.locality ?? '';
          _selectedState = place.administrativeArea ?? '';
          _selectedPincode = place.postalCode ?? '';
          _selectedLat = position.latitude;
          _selectedLng = position.longitude;
          _searchController.text = place.locality ?? 'Current Location';
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error getting location: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _openLocationPicker() async {
    debugPrint('🗺️ Opening Google Maps Location Picker...');
    debugPrint('🗺️ API Key Status: ${MapsService.instance.isApiKeyValid}');
    debugPrint('🗺️ Full API Key: ${MapsService.instance.apiKey}');

    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('Loading Google Maps...'),
          ],
        ),
      ),
    );

    Map<String, dynamic>? locationData;

    try {
      locationData = await GoogleMapsLocationPicker.show(
        context: context,
      );

      // Close loading dialog
      if (mounted) Navigator.of(context).pop();

      debugPrint('🗺️ Location picker result: $locationData');
    } catch (e) {
      // Close loading dialog
      if (mounted) Navigator.of(context).pop();

      debugPrint('❌ Error opening location picker: $e');
      debugPrint('❌ Error type: ${e.runtimeType}');
      debugPrint('❌ Error details: ${e.toString()}');

      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Google Maps Error'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Failed to load Google Maps. This could be due to:'),
                const SizedBox(height: 8),
                const Text('1. Google Maps API key not enabled for Android'),
                const Text('2. Maps SDK for Android not enabled in Google Cloud Console'),
                const Text('3. API key restrictions'),
                const SizedBox(height: 16),
                Text('Error: ${e.toString()}'),
                const SizedBox(height: 8),
                Text('API Key: ${MapsService.instance.apiKey.substring(0, 8)}...'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
      return;
    }

    if (locationData != null) {
      setState(() {
        _selectedAddress = locationData!['address'] ?? '';
        _selectedCity = locationData['city'] ?? '';
        _selectedState = locationData['state'] ?? '';
        _selectedPincode = locationData['pincode'] ?? '';
        _selectedLat = double.tryParse(locationData['latitude'] ?? '');
        _selectedLng = double.tryParse(locationData['longitude'] ?? '');

        // Store additional address details
        _societyName = locationData['societyName'] ?? '';
        _flatDetails = locationData['flatDetails'] ?? '';
        _nearestArea = locationData['nearestArea'] ?? '';
        _landmark = locationData['landmark'] ?? '';

        // Update search controller with society name or address
        _searchController.text = _societyName.isNotEmpty ? _societyName : _selectedAddress;
      });
    }
  }

  Widget _buildSelectedAddressDetails() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.location_on,
                color: AppColors.green,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'Selected Address',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: _openLocationPicker,
                child: const Text(
                  'Change',
                  style: TextStyle(
                    color: AppColors.green,
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            _selectedAddress,
            style: const TextStyle(
              color: Colors.grey,
              fontSize: 14,
            ),
          ),
          if (_selectedCity.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              '$_selectedCity, $_selectedState - $_selectedPincode',
              style: const TextStyle(
                color: Colors.grey,
                fontSize: 12,
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _onContinue() async {
    if (_selectedAddress.isNotEmpty) {
      final data = {
        'address': _selectedAddress,
        'city': _selectedCity,
        'state': _selectedState,
        'pincode': _selectedPincode,
        'latitude': _selectedLat?.toString() ?? '',
        'longitude': _selectedLng?.toString() ?? '',
        'societyName': _societyName,
        'flatDetails': _flatDetails,
        'nearestArea': _nearestArea,
        'landmark': _landmark,
      };

      // Store coordinates in local storage for API calls
      if (_selectedLat != null && _selectedLng != null) {
        final storage = SecureStorageService.instance;
        await storage.write(StorageKeys.latitude, _selectedLat.toString());
        await storage.write(StorageKeys.longitude, _selectedLng.toString());

        debugPrint('📍 Stored coordinates: $_selectedLat, $_selectedLng');

        // Reload common data with new coordinates
        try {
          final commonDataController = Get.find<CommonDataController>();
          await commonDataController.reloadWithStoredCoordinates();
          debugPrint('✅ Common data reloaded with new coordinates');
        } catch (e) {
          debugPrint('⚠️ Could not reload common data: $e');
        }
      }

      // Save data to controller for persistence
      _saveDataToController(data);

      widget.onContinue?.call(data);
    }
  }
}
