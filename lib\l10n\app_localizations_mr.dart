// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Marathi (`mr`).
class AppLocalizationsMr extends AppLocalizations {
  AppLocalizationsMr([String locale = 'mr']) : super(locale);

  @override
  String get appName => 'किसानकनेक्ट रायडर';

  @override
  String get profile => 'प्रोफाइल';

  @override
  String get riderName => 'रायडरचे नाव';

  @override
  String get phoneNumber => '+91- 98388 89898';

  @override
  String get idCard => 'ओळखपत्र';

  @override
  String get cashBalance => 'रोख शिल्लक';

  @override
  String get myShift => 'माझी शिफ्ट';

  @override
  String get myKFHLocation => 'माझे KFH स्थान';

  @override
  String get tripHistory => 'प्रवास इतिहास';

  @override
  String get kisanStore => 'किसान स्टोअर';

  @override
  String get newTrends => 'नवीन ट्रेंड्स';

  @override
  String get referAndEarn => 'रेफर करा आणि कमवा';

  @override
  String get referralBonusText => '10,000+ रायडर रेफरल बोनस कमावत आहेत';

  @override
  String get helpAndSupport => 'मदत आणि सहाय्य';

  @override
  String get appVersion => 'अॅप आवृत्ती v0.1.10';

  @override
  String get featuredProducts => 'वैशिष्ट्यीकृत उत्पादने';

  @override
  String get bigDiscount => 'मोठी सूट';

  @override
  String get orderNow => 'आता ऑर्डर करा';

  @override
  String get cartReview => 'कार्ट पुनरावलोकन';

  @override
  String get cartDetails => 'कार्ट तपशील';

  @override
  String get wallet => 'वॉलेट';

  @override
  String get kisanKonnectWallet => 'किसानकनेक्ट वॉलेट';

  @override
  String get kisanKash => 'KisanKash';

  @override
  String get balance => 'शिल्लक';

  @override
  String get add => 'Add';

  @override
  String get redeem => 'Redeem';

  @override
  String get selectPaymentMode => 'Select your payment mode';

  @override
  String get cardsUPINetbanking => 'Cards/UPI/Netbanking';

  @override
  String get payWithUPI => 'Pay with UPI';

  @override
  String get useAnyUPIApp => 'Use any UPI app on your phone to pay';

  @override
  String get payNow => 'Pay Now';

  @override
  String get orderPlacedSuccessfully => 'Your order placed\nsuccessfully';

  @override
  String get continueShopping => 'Continue Shopping';

  @override
  String get viewMyOrders => 'View My Orders';

  @override
  String get selectAddress => 'Select Address';

  @override
  String get selectYourLocation => 'Select your location';

  @override
  String get getUpdatedOnWhatsapp => 'Get updated on Whatsapp';

  @override
  String get continueButton => 'Continue';

  @override
  String get selectPreferableFilter => 'Select your preferable filter';

  @override
  String get tripAscending => 'Trip in Ascending order';

  @override
  String get tripDescending => 'Trip in Descending order';

  @override
  String get onlySuccessTrip => 'Only Success Trip';

  @override
  String get onlyFailedTrip => 'Only Failed Trip';

  @override
  String get okay => 'Okay';

  @override
  String get myEarnings => 'माझी कमाई';

  @override
  String get today => 'आज';

  @override
  String get thisWeek => 'हा आठवडा';

  @override
  String get thisMonth => 'हा महिना';

  @override
  String get totalEarningsOfTheDay => 'दिवसाची एकूण कमाई';

  @override
  String get totalEarningsOfThePreviousDay => 'मागील दिवसाची एकूण कमाई';

  @override
  String get orderDelivered => 'ऑर्डर डिलिव्हर केले';

  @override
  String get orderEarning => 'ऑर्डर कमाई';

  @override
  String get rainSurgeEarning => 'पाऊस सर्ज कमाई';

  @override
  String get totalIncentive => 'एकूण प्रोत्साहन';

  @override
  String get incentiveSubtitle => 'शनिवार आणि रविवार काम केल्यास लागू';

  @override
  String get doMoreOrdersAndEarn => 'आणखी 15 ऑर्डर करा आणि कमवा';

  @override
  String get viewAllEarningsAndIncentives => 'सर्व कमाई आणि प्रोत्साहन पहा';

  @override
  String get orders => 'ऑर्डर';

  @override
  String get referFriendAndEarn => 'मित्राला रेफर करा आणि कमवा';

  @override
  String get yourFriendGets => 'तुमच्या मित्राला मिळते';

  @override
  String get onJoining => ' सामील होताना!';

  @override
  String get totalReferralEarnings => 'एकूण रेफरल कमाई: ₹5500';

  @override
  String get friendsReferred => '2 मित्र रेफर केले';

  @override
  String get shareYourReferralCode => 'तुमचा रेफरल कोड शेअर करा';

  @override
  String get howItWorks => 'हे कसे कार्य करते';

  @override
  String get referInSimpleSteps => '3 सोप्या पायऱ्यांमध्ये रेफर करा';

  @override
  String get copyCodeOrShareViaWhatsapp =>
      'कोड कॉपी करा किंवा व्हाट्सअॅपद्वारे शेअर करा';

  @override
  String get completeTheTarget => 'लक्ष्य पूर्ण करा';

  @override
  String get enjoyTheBonus => 'बोनसचा आनंद घ्या';

  @override
  String get yourReferrals => 'तुमचे रेफरल';

  @override
  String get pending => 'प्रलंबित';

  @override
  String get success => 'यशस्वी';

  @override
  String get failed => 'अयशस्वी';

  @override
  String get inviteViaWhatsApp => 'व्हाट्सअॅपद्वारे आमंत्रित करा';

  @override
  String get referralCodeCopied => 'रेफरल कोड क्लिपबोर्डवर कॉपी झाला!';

  @override
  String joinKisanKonnectMessage(String code) {
    return '🎉 किसानकनेक्टमध्ये सामील व्हा आणि सामील होताना ₹10,000 कमवा!\n\nमाझा रेफरल कोड वापरा: $code\n\nअॅप डाउनलोड करा आणि आजच कमवायला सुरुवात करा!';
  }
}
