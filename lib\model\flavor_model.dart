class FlavorModel {
  final String flavorName;
  final String baseUrl;
  const FlavorModel({required this.flavorName, required this.baseUrl});

  static FlavorModel? _flavor;

  factory FlavorModel.set({required String flavorName, required String baseUrl}) {
    _flavor = FlavorModel(flavorName: flavorName, baseUrl: baseUrl);
    return _flavor!;
  }

  static FlavorModel get getFlavor => _flavor!;
}
